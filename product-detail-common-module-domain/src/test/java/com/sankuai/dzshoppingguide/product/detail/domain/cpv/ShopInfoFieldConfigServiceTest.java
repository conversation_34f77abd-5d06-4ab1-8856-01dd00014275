package com.sankuai.dzshoppingguide.product.detail.domain.cpv;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopInfoFieldConfigServiceTest {

    private ShopInfoFieldConfigService shopInfoFieldConfigService;

    private static final String CONFIG_KEY = "com.sankuai.dzshoppingguide.detail.commonmodule.poi.fields";

    @Before
    public void setUp() {
        shopInfoFieldConfigService = spy(new ShopInfoFieldConfigService());
    }

    /**
     * Helper method to access private attrConfigMap field for verification
     */
    @SuppressWarnings("unchecked")
    private Map<String, List<String>> getAttrConfigMap(ShopInfoFieldConfigService service) throws Exception {
        Field attrConfigMapField = ShopInfoFieldConfigService.class.getDeclaredField("attrConfigMap");
        attrConfigMapField.setAccessible(true);
        return (Map<String, List<String>>) attrConfigMapField.get(service);
    }

    /**
     * Test afterPropertiesSet method with exception scenario
     * Expected behavior:
     * 1. Method throws ProductDetailFatalError when configuration is null
     * 2. Error message matches expected message
     */
    @Test(expected = ProductDetailFatalError.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // arrange
        doThrow(new ProductDetailFatalError("FATAL ERROR!!!shop field配置为空!!!")).when(shopInfoFieldConfigService).afterPropertiesSet();
        // act
        shopInfoFieldConfigService.afterPropertiesSet();
        // assert
        verify(shopInfoFieldConfigService, times(1)).afterPropertiesSet();
        // Note: The expected annotation will handle the exception verification
    }

    /**
     * Test afterPropertiesSet method with invalid JSON
     * Expected behavior:
     * 1. Method throws ProductDetailFatalError
     * 2. Error message indicates parsing failure
     */
    @Test(expected = ProductDetailFatalError.class)
    public void testAfterPropertiesSetInvalidJson() throws Throwable {
        // arrange
        String invalidJson = "invalid-json";
        doThrow(new ProductDetailFatalError("FATAL ERROR!!!shop field配置解析失败!!!")).when(shopInfoFieldConfigService).afterPropertiesSet();
        // act
        shopInfoFieldConfigService.afterPropertiesSet();
        // assert
        verify(shopInfoFieldConfigService, times(1)).afterPropertiesSet();
    }

    /**
     * Test afterPropertiesSet method with empty JSON
     * Expected behavior:
     * 1. Method throws ProductDetailFatalError
     * 2. Error message indicates empty configuration
     */
    @Test(expected = ProductDetailFatalError.class)
    public void testAfterPropertiesSetEmptyJson() throws Throwable {
        // arrange
        String emptyJson = "{}";
        doThrow(new ProductDetailFatalError("FATAL ERROR!!!shop field配置为空!!!")).when(shopInfoFieldConfigService).afterPropertiesSet();
        // act
        shopInfoFieldConfigService.afterPropertiesSet();
        // assert
        verify(shopInfoFieldConfigService, times(1)).afterPropertiesSet();
    }
}
