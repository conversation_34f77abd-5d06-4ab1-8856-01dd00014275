package com.sankuai.dzshoppingguide.product.detail.domain.cpv;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SkuAttrConfigServiceTest {

    @InjectMocks
    private SkuAttrConfigService skuAttrConfigService;

    @Mock
    private Map<String, Set<String>> attrConfigMap;

    /**
     * Tests getSkuAttrConfig method when attrConfigMap contains the corresponding key-value pair.
     * Verifies that the method returns the expected Set when the key exists in the map.
     */
    @Test
    public void testGetSkuAttrConfig_ContainsKey() throws Throwable {
        // arrange
        ProductTypeEnum productType = ProductTypeEnum.DEAL;
        int secondProductCategory = 1;
        String expectedKey = productType.getCode() + "-" + secondProductCategory;
        Set<String> expected = new HashSet<>();
        expected.add("test");
        when(attrConfigMap.getOrDefault(eq(expectedKey), any(HashSet.class))).thenReturn(expected);
        // act
        Set<String> actual = skuAttrConfigService.getSkuAttrConfig(productType, secondProductCategory);
        // assert
        assertNotNull(actual);
        assertEquals(expected, actual);
        assertEquals(1, actual.size());
        assertTrue(actual.contains("test"));
    }

    /**
     * Tests getSkuAttrConfig method when attrConfigMap does not contain the corresponding key-value pair.
     * Verifies that the method returns an empty Set when the key doesn't exist in the map.
     */
    @Test
    public void testGetSkuAttrConfig_NotContainsKey() throws Throwable {
        // arrange
        ProductTypeEnum productType = ProductTypeEnum.DEAL;
        int secondProductCategory = 1;
        String expectedKey = productType.getCode() + "-" + secondProductCategory;
        when(attrConfigMap.getOrDefault(eq(expectedKey), any(HashSet.class))).thenReturn(new HashSet<>());
        // act
        Set<String> actual = skuAttrConfigService.getSkuAttrConfig(productType, secondProductCategory);
        // assert
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    /**
     * Tests getSkuAttrConfig method with null ProductTypeEnum.
     * Verifies that the method handles null input appropriately.
     */
    @Test(expected = NullPointerException.class)
    public void testGetSkuAttrConfig_NullProductType() throws Throwable {
        // arrange
        ProductTypeEnum productType = null;
        int secondProductCategory = 1;
        // act
        skuAttrConfigService.getSkuAttrConfig(productType, secondProductCategory);
    }
}
