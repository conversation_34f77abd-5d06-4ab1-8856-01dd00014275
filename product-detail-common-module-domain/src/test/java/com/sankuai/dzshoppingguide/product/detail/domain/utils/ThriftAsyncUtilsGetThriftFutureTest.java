package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import java.lang.reflect.Field;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ThriftAsyncUtilsGetThriftFutureTest {

private ThreadLocal<SettableFuture> threadLocalFuture;

@Before
@SuppressWarnings("unchecked")
public void setUp() throws Exception {
Field field = ContextStore.class.getDeclaredField("threadLocalFuture");
field.setAccessible(true);
threadLocalFuture = (ThreadLocal<SettableFuture>) field.get(null);
}

@After
public void tearDown() {
if (threadLocalFuture != null) {
threadLocalFuture.remove();
}
}

/**
 * 测试正常情况 - SettableFuture成功完成
 */
@Test
public void testGetThriftFuture_WhenSuccessful() throws Throwable {
// arrange
SettableFuture<String> settableFuture = SettableFuture.create();
threadLocalFuture.set(settableFuture);
// act
CompletableFuture<String> result = ThriftAsyncUtils.getThriftFuture();
// 设置结果
settableFuture.set("success");
// assert
assertTrue(result.isDone());
assertFalse(result.isCompletedExceptionally());
assertEquals("success", result.get(100, TimeUnit.MILLISECONDS));
}

/**
 * 测试异常情况 - SettableFuture异常完成
 */
@Test
public void testGetThriftFuture_WhenFailed() throws Throwable {
// arrange
SettableFuture<String> settableFuture = SettableFuture.create();
threadLocalFuture.set(settableFuture);
RuntimeException expectedException = new RuntimeException("test exception");
// act
CompletableFuture<String> result = ThriftAsyncUtils.getThriftFuture();
// 设置异常
settableFuture.setException(expectedException);
// assert
assertTrue(result.isCompletedExceptionally());
try {
result.get(100, TimeUnit.MILLISECONDS);
fail("Should throw exception");
} catch (ExecutionException e) {
assertEquals(expectedException, e.getCause());
}
}

/**
 * 测试边界情况 - SettableFuture为null
 */
@Test(expected = NullPointerException.class)
public void testGetThriftFuture_WhenSettableFutureIsNull() throws Throwable {
// arrange
threadLocalFuture.set(null);
// act & assert
ThriftAsyncUtils.getThriftFuture();
}

/**
 * 测试CompletableFuture取消操作
 */
@Test
public void testGetThriftFuture_WhenCompletableFutureCancelled() throws Throwable {
// arrange
SettableFuture<String> settableFuture = SettableFuture.create();
threadLocalFuture.set(settableFuture);
// act
CompletableFuture<String> result = ThriftAsyncUtils.getThriftFuture();
result.cancel(true);
// assert
assertTrue("CompletableFuture should be cancelled", result.isCancelled());
assertTrue("CompletableFuture should be completed exceptionally", result.isCompletedExceptionally());
// 验证原始的SettableFuture状态
assertFalse("SettableFuture should not be affected by CompletableFuture cancellation", settableFuture.isCancelled());
}

/**
 * 测试异步回调执行
 */
@Test
public void testGetThriftFuture_AsyncCallback() throws Throwable {
// arrange
SettableFuture<String> settableFuture = SettableFuture.create();
threadLocalFuture.set(settableFuture);
final String[] result = new String[1];
// act
CompletableFuture<String> future = ThriftAsyncUtils.getThriftFuture();
future.thenAccept(value -> result[0] = value);
settableFuture.set("test value");
// assert
// 等待完成
future.get(100, TimeUnit.MILLISECONDS);
assertEquals("test value", result[0]);
}
}
