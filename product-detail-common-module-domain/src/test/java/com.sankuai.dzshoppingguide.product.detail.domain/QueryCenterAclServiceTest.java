package com.sankuai.dzshoppingguide.product.detail.domain;

import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.junit.Assert;
import org.junit.Test;


/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/11 16:07
 */
public class QueryCenterAclServiceTest {



    @Test
    public void testGetBaseRequestBuilder_MT_Deal() {
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        request.setClientType(ClientTypeEnum.MT_APP.getCode());
        QueryByDealGroupIdRequestBuilder QueryByDealGroupIdRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(request);
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.build();
        Assert.assertEquals((int) queryByDealGroupIdRequest.getIdType(), IdTypeEnum.MT.getCode());
    }

    @Test
    public void testGetBaseRequestBuilder_MT_RESERVE() {
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductType(ProductTypeEnum.RESERVE.getCode());
        request.setClientType(ClientTypeEnum.MT_APP.getCode());
        QueryByDealGroupIdRequestBuilder QueryByDealGroupIdRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(request);
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.build();
        Assert.assertEquals((int) queryByDealGroupIdRequest.getIdType(), IdTypeEnum.BIZ_PRODUCT.getCode());
    }
  
}