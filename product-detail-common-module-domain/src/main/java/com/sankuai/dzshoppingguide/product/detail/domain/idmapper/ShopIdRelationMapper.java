package com.sankuai.dzshoppingguide.product.detail.domain.idmapper;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.poi.relation.service.dto.AdvancedPoiPairDTO;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 15:13
 */
@Slf4j
@Service
public class ShopIdRelationMapper {

    @MdpThriftClient(remoteAppKey = "poi-relation-service-web", timeout = 1000, testTimeout = 5000, async = true)
    private PoiRelationService poiRelationServiceFuture;

    public CompletableFuture<Long> getDpPoiIdByMtPoiId(long mtShopId) {
        try {
            poiRelationServiceFuture.queryPoiPairByMtIdL(mtShopId);
            CompletableFuture<AdvancedPoiPairDTO> future = ThriftAsyncUtils.getThriftFuture();
            return future.exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "IdMapperFetcher")
                                .putTag("method", "getDpPoiIdByMtPoiId")
                                .message(String.format("getDpPoiIdByMtPoiId error, mtShopId: %s", JsonCodec.encode(mtShopId))));
                        return null;
                    })
                    .thenApply(result -> {
                        if (result == null) {
                            return 0L;
                        }
                        return Optional.ofNullable(result.getDpId()).orElse(0L);
                    });
        } catch (Exception e) {
            log.error("getDpPoiIdByMtPoiId error", e);
            return CompletableFuture.completedFuture(0L);
        }
    }

    public CompletableFuture<Long> getMtPoiIdByDpPoiId(long dpShopId) {
        try {
            poiRelationServiceFuture.queryPoiPairByDpIdL(dpShopId);
            CompletableFuture<AdvancedPoiPairDTO> future = ThriftAsyncUtils.getThriftFuture();
            return future.exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "IdMapperFetcher")
                                .putTag("method", "getMtPoiIdByDpPoiId")
                                .message(String.format("getMtPoiIdByDpPoiId error, dpShopId: %s", JsonCodec.encode(dpShopId))));
                        return null;
                    })
                    .thenApply(result -> {
                        if (result == null) {
                            return 0L;
                        }
                        return Optional.ofNullable(result.getMtId()).orElse(0L);
                    });
        } catch (Exception e) {
            return CompletableFuture.completedFuture(0L);
        }
    }


    // public CompletableFuture<Long> getDpPoiIdByMtPoiId(long mtShopId) {
    //     try {
    //         return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByMtIdL(mtShopId))
    //                 .exceptionally(e -> {
    //                     log.error(XMDLogFormat.build()
    //                             .putTag("scene", "IdMapperFetcher")
    //                             .putTag("method", "getDpPoiIdByMtPoiId")
    //                             .message(String.format("getDpPoiIdByMtPoiId error, mtShopId: %s", JsonCodec.encode(mtShopId))));
    //                     return null;
    //                 })
    //                 .thenApply(result -> {
    //                     if (result == null) {
    //                         return 0L;
    //                     }
    //                     return Optional.ofNullable(result.getDpId()).orElse(0L);
    //                 });
    //     } catch (Exception e) {
    //         return CompletableFuture.completedFuture(0L);
    //     }
    // }
    //
    // public CompletableFuture<Long> getMtPoiIdByDpPoiId(long dpShopId) {
    //     try {
    //         return AthenaInf.getRpcCompletableFuture(poiRelationService.queryPoiPairByDpIdL(dpShopId))
    //                 .exceptionally(e -> {
    //                     log.error(XMDLogFormat.build()
    //                             .putTag("scene", "IdMapperFetcher")
    //                             .putTag("method", "getMtPoiIdByDpPoiId")
    //                             .message(String.format("getMtPoiIdByDpPoiId error, dpShopId: %s", JsonCodec.encode(dpShopId))));
    //                     return null;
    //                 })
    //                 .thenApply(result -> {
    //                     if (result == null) {
    //                         return 0L;
    //                     }
    //                     return Optional.ofNullable(result.getMtId()).orElse(0L);
    //                 });
    //     } catch (Exception e) {
    //         return CompletableFuture.completedFuture(0L);
    //     }
    // }
}
