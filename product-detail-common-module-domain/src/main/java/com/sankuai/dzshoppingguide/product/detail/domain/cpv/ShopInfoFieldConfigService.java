package com.sankuai.dzshoppingguide.product.detail.domain.cpv;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 19:09
 */
@Service
public class ShopInfoFieldConfigService implements InitializingBean {
    private static final List<String> dpPoiFields = Lists.newArrayList("shopId", "shopType", "mainCategoryId", "cityId", "mainRegionId",
            "backMainCategoryPath");

    private static final List<String> mtPoiFields = Lists.newArrayList("dpPoiId", "mtPoiId");

    private final String SHOP_FIELD_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.poi.fields";

    private Map<String, List<String>> attrConfigMap = new ConcurrentHashMap<>();

    private void parse(String json) {
        Map<String, List<String>> map = JSONObject.parseObject(json, new TypeReference<Map<String, List<String>>>() {
        });
        if (map == null) {
            throw new ProductDetailFatalError("FATAL ERROR!!!shop field配置为空!!!");
        }
        this.attrConfigMap = map;
    }


    public List<String> getDpShopFieldConfig() {
        return attrConfigMap.getOrDefault("dp", dpPoiFields);
    }

    public List<String> getMtShopFieldConfig() {
        return attrConfigMap.getOrDefault("mt", mtPoiFields);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        parse(Lion.getString(Environment.getAppName(), SHOP_FIELD_CONFIG));
        Lion.addConfigListener(SHOP_FIELD_CONFIG, configEvent -> parse(configEvent.getValue()));
    }
}
