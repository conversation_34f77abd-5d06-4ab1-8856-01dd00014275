package com.sankuai.dzshoppingguide.product.detail.domain.idmapper;

import com.dianping.cat.Cat;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.FutureUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.RedisClientUtils;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2024/8/21 15:12
 */
@Component
@Slf4j
public class MapperCacheWrapper {
    @Autowired
    private ShopIdRelationMapper shopIdRelationMapper;

    @Autowired
    private CityIdTransformMapper cityIdMapper;

    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteAppKey = "com.sankuai.productuser.query.center",
            async = false)
    public DealGroupQueryService dealGroupQueryServiceSync;

    private static final String MAPPER_CACHE_KEY = "mapperCacheError";

    private static final String REDIS_DEAL_MAPPER_CITY_ID = "mapper_city_id";

    private static final String REDIS_DEAL_MAPPER_SHOP_ID = "mapper_shop_id";

    private static final String REDIS_DEAL_MAPPER_DEAL_ID = "mapper_deal_id";

    private static TypeReference<Integer> cityMapperCacheTypeReference = new TypeReference<Integer>() {};

    private static TypeReference<Long> shopMapperCacheTypeReference = new TypeReference<Long>() {};

    private static TypeReference<Long> dealMapperCacheTypeReference = new TypeReference<Long>() {};

    private static final String Empty_Cat_Name = "MapperCacheEmpty";

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();

    private static int CACHE_EXPIRE_TIME = LionConfigUtils.getIdMapperCacheExpiryTime();

    private static int CACHE_REFRESH_TIME = LionConfigUtils.getIdMapperCacheRefreshTime();

    static {
        ConfigRepository configRepository = Lion.getConfigRepository(LionConstants.DZTGDETAIL_APPKEY);
        configRepository.addConfigListener(LionConstants.ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG, configEvent -> {
            if (NumberUtils.isCreatable(configEvent.getValue())) {
                CACHE_EXPIRE_TIME = Integer.parseInt(configEvent.getValue());
            }
        });
        configRepository.addConfigListener(LionConstants.ID_MAPPER_CACHE_REFRESH_TIME_CONFIG, configEvent -> {
            if (NumberUtils.isCreatable(configEvent.getValue())) {
                CACHE_REFRESH_TIME = Integer.parseInt(configEvent.getValue());
            }
        });
    }
    
    public long fetchDpShopId(long mtShopId) {
        try {
            return Optional.ofNullable(getDpShopIdFromCache(mtShopId).get()).orElse(0L);
        } catch(Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "dpShopIdError");
            CompletableFuture<Long> dpPoiIdCf = shopIdRelationMapper.getDpPoiIdByMtPoiId(mtShopId);
            Long res = FutureUtils.get(dpPoiIdCf);
            return res == null ? 0 : res;
        }
    }

    public long fetchMtShopId(long dpShopId) {
        try {
            return Optional.ofNullable(getMtShopIdFromCache(dpShopId).get()).orElse(0L);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "mtShopIdError");
            CompletableFuture<Long> mtPoiIdCf = shopIdRelationMapper.getMtPoiIdByDpPoiId(dpShopId);
            Long res = FutureUtils.get(mtPoiIdCf);
            return res == null ? 0 : res;
        }
    }

    public int fetchDpCityId(int mtCityId) {
        try {
            return Optional.ofNullable(getDpCityIdFromCache(mtCityId).get()).orElse(0);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "dpCityIdError");
            CompletableFuture<Integer> dpCityIdCf = cityIdMapper.transformCityId(mtCityId, true);
            Integer dpCityId = FutureUtils.get(dpCityIdCf);
            return dpCityId == null ? 0 : dpCityId;
        }
    }

    public int fetchMtCityId(int dpCityId) {
        try {
            return Optional.ofNullable(getMtCityIdFromCache(dpCityId).get()).orElse(0);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "mtCityIdError");
            CompletableFuture<Integer> mtCityIdCf = cityIdMapper.transformCityId(dpCityId, false);
            Integer mtCityId = FutureUtils.get(mtCityIdCf);
            return mtCityId == null ? 0 : dpCityId;
        }
    }

    public long fetchDpDealId(final long mtDealGroupId) {
        try {
            return Optional.ofNullable(getDpDealIdFromCache(mtDealGroupId).get()).orElse(0L);
        }  catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "dpDealIdError");
            return getDpDealGroupId(mtDealGroupId);
        }
    }

    public long fetchMtDealId(final long dpDealGroupId) {
        try {
            return Optional.ofNullable(getMtDealIdFromCache(dpDealGroupId).get()).orElse(0L);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "mtDealIdError");
            return getMtDealGroupId(dpDealGroupId);
        }
    }

    public CompletableFuture<Long> getDpShopIdFromCache(long mtShopId) {
        if (mtShopId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "mtShopId");
            return CompletableFuture.completedFuture(0L);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_SHOP_ID, mtShopId);
        DataLoader<Long> dataLoader = key -> getDpByMtShopIdFuture(mtShopId, key);
        return cacheClient.asyncGetReadThrough(cacheKey,shopMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getDpShopIdFromCache error"));
    }

    private <T> T handleException(Throwable e, String errMsg) {
        if (e instanceof IllegalStateException) {
            return null;
        }
        log.error(errMsg, e);
        return null;
    }

    public CompletableFuture<Long> getDpByMtShopIdFuture(long mtShopId, CacheKey key) {
        CompletableFuture<Long> dpShopIdCf = shopIdRelationMapper.getDpPoiIdByMtPoiId(mtShopId);
        Long dpShopId = FutureUtils.get(dpShopIdCf);
        if(Objects.isNull(dpShopId) || dpShopId <= 0L || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "getDpByMtShopId.invalid");
            throw new IllegalStateException("dpShopId is null or equals 0");
        }
        return CompletableFuture.completedFuture(dpShopId);
    }

    public CompletableFuture<Long> getMtShopIdFromCache(long dpShopId) {
        if (dpShopId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "dpShopId");
            return CompletableFuture.completedFuture(0L);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_SHOP_ID, String.format("DP-%s", dpShopId));
        DataLoader<Long> dataLoader = key -> getShopIdByDpShopIdFuture(dpShopId, key);
        return cacheClient.asyncGetReadThrough(cacheKey, shopMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300), CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getMtShopIdFromCache error"));
    }

    public CompletableFuture<Long> getShopIdByDpShopIdFuture(long dpShopId, CacheKey key) {
        CompletableFuture<Long> mtShopIdCf = shopIdRelationMapper.getMtPoiIdByDpPoiId(dpShopId);
        Long mtShopId = FutureUtils.get(mtShopIdCf);
        if (Objects.isNull(mtShopId) || mtShopId <= 0L || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "getMtShopIdByDpShopIdLong.invalid");
            throw new IllegalStateException("mtShopId is null or equals 0");
        }
        return CompletableFuture.completedFuture(mtShopId);
    }

    public CompletableFuture<Integer> getDpCityIdFromCache(int mtCityId) {
        if (mtCityId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "mtCityId");
            return CompletableFuture.completedFuture(0);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_CITY_ID, mtCityId);
        DataLoader<Integer> dataLoader = key -> getDpCityIdByMtCityIdFuture(mtCityId, key);
        return cacheClient.asyncGetReadThrough(cacheKey,cityMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getDpCityIdFromCache error"));
    }

    public CompletableFuture<Integer> getDpCityIdByMtCityIdFuture(int mtCityId, CacheKey key) {
        CompletableFuture<Integer> dpCityIdCf = cityIdMapper.transformCityId(mtCityId, true);
        Integer dpCityId = FutureUtils.get(dpCityIdCf);
        if(Objects.isNull(dpCityId) || dpCityId <= 0 || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "fetchDpCityByMtCity.invalid");
            throw new IllegalStateException("dpCityId is null or equals 0");
        }
        return CompletableFuture.completedFuture(dpCityId);
    }

    public CompletableFuture<Integer> getMtCityIdFromCache(int dpCityId) {
        if (dpCityId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "dpCityId");
            return CompletableFuture.completedFuture(0);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_CITY_ID, String.format("DP-%s", dpCityId));
        DataLoader<Integer> dataLoader = key -> getMtCityIdByDpCityIdFuture(dpCityId, key);
        return cacheClient.asyncGetReadThrough(cacheKey, cityMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300), CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getMtCityIdFromCache error"));
    }

    public CompletableFuture<Integer> getMtCityIdByDpCityIdFuture(int dpCityId, CacheKey key) {
        CompletableFuture<Integer> mtCityIdCf = cityIdMapper.transformCityId(dpCityId, false);
        Integer mtCityId = FutureUtils.get(mtCityIdCf);
        if (Objects.isNull(mtCityId) || mtCityId <= 0 || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "fetchMtCityByDpCity.invalid");
            throw new IllegalStateException("mtCityId is null or equals 0");
        }
        return CompletableFuture.completedFuture(mtCityId);
    }

    public CompletableFuture<Long> getDpDealIdFromCache(final long mtDealGroupId) {
        if (mtDealGroupId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "mtDealGroupId");
            return CompletableFuture.completedFuture(0L);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_DEAL_ID, mtDealGroupId);
        DataLoader<Long> dataLoader = key -> getDpDealGroupIdFuture(mtDealGroupId, key);
        return cacheClient.asyncGetReadThrough(cacheKey,dealMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getDpDealIdFromCache error"));
    }

    public CompletableFuture<Long> getDpDealGroupIdFuture(long mtDealGroupId, CacheKey key) {
        long dpDealGroupId = getDpDealGroupId(mtDealGroupId);
        if(dpDealGroupId <= 0 || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "getDpDealGroupId.invalid");
            throw new IllegalStateException("dpDealGroupId is null or equals 0");
        }
        return CompletableFuture.completedFuture(dpDealGroupId);
    }

    public CompletableFuture<Long> getMtDealIdFromCache(final long dpDealGroupId) {
        if (dpDealGroupId <= 0) {
            Cat.logEvent(Empty_Cat_Name, "dpDealGroupId");
            return CompletableFuture.completedFuture(0L);
        }
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_MAPPER_DEAL_ID, String.format("DP-%s", dpDealGroupId));
        DataLoader<Long> dataLoader = key -> getMtDealGroupIdFuture(dpDealGroupId, key);
        return cacheClient.asyncGetReadThrough(cacheKey, dealMapperCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300), CACHE_REFRESH_TIME)
                .exceptionally(e -> handleException(e, "getMtDealIdFromCache error"));
    }

    private CompletableFuture<Long> getMtDealGroupIdFuture(long dpDealGroupId, CacheKey key) {
        long mtDealGroupId = getMtDealGroupId(dpDealGroupId);
        if(mtDealGroupId <= 0 || Objects.isNull(key)) {
            Cat.logEvent(Empty_Cat_Name, "getMtDealGroupId.invalid");
            throw new IllegalStateException("mtDealGroupId is null or equals 0");
        }
        return CompletableFuture.completedFuture(mtDealGroupId);
    }

    public long getDpDealGroupId(long mtDealGroupId) {
        if (mtDealGroupId <= 0) {
            return 0;
        }
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(mtDealGroupId), IdTypeEnum.MT)
                .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                .build();
        DealGroupDTO dealGroupDTO = getDealGroupDTO(queryByDealGroupIdRequest);
        if(dealGroupDTO == null || dealGroupDTO.getDpDealGroupIdInt() == null) {
            log.error("queryCenterWrapper.getDealGroupDTO empty, request = {}", JsonCodec.encodeWithUTF8(queryByDealGroupIdRequest), new IllegalArgumentException("查询中心查不到团购ID映射"));
        }
        return dealGroupDTO == null ? 0 : dealGroupDTO.getDpDealGroupId();
    }
    
    private DealGroupDTO getDealGroupDTO(QueryByDealGroupIdRequest request) {
        try {
            QueryDealGroupListResponse response = dealGroupQueryServiceSync.queryByDealGroupIds(request);
            return Optional.of(response)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()).stream()
                    .findFirst().orElse(null);
        } catch (TException e) {
            log.error("查询中心查询团单映射关系失败,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return null;
    }

    public long getMtDealGroupId(long dpDealGroupId) {
        if (dpDealGroupId <= 0) {
            return 0;
        }
        QueryByDealGroupIdRequest queryByDealGroupIdRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet( dpDealGroupId), IdTypeEnum.DP)
                .dealGroupId(DealGroupIdBuilder.builder().mtDealGroupId())
                .build();

        DealGroupDTO dealGroupDTO = getDealGroupDTO(queryByDealGroupIdRequest);
        if (dealGroupDTO == null || dealGroupDTO.getMtDealGroupIdInt() == null) {
            log.error("queryCenterWrapper.getDealGroupDTO empty, request = {}", JsonCodec.encodeWithUTF8(queryByDealGroupIdRequest), new IllegalArgumentException("查询中心查不到团购ID映射"));
        }
        return dealGroupDTO == null ? 0 : dealGroupDTO.getMtDealGroupId();
    }

}
