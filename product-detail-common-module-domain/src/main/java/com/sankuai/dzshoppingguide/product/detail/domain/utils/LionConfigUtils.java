package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import lombok.extern.slf4j.Slf4j;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 16:05
 */
@Slf4j
public class LionConfigUtils {


    /**
     * 获取id映射缓存刷新时间
     *
     * @return 刷新时间（秒）
     */
    public static int getIdMapperCacheRefreshTime() {
        return Lion.getInt(DZTGDETAIL_APPKEY, ID_MAPPER_CACHE_REFRESH_TIME_CONFIG, 0);
    }

    /**
     * 获取id映射缓存过期时间
     *
     * @return 过期时间（秒）
     */
    public static int getIdMapperCacheExpiryTime() {
        return Lion.getInt(DZTGDETAIL_APPKEY, ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG, 1296000);
    }

    public static String getOpenPlatformKey() {
        return Lion.getString(Environment.getAppName(), CITYID_TRANSFORM_OPEN_PLATFORM_KEY, "");
    }
}
