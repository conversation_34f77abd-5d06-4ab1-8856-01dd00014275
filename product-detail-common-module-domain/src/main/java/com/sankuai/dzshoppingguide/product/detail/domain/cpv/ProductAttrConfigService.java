package com.sankuai.dzshoppingguide.product.detail.domain.cpv;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash.CpvAttrConfigService;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 19:24
 */
@Service
public class ProductAttrConfigService implements InitializingBean {
    @Autowired
    private CpvAttrConfigService cpvExtraAttrConfigService;

    private final String PRODUCT_ATTR_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.product.attr.config";

    private Map<String, Set<String>> attrConfigMap = new ConcurrentHashMap<>();

    private void parse(String json) {
        Map<String, Set<String>> map = JSONObject.parseObject(json, new TypeReference<Map<String, Set<String>>>() {
        });
        if (map == null) {
            throw new ProductDetailFatalError("FATAL ERROR!!!查询中心attr配置为空!!!");
        }
        this.attrConfigMap = map;
    }

    private String buildLionKey(ProductTypeEnum productType, int secondProductCategory) {
        return String.format("%s-%s", productType.getCode(), secondProductCategory);
    }

    public Set<String> getProductAttrConfig(ProductTypeEnum productType, int secondProductCategory) {
        if (productType == null || secondProductCategory <= 0) {
            return Collections.emptySet();
        }
        
        Set<String> productExtraAttrs = cpvExtraAttrConfigService.getProductExtraAttrs(secondProductCategory);
        Set<String> productAttrConfig = attrConfigMap.getOrDefault(buildLionKey(productType, secondProductCategory), new HashSet<>());
        
        // 使用不可变集合避免外部修改
        Set<String> result = new HashSet<>(productAttrConfig);
        result.addAll(productExtraAttrs);
        return Collections.unmodifiableSet(result);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        parse(Lion.getString(Environment.getAppName(), PRODUCT_ATTR_CONFIG));
        Lion.addConfigListener(PRODUCT_ATTR_CONFIG, configEvent -> parse(configEvent.getValue()));
    }

}
