package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/9/23 10:28
 */
@Slf4j
public class RedisClientUtils {
    private static final CacheClientConfig CACHE_CONFIG = new CacheClientConfig("redis-dz-guide", "master-slave");

    private static class CacheClientHolder {
        private static final CacheClient INSTANCE;

        static {
            try {
                INSTANCE = AthenaInf.getCacheClient(CACHE_CONFIG);
            } catch (Exception e) {
                throw new IllegalStateException("init redisCacheClient error", e);
            }
        }
    }

    public static CacheClient getRedisCacheClient() {
        return CacheClientHolder.INSTANCE;
    }
}
