package com.sankuai.dzshoppingguide.product.detail.domain.utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
public class FutureUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(FutureUtils.class);

    public static <T> T get(CompletableFuture<T> future) {
        try {
            if (future == null) {
                return null;
            }
            return future.get(1000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            LOGGER.error("FutureUtilsGetError", e);
            return null;
        }
    }
}