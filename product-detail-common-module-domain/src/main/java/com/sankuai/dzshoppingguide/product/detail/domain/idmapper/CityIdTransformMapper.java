package com.sankuai.dzshoppingguide.product.detail.domain.idmapper;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.transformcitytype.DpInfo;
import com.sankuai.map.open.platform.api.transformcitytype.MtFrontInfo;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeRequest;
import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/12 10:25
 */
@Service
@Slf4j
public class CityIdTransformMapper {
    @MdpThriftClient(
            timeout = 500, testTimeout = 5000,
            remoteAppKey = "com.sankuai.apigw.map.facadecenter",
            async = true
    )
    private MapOpenApiService.AsyncIface mapOpenApiService;

    public CompletableFuture<Integer> transformCityId(int cityId, boolean mt) {
        TransformCityTypeRequest cityIdTransFormRequest = mt ? buildFromMt2DpTransformCityRequest(cityId) : buildFromDp2MtTransformCityRequest(cityId);
        try {
            OctoThriftCallback<MapOpenApiService.AsyncClient.transformcitytype_call, TransformCityTypeResponse> thriftCallback = new OctoThriftCallback<>();
            mapOpenApiService.transformcitytype(cityIdTransFormRequest,thriftCallback);
            CompletableFuture<TransformCityTypeResponse> cityIdTransResultCf = ThriftAsyncUtils.getThriftFuture(thriftCallback);
            return cityIdTransResultCf.thenApply(res -> mt ? Optional.ofNullable(res).map(TransformCityTypeResponse::getDp_info).map(DpInfo::getDp_city_id).map(Integer::parseInt).orElse(0) :
                    Optional.ofNullable(res).map(TransformCityTypeResponse::getMt_front_info).map(MtFrontInfo::getMt_front_city_id).map(Integer::parseInt).orElse(0));
        } catch (Exception e) {
            log.error("mapOpenApiService.transformcitytype fail,cityId:{},platform:{}", cityId,mt ? "mt" : "dp",e);
        }
        return CompletableFuture.completedFuture(0);
    }

    /**
     * 已知美团前台cityId,转换点评前台cityId
     * @return
     */
    private TransformCityTypeRequest buildFromMt2DpTransformCityRequest(int cityId) {
        TransformCityTypeRequest transformCityTypeRequest = new TransformCityTypeRequest();
        String openPlatformKey = LionConfigUtils.getOpenPlatformKey();
        transformCityTypeRequest.setKey(openPlatformKey);
        transformCityTypeRequest.setSource_city_type("mt_front");
        transformCityTypeRequest.setTarget_city_type("dp");
        transformCityTypeRequest.setMt_front_city_id(String.valueOf(cityId));
        return transformCityTypeRequest;
    }

    /**
     * 已知点评前台cityId,转换美团前台cityId
     * @param cityId
     * @return
     */
    private TransformCityTypeRequest buildFromDp2MtTransformCityRequest(int cityId) {
        TransformCityTypeRequest transformCityTypeRequest = new TransformCityTypeRequest();
        String openPlatformKey = LionConfigUtils.getOpenPlatformKey();
        transformCityTypeRequest.setKey(openPlatformKey);
        transformCityTypeRequest.setSource_city_type("dp");
        transformCityTypeRequest.setTarget_city_type("mt_front");
        transformCityTypeRequest.setDp_city_id(String.valueOf(cityId));
        return transformCityTypeRequest;
    }
}
