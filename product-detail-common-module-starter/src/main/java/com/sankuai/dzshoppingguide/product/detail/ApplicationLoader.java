package com.sankuai.dzshoppingguide.product.detail;

import com.dianping.rhino.spring.RhinoConfiguration;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = {"com.sankuai.dzshoppingguide"}
)
@ImportResource(locations = {"classpath*:spring/appcontext-core.xml"})
@Import({
        SpringContextUtils.class,
//        LowCodeBeanConfiguration.class,//商品详情页配置化框架启动器
//        ModuleArrangeFrameworkConfiguration.class,//商品详情页模块编排框架启动器
})
@RhinoConfiguration
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }

}


