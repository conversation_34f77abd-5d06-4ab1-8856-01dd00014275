<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:athena-client="http://www.sankuai.com/schema/athena-client"

       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
    http://code.dianping.com/schema/pigeon http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd
    http://www.sankuai.com/schema/athena-client http://www.sankuai.com/schema/athena-client-1.0.xsd">

    <athena-client:annotation-driven/>
</beans>
