package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.ComponentFactory;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BuffetFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SnackFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.TeaFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.MassageVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;

/**
 * <AUTHOR>
 * @date 2025-04-29
 * @desc
 */
@Component
public class FreeMealStyle implements Style<MassageVO> {

    @Resource
    private ComponentFactory componentFactory;

    // 初始化时会先初始化字段,然后再执行依赖注入,这个地方直接调用factory.createComponent()会报错
    // private final CommonComponent<MassageVO> commonComponent = componentFactory.createComponent();

    private CommonComponent0<MassageVO> commonComponent;

    @PostConstruct
    public void init() {
        commonComponent = componentFactory.createComponent();
    }

    @Override
    public List<MassageVO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        ProductServiceProject serviceProject = Optional.ofNullable(context).map(DealDetailBuildContext::getProductServiceProject).orElse(null);
        if (Objects.isNull(productAttr) || Objects.isNull(serviceProject)) {
            return Collections.emptyList();
        }
        String freeFoodType = productAttr.getSkuAttrFirstValue("freeFood");
        if (StringUtils.isBlank(freeFoodType)) {
            return Collections.emptyList();
        }
        ServiceProjectDTO firstMustSkuModel = DealAttrHelper.getFirstMustSkuFromServiceProject(serviceProject.getServiceProject());
        if (Objects.isNull(firstMustSkuModel)) {
            return null;
        }
        List<MassageVO> details = Lists.newArrayList();
        // 1. 构建标题
        details.addAll(commonComponent.build(MassageVO.builder()
                .title(freeFoodType)
                .detail("免费")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .build()));

        // 2. 构建餐食内容
        switch (freeFoodType) {
            case "自助餐畅吃":
                details.addAll(buildBuffetItems(firstMustSkuModel));
                break;
            case "小吃简餐畅吃":
                details.addAll(buildSnackItems(firstMustSkuModel));
                break;
            case "茶点水果":
                details.addAll(buildTeaItems(firstMustSkuModel));
                break;
        }

        return details;
    }

    /**
     * 构造自助餐模块
     *
     * @param firstMustSkuModel 第一个必选sku
     * @return 自助餐模块列表
     */
    private List<MassageVO> buildBuffetItems(ServiceProjectDTO firstMustSkuModel) {
        List<MassageVO> buffetItems = Lists.newArrayList();
        for (BuffetFoodEnum foodTypeEnum : BuffetFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodTypeEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                MassageVO buffetItem = buildFreeMealItem(foodTypeEnum.getFoodName(), foodValue, foodTypeEnum.getNewFoodIcon());
                buffetItems.add(buffetItem);
            }
        }
        return buffetItems;
    }

    /**
     * 构造小吃模块
     *
     * @param firstMustSkuModel 第一个必选sku
     * @return 小吃模块列表
     */
    private List<MassageVO> buildSnackItems(ServiceProjectDTO firstMustSkuModel) {
        List<MassageVO> snackItems = new ArrayList<>();
        for (SnackFoodEnum foodEnum : SnackFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                MassageVO snackItem = buildFreeMealItem(foodEnum.getFoodName(), foodValue, foodEnum.getNewFoodIcon());
                snackItems.add(snackItem);
            }
        }
        return snackItems;
    }

    private List<MassageVO> buildTeaItems(ServiceProjectDTO firstMustSkuModel) {
        List<MassageVO> teaItems = Lists.newArrayList();
        for (TeaFoodEnum foodEnum : TeaFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                MassageVO teaItem = buildFreeMealItem(foodEnum.getFoodName(), foodValue, foodEnum.getNewFoodIcon());
                teaItems.add(teaItem);
            }
        }
        return teaItems;
    }

    private MassageVO buildFreeMealItem(String title, String content, String icon) {
        return MassageVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                .title(title)
                .content(content)
                .icon(icon)
                .build();
    }
}
