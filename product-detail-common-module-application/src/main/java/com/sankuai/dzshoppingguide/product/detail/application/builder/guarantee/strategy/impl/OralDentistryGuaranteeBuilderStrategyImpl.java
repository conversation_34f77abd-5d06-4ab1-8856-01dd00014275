package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.tag.ShopTagUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 口腔齿科保障标签构造策略
 */
public class OralDentistryGuaranteeBuilderStrategyImpl extends BaseGuaranteeBuilderStrategy implements GuaranteeBuilderStrategy {
    /**
     * 放心种植标签
     */
    private static final Set<Integer> SAFE_IMPLANT_TAGS = Sets.newHashSet(
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_3_YEARS_GUARANTEE.getCode(),
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_5_YEARS_GUARANTEE.getCode());

    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.ORAL_DENTISTRY;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        List<GuaranteeInstructionsContentVO> result = Lists.newArrayList();
        // 放心种植牙
        if (hasSafeImplantTag(param.getProductCategory(), param.getProductGuaranteeTagInfo())) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
            Icon icon = new Icon();
            icon.setIcon("https://p0.meituan.net/ingee/cb9a1d8726bb251839b0ee5fa20bac4b2351.png");
            icon.setIconHeight(12);
            icon.setIconWidth(61);
            content.setPrefixIcon(icon);
            content.setFontColor("038880");
            result.add(content);
        }
        // 安心补牙
        ProductGuaranteeTagInfo productGuaranteeTag = param.getProductGuaranteeTagInfo();
        if (ShopTagUtils.isSafeDenture(param.getShopDisplayTagList(), productGuaranteeTag.getSafeMedicalGuaranteeInfo())) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO("免费复种・正品保障・种植体质保・免费复查");
            content.setFontColor("#038880");
            Icon icon = new Icon();
            icon.setIcon("https://p1.meituan.net/travelcube/1dfac41cc35d1f21fc8d6a8d1345308515690.png");
            content.setPrefixIcon(icon);
            result.add(content);
        }
        List<GuaranteeInstructionsContentVO> guaranteeInstructionsContents = buildContents(param);
        if (CollectionUtils.isNotEmpty(guaranteeInstructionsContents)) {
            result.addAll(guaranteeInstructionsContents);
        }
//        return result;
        return null;
    }

    private boolean hasSafeImplantTag(ProductCategory productCategory, ProductGuaranteeTagInfo guaranteeTagInfo) {
        // 类目匹配
        if (!LionConfigUtils.showSafeImplantTag(productCategory)) {
            return false;
        }
        // 标签匹配
        return Objects.nonNull(guaranteeTagInfo.getSafeImplantTagDTO()) && SAFE_IMPLANT_TAGS.contains(guaranteeTagInfo.getSafeImplantTagDTO().getCode());
    }
}
