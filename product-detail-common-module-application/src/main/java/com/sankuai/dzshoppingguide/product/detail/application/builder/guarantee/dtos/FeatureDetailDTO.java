package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos;

import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import lombok.Data;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2023/10/24
 */
@Data
public class FeatureDetailDTO {

    /**
     * 特征标识ID
     */
    private Long id;

    /**
     * 特征名
     */
    private String text;

    /**
     * icon
     */
    private String icon;

    /**
     * 类型，0-纯文本，1-高亮
     */
    private int type;

    /**
     * 不同样式对应的值，比如高亮下发对应颜色值
     */
    private String style;

    /**
     * 浮层配置信息
     */
    private GuaranteeInstructionsBarLayerVO layerConfig;

    /**
     * 履约落地页跳转ID
     */
    private Long guaranteeJumpId;
}
