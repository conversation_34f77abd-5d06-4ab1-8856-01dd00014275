package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto;

import com.sankuai.dz.product.detail.gateway.spi.enums.ProductModelTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/2/6 17:13
 */
@Getter
public class ProductIdDTO {

    /**
     * 商品类型，团购、预订、预付等
     */
    private final ProductTypeEnum productType;
    /**
     * 商品模型类型，团购or泛商品
     */
    private final ProductModelTypeEnum productModelType;
    /**
     * 点评商品id
     */
    private final long dpProductId;
    /**
     * 美团商品id
     */
    private final long mtProductId;

    public ProductIdDTO(final ProductTypeEnum productType,
                        final long dpProductId,
                        final long mtProductId) {
        this.productType = productType;
        this.productModelType = productType.getProductModelType();
        this.dpProductId = dpProductId;
        this.mtProductId = mtProductId;
    }

}
