package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ProductIntroductionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductOriginDetail extends FetcherReturnValueDTO {
    private ProductIntroductionDTO productIntroduction;
}
