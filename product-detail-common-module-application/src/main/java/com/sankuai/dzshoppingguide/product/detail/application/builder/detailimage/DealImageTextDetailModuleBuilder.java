package com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage.enums.ImageTextContentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.ProductOriginDetail;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.ProductOriginDetailFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ContentItem;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ImageItem;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ImageListContent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ProductIntroductionDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.RichTextContent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.HtmlUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailContent;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @desc 预订图文详情模块
 */
@Slf4j
@Builder(
        moduleKey = ModuleKeyConstants.DETAIL_IMAGE,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductOriginDetailFetcher.class
        }
)
public class DealImageTextDetailModuleBuilder extends BaseVariableBuilder<ImageTextDetailVO> {
    @Override
    public ImageTextDetailVO doBuild() {
        ProductOriginDetail productOriginDetail = getDependencyResult(ProductOriginDetailFetcher.class);
        return buildImageTextDetailVO(productOriginDetail.getProductIntroduction());
    }

    private ImageTextDetailVO buildImageTextDetailVO(ProductIntroductionDTO productIntroduction) {
        if (Objects.isNull(productIntroduction) || CollectionUtils.isEmpty(productIntroduction.getContent())) {
            return null;
        }
        List<ImageTextDetailContent> contents = productIntroduction.getContent()
                .stream()
                .map(this::buildImageTextDetailContent)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        ImageTextDetailVO imageTextDetailVO = new ImageTextDetailVO();
        imageTextDetailVO.setContents(contents);
        imageTextDetailVO.setFold(false);
        imageTextDetailVO.setFoldThreshold(2);
        imageTextDetailVO.setTitle("图文详情");
        return imageTextDetailVO;
    }

    private ImageTextDetailContent buildImageTextDetailContent(ContentItem contentItem) {
        if (Objects.isNull(contentItem)) {
            return null;
        }
        if (contentItem instanceof RichTextContent) {
            return new ImageTextDetailContent(ImageTextContentTypeEnum.TEXT.getType(),
                    HtmlUtils.html2text(((RichTextContent) contentItem).getData()));
        }
        if (contentItem instanceof ImageListContent) {
            ImageListContent imageItems = (ImageListContent) contentItem;
            for (ImageItem imageItem : imageItems.getData()) {
                return new ImageTextDetailContent(ImageTextContentTypeEnum.IMAGE.getType(), imageItem.getPath());
            }
        }
        return null;
    }
}
