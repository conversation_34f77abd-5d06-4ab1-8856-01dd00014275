package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.highlights.vo.HighlightsModuleVO;

/**
 * <AUTHOR>
 * @date 2025-05-06
 * @desc
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.HIGHLIGHTS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class
        }
)
public class DefaultHighlightModuleBuilder extends BaseVariableBuilder<HighlightsModuleVO> {
    @Override
    public HighlightsModuleVO doBuild() {
        return null;
    }
}
