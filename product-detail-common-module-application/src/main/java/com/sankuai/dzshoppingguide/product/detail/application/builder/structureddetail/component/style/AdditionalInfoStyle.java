package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 补充说明信息
 * <AUTHOR>
 * @date 2025/5/6 16:23
 */
@Component
@Slf4j
public class AdditionalInfoStyle implements Style<DealDetailStructuredDetailVO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        if (context == null) {
            return Collections.emptyList();
        }
        AdditionInfoResult additionInfo = context.getAdditionInfo();
        String additionInfoStr = Optional.ofNullable(additionInfo).map(AdditionInfoResult::getAdditionInfo).orElse(StringUtils.EMPTY);
        String desc = FootMassageUtils.getDesc(additionInfoStr);
        if (StringUtils.isBlank(desc)) {
            return Collections.emptyList();
        }

        return Lists.newArrayList(
            DealDetailStructuredDetailVO.builder().title("补充说明").type(ViewComponentTypeEnum.DETAIL_TYPE_14.getType()).build(),
            DealDetailStructuredDetailVO.builder().content(desc).type(ViewComponentTypeEnum.DETAIL_TYPE_13.getType()).build()
        );
    }
}
