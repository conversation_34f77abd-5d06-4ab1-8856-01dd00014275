package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 11:31
 * @Description: TODO
 */
@Data
@Builder
@TypeDoc(description = "换行")
// TODO
@MobileDo(id = 0x178)
public class NewLineVO implements Serializable {

    @FieldDoc(description = "对应前端展示类型")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;
}
