package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;

@Log4j
public final class PictureUrlUtils {
    /**
     * 生成图片中心图片url
     *
     * @param rawUrl 团片原始url  形如：/pc/2b6b694886ec7024e50780569e21b2ee
     * @param width  图片宽度
     * @param height 图片高度
     * @return
     */
    public static String getUrl(String rawUrl, int width, int height, boolean cutMode) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.PictureUrlUtils.getUrl(java.lang.String,int,int,boolean)");
        if (StringUtils.isEmpty(rawUrl)) {
            return StringUtils.EMPTY;
        }
        String biz = "pc";
        int scale = cutMode ? 1 : 0;
        int crop = 1;
        //这里的参数的含义见  http://ugc.dp/piccenter/picdisplay.html
        PictureVisitParams params = new PictureVisitParams(biz, rawUrl, scale, crop, width, height, WaterMark.DIANPING);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        return generateUrl(rawUrl, pictureUrlGenerator);
    }

    public static String generateUrl(String raw, PictureUrlGenerator pictureUrlGenerator) {
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            log.debug(e);
        }
        return pictureUrlGenerator.getFullPictureURL();
    }
}
