package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.CategoryInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/15 17:16
 */
public abstract class AbstractSubModuleBuildStrategy {

    public abstract String getSubModuleName();

    /**
     * 判断是否命中
     */
    public abstract boolean isHit(DealDetailBuildContext context);

    /**
     * 执行构建逻辑
     */
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        CategoryInfo categoryInfo = context.getCategoryInfo();
        String categoryName = Optional.of(categoryInfo)
                .map(CategoryInfo::getCategoryName)
                .orElse("");
        // 添加标题
        // 最终结果
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        DealDetailStructuredDetailVO titleVO = DealDetailStructuredUtils.buildTitle(categoryName);
        List<DealDetailStructuredDetailVO> contents = doBuild(context);
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        DealDetailStructuredDetailVO contentVO = DealDetailStructuredUtils.buildContentFromAttr(JSON.toJSONString(contents));
        result.add(titleVO);
        result.add(contentVO);

        // 增加分隔符
        DealDetailStructuredDetailVO separator = DealDetailStructuredUtils.buildLimiter();
        result.add(separator);
        return result;
    }

    protected abstract List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context);
}
