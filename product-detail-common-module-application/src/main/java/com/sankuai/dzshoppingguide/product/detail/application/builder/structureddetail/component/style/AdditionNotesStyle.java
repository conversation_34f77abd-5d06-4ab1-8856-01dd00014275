package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.ComponentFactory;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.ChessCardRoomVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 14:23
 * @Description: 二级类目: 棋牌室 补充说明
 */
@Component
public class AdditionNotesStyle implements Style<ChessCardRoomVO> {

    private static final String ADDITION_NOTES = "sys_textArea";

    @Resource
    private ComponentFactory componentFactory;

    // 初始化时会先初始化字段,然后再执行依赖注入,这个地方直接调用factory.createComponent()会报错
    // private final CommonComponent<ChessCardRoomVO> commonComponent = componentFactory.createComponent();
    private CommonComponent0<ChessCardRoomVO> commonComponent;

    @PostConstruct
    public void init() {
        commonComponent = componentFactory.createComponent();
    }

    @Override
    public List<ChessCardRoomVO> build(DealDetailBuildContext context) {
        List<ChessCardRoomVO> additionNotesStyle = Lists.newArrayList();
        // 构建标题
        additionNotesStyle.addAll(commonComponent.build(ChessCardRoomVO.builder()
                .title("补充说明")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType())
                .build()));
        // 构建内容
        ProductAttr productAttr = context.getProductAttr();
        String additionContent = productAttr.getSkuAttrFirstValue(ADDITION_NOTES);
        additionNotesStyle.addAll(commonComponent.build(ChessCardRoomVO.builder()
                .content(additionContent)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType())
                .build()));
        return additionNotesStyle;
    }
}
