package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealreserve;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 15:30
 */
@Fetcher(
        previousLayerDependencies = {DealGroupIdMapperFetcher.class}
)
@Slf4j
public class DealOnlineReserveFetcher extends NormalFetcherContext<DealOnlineReserveResult> {
    
    @Autowired
    private CompositeAtomService compositeAtomService;
    
    @Override
    protected CompletableFuture<DealOnlineReserveResult> doFetch() {
        DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        long dpDealGroupId = Optional.ofNullable(dealGroupIdMapper).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);
        if (dpDealGroupId <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.queryDealGroupOnlineReserve(dpDealGroupId).thenApply(res -> {
            if (res == null) {
                return new DealOnlineReserveResult(false);
            }
            return new DealOnlineReserveResult(res);
        });
    }
}
