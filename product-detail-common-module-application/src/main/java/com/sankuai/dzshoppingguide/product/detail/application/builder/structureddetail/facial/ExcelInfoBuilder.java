package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.CustomizedMethodEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.EyesAttrUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.ThirdCategoryIdConstant.*;

/**
 * 表格信息
 */
@Component
public class ExcelInfoBuilder extends AbstractDealDetailBuilder {

    // 眼镜：近视配镜、儿童配镜、仅镜片、老花眼镜、太阳眼镜、仅镜框、隐形眼镜
    // 眼科：儿童普通眼镜、医学配镜、离焦镜、OK镜、离焦软镜、RGP镜、其他接触镜
    private static final List<Integer> EXCEL_THIRD_CATEGORY_ID = Lists.newArrayList(
            MYOPIA_GLASSES,
            CHILD_GLASSES,
            LENS_ONLY,
            PRESBYOPIA_GLASSES,
            CONTACT_LENS,
            SUNGLASSES,
            FRAME_ONLY,
            CHILD_REGULAR_GLASSES,
            MEDICAL_GLASSES,
            DEFOCUS_GLASSES,
            OK_GLASSES,
            DEFOCUS_SOFT_LENS,
            RGP_LENS,
            OTHER_CONTACT_LENS
    );

    // 度数/折射率说明入口及浮层
    private static final List<Integer> EXCEL_EXPLANATION_THIRD_CATEGORY_ID = Lists.newArrayList(
            MYOPIA_GLASSES,
            CHILD_GLASSES,
            PRESBYOPIA_GLASSES
    );

    @Override
    public List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        int thirdCategoryId = Optional.ofNullable(context.getProductCategory()).map(ProductCategory::getProductThirdCategoryId).orElse(0);
        if (!EXCEL_THIRD_CATEGORY_ID.contains(thirdCategoryId)) {
            return null;
        }

        // 2.表格
        List<DealDetailStructuredDetailVO> excelList = getExcel(context);
        List<DealDetailStructuredDetailVO> excelListResult = excelList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(excelListResult)) {
            result.addAll(excelListResult);
        }

        // 3.表格下方补充信息
        List<DealDetailStructuredDetailVO> excelExplanation = getExcelExplanation(context).stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(excelExplanation)) {
            result.addAll(excelExplanation);
        }

        // 判空
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }

        // 如果是近镜片 + 只有镜片表单
        if (onlyLens(thirdCategoryId, excelList)) {
            result.add(0, getModuleTitle(context.getCategoryInfo().getCategoryName(), thirdCategoryId));
        } else {
            // 1.模块标题
            result.add(0, getModuleTitle(context.getCategoryInfo().getCategoryName()));
        }

        // 4.分隔符
        result.add(DealDetailStructuredUtils.buildLimiter());
        return result;
    }

    private List<DealDetailStructuredDetailVO> getExcel(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> excelList = Lists.newArrayList();
        List<DealDetailStructuredDetailVO> glassList = Lists.newArrayList();
        List<DealDetailStructuredDetailVO> frameList = Lists.newArrayList();

        // 眼镜
        if (context.getProductCategory().getProductSecondCategoryId() == SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId()) {
            // 镜片
            glassList = buildSingleExcel(context.getProductAttr(), Lists.newArrayList(
                    new ItemConfig("品牌", "lens_brand", "", 1, null),
                    new ItemConfig("型号", "lens_model", "", 1, null),
                    new ItemConfig("折射率", "refractivity", "", 0, null),
                    new ItemConfig("功能", "lens_function", "", 0, null),
                    new ItemConfig("技术", "lens_technology2", "、", 0, null),
                    new ItemConfig("适用度数", "", "", 0, CustomizedMethodEnum.EyeDegreeRanges),
                    new ItemConfig("佩戴周期", "Wearcycle", "", 0, null),
                    new ItemConfig("镜片类型", "LensType5", "", 0, null),
                    new ItemConfig("规格", "Specificationsnumberofpieces2", "", 0, null),
                    new ItemConfig("材质", "lens_material", "或", 0, null)
            ));
            // 镜框
            frameList = buildSingleExcel(context.getProductAttr(), Lists.newArrayList(
                    new ItemConfig("品牌", "", "" ,1, CustomizedMethodEnum.EyeglassFrameInfo),
                    new ItemConfig("材质", "frame_material", " / " ,0, null),
                    new ItemConfig("款式", "frame_style", " / " ,0, null)
            ));
        }
        // 眼科
        else if (context.getProductCategory().getProductSecondCategoryId() == SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId()) {
            glassList = buildSingleExcel(context.getProductAttr(), Lists.newArrayList(
                    new ItemConfig("品牌", "", "", 1, CustomizedMethodEnum.LensSelectionInfo),
                    new ItemConfig("型号", "lens_model", "", 1, null),
                    new ItemConfig("折射率", "refractivity", "", 0, null),
                    new ItemConfig("功能", "lens_function", "", 0, null),
                    new ItemConfig("技术", "lens_technology", "", 0, null),
                    new ItemConfig("适用度数", "", "", 0, CustomizedMethodEnum.EyeDegreeRanges)
            ));
            // 镜框
            frameList = buildSingleExcel(context.getProductAttr(), Lists.newArrayList(
                    new ItemConfig("品牌", "", "" ,1, CustomizedMethodEnum.EyeglassFrameSelection)
            ));
        }
        if (CollectionUtils.isNotEmpty(glassList) && CollectionUtils.isNotEmpty(frameList)) {
            int thirdCategoryId = Optional.ofNullable(context.getProductCategory()).map(ProductCategory::getProductThirdCategoryId).orElse(0);
            glassList.add(0, getExcelTitle("镜片", thirdCategoryId));
            frameList.add(0, getExcelTitle("镜框"));
        }
        excelList.addAll(glassList);
        excelList.addAll(frameList);
        return excelList;
    }

    private List<DealDetailStructuredDetailVO> getExcelExplanation(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        if (context.getProductCategory().getProductSecondCategoryId() == SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId()) {
            result.add(buildExplanationItem("验光操作", getOptometrist(productAttr),
                    EyesAttrUtils.buildOptometristBubblePopupData(productAttr, "optometrist2", context.getAvailableShopCount())));
            result.add(buildExplanationItem("适用人群", getApplicablePopulation(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("取镜时间", getAcquireTime(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("售后质保", getSalesAssure(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("防控保障", getPreventionAndControl(productAttr), StringUtils.EMPTY));
        } else if (context.getProductCategory().getProductSecondCategoryId() == SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId()) {
            result.add(buildExplanationItem("验光操作", getOptometryStaffInfo(productAttr),
                    EyesAttrUtils.buildOptometristBubblePopupData(productAttr, "OperatorsSelection", context.getAvailableShopCount())));
            result.add(buildExplanationItem("适用人群", getApplicablePopulation(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("取镜时间", getAcquireTime(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("售后质保", getSalesAssure(productAttr), StringUtils.EMPTY));
            result.add(buildExplanationItem("防控保障", getPreventionAndControl(productAttr), StringUtils.EMPTY));
        }
        return result;
    }

    private DealDetailStructuredDetailVO buildExplanationItem(String title, String value, String popupData) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return  DealDetailStructuredDetailVO.builder()
                .title(title)
                .content(value)
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .popupData(popupData)
                .titleFontWeight("600")
                .build();
    }

    private String getOptometrist(ProductAttr productAttr) {
        List<String> optometrist2 = productAttr.getSkuAttrValue("optometrist2");
        if (CollectionUtils.isEmpty(optometrist2)) {
            return "";
        }
        String optometristExperience = getFirstAttr(productAttr, "optometrist_experience");
        if (optometrist2.size() == 1) {
            if (StringUtils.isNotBlank(optometristExperience)) {
                return String.format("%s（从业%s）", optometrist2.get(0), optometristExperience);
            }
            return optometrist2.get(0);
        }
        // 以“/”区隔展示optometrist2数组中的内容+“可选”
        return String.join(" / ", optometrist2) + "可选";
    }

    private String getApplicablePopulation(ProductAttr productAttr) {
        String population = getFirstAttr(productAttr, "applicable_population");
        String ageMin = getFirstAttr(productAttr, "applicable_age_min");
        String ageMax = getFirstAttr(productAttr, "applicable_age_max");

        if (StringUtils.isBlank(population) && StringUtils.isBlank(ageMin)) {
            return "";
        }
        if ("各年龄段均适用".equals(population)) {
            return population;
        }
        if (StringUtils.isAnyBlank(ageMin, ageMax)) {
            return "";
        }
        return String.format("%s-%s岁", ageMin, ageMax);
    }

    private String getAcquireTime(ProductAttr productAttr) {
        String acquireTime = getFirstAttr(productAttr, "acquire_time");
        String specifyDays = getFirstAttr(productAttr, "specify_days");
        // 指定天数内可取
        String specifyDays2 = getFirstAttr(productAttr, "specify_days2");
        String specifyDaysRangeMinValue = getFirstAttr(productAttr, "SpecifyDaysRangeMinValue");
        String specifyDaysRangeMaxValue = getFirstAttr(productAttr, "SpecifyDaysRangeMaxValue");
        if (StringUtils.isBlank(acquireTime)) {
            return "";
        }
        if ("立等可取".equals(acquireTime)) {
            return acquireTime;
        }
        if ("指定天数后可取".equals(acquireTime) && StringUtils.isNotBlank(specifyDays)) {
            return String.format("%s天后可取", specifyDays);
        }
        if ("指定天数内可取".equals(acquireTime) && StringUtils.isNotBlank(specifyDays2)) {
            return String.format("%s天内可取", specifyDays2);
        }
        if ("指定天数范围可取".equals(acquireTime) && StringUtils.isNotBlank(specifyDaysRangeMinValue) && StringUtils.isNotBlank(specifyDaysRangeMaxValue)) {
            return String.format("%s-%s天可取", specifyDaysRangeMinValue, specifyDaysRangeMaxValue);
        }
        return "";
    }

    private String getSalesAssure(ProductAttr productAttr) {
        String lensAssure = getFirstAttr(productAttr, "lens_assure");
        String frameWarranty = getFirstAttr(productAttr, "frame_warranty");
        String productWarranty = getFirstAttr(productAttr, "product_warranty");
        // 镜片质保单位
        String lensAssuranceUnit = getFirstAttr(productAttr, "LensQualityAssuranceUnit");
        // 镜框质保单位
        String frameAssuranceUnit = getFirstAttr(productAttr, "FrameQualityUnit");
        // 产品质保单位
        String productAssuranceUnit = getFirstAttr(productAttr, "ProductQualityAssuranceUnit");
        // 产品质保
        if (StringUtils.isBlank(productAssuranceUnit)) {
            productAssuranceUnit = "年";
        }
        if (StringUtils.isNotBlank(productWarranty)) {
            return String.format("%s%s质保", productWarranty, productAssuranceUnit);
        }
        // 镜片质保单位
        if (StringUtils.isBlank(lensAssuranceUnit)) {
            lensAssuranceUnit = "年";
        }
        List<String> result = Lists.newArrayList();
        if (StringUtils.isNotBlank(lensAssure)) {
            result.add(String.format("镜片%s%s质保", lensAssure, lensAssuranceUnit));
        }
        if (StringUtils.isBlank(frameAssuranceUnit)) {
            frameAssuranceUnit = "年";
        }
        if (StringUtils.isNotBlank(frameWarranty)) {
            result.add(String.format("镜框%s%s质保", frameWarranty, frameAssuranceUnit));
        }
        if (CollectionUtils.isEmpty(result)) {
            return "";
        }
        return String.join("，", result);
    }

    private String getPreventionAndControl(ProductAttr productAttr) {
        String duration = getFirstAttr(productAttr, "PreventionAndControlDuration");
        String measure = getFirstAttr(productAttr, "PreventionAndControlMeasure");

        if (StringUtils.isBlank(duration) || StringUtils.isBlank(measure)) {
            return "";
        }

        return String.format("%s个月内防控失效可%s", duration, measure);
    }

    private String getEyeglassFrameInfo(ProductAttr productAttr) {
        String eyeglassFrame = getFirstAttr(productAttr, "eyeglass_frame");
        if (StringUtils.isBlank(eyeglassFrame)) {
            return "";
        }

        // 处理指定价格/款式范围任选的情况
        if ("指定价格/款式范围任选".equals(eyeglassFrame)) {
            String priceRange = getFirstAttr(productAttr, "availablePriceRange");
            String frameStyle = getFirstAttr(productAttr, "frame_available_style");
            
            if (StringUtils.isNotBlank(priceRange) && StringUtils.isNotBlank(frameStyle)) {
                return String.format("%s元内%s款镜框任选", priceRange, frameStyle);
            }
            return "";
        }

        // 处理指定品牌的情况
        if ("指定品牌".equals(eyeglassFrame)) {
            List<String> brands = getAttr(productAttr, "eyeglass_brand");
            if (CollectionUtils.isEmpty(brands)) {
                return "";
            }

            // 单个品牌直接返回
            if (brands.size() == 1) {
                return brands.get(0);
            }

            // 多个品牌，展示"品牌1、品牌2、品牌3 N选1"的格式
            return String.format("%s %d选1", String.join("、", brands), brands.size());
        }

        return "";
    }

    private String getLensSelectionInfo(ProductAttr productAttr) {
        String lensSelection = getFirstAttr(productAttr, "LensSelection");
        if (StringUtils.isBlank(lensSelection)) {
            return "";
        }

        // 处理指定价格范围内任选的情况
        if ("指定价格范围内任选".equals(lensSelection)) {
            String lensPrice = getFirstAttr(productAttr, "LensPrice");
            if (StringUtils.isNotBlank(lensPrice)) {
                return String.format("%s元以内任选", lensPrice);
            }
            return "";
        }

        // 处理指定品牌的情况
        if ("指定品牌".equals(lensSelection)) {
            List<String> brands = getAttr(productAttr, "lens_brand");
            if (CollectionUtils.isEmpty(brands)) {
                return "";
            }

            // 单个品牌直接返回
            if (brands.size() == 1) {
                return brands.get(0);
            }

            // 多个品牌，展示"品牌1、品牌2、品牌3 N选1"的格式
            return String.format("%s %d选1", String.join("、", brands), brands.size());
        }

        return "";
    }

    /**
     * 获取眼科度数范围信息
     */
    private String getEyeDegreeRanges(ProductAttr productAttr) {
        List<String> applicableDegrees = getAttr(productAttr, "applicable_degrees");
        if (CollectionUtils.isEmpty(applicableDegrees)) {
            return "";
        }

        List<String> result = Lists.newArrayList();

        // 处理近视度数
        if (applicableDegrees.contains("近视")) {
            String minDiameter = getFirstAttr(productAttr, "ApplicableMinDiameter");
            String maxValue = getFirstAttr(productAttr, "ApplicableMyopiaMaxValue");

            if (StringUtils.isBlank(minDiameter) || "0".equals(minDiameter.trim())) {
                if (StringUtils.isNotBlank(maxValue)) {
                    result.add(String.format("近视%s度以下", maxValue));
                }
            } else if (StringUtils.isBlank(maxValue)) {
                result.add(String.format("近视%s度以上", minDiameter));
            } else {
                result.add(String.format("近视%s-%s度", minDiameter, maxValue));
            }
        }

        // 处理散光度数
        if (applicableDegrees.contains("散光")) {
            String minValue = getFirstAttr(productAttr, "ApplicableMinimumAstigmatism");
            String maxValue = getFirstAttr(productAttr, "ApplicableMaximumAstigmatism");

            if (StringUtils.isBlank(minValue) || "0".equals(minValue.trim())) {
                if (StringUtils.isNotBlank(maxValue)) {
                    result.add(String.format("散光%s度以下", maxValue));
                }
            } else if (StringUtils.isBlank(maxValue)) {
                result.add(String.format("散光%s度以上", minValue));
            } else {
                result.add(String.format("散光%s-%s度", minValue, maxValue));
            }
        }

        // 处理远视度数
        if (applicableDegrees.contains("远视")) {
            String minValue = getFirstAttr(productAttr, "ApplicableHyperopicMinimumValue");
            String maxValue = getFirstAttr(productAttr, "ApplicableHyperopicMaximumValue");

            if (StringUtils.isBlank(minValue) || "0".equals(minValue.trim())) {
                if (StringUtils.isNotBlank(maxValue)) {
                    result.add(String.format("远视%s度以下", maxValue));
                }
            } else if (StringUtils.isBlank(maxValue)) {
                result.add(String.format("远视%s度以上", minValue));
            } else {
                result.add(String.format("远视%s-%s度", minValue, maxValue));
            }
        }

        return CollectionUtils.isEmpty(result) ? "" : String.join("、", result);
    }

    /**
     * 获取眼镜框选择信息
     */
    private String getEyeglassFrameSelection(ProductAttr productAttr) {
        String eyeglassFrame = getFirstAttr(productAttr, "eyeglass_frame");
        if (StringUtils.isBlank(eyeglassFrame)) {
            return "";
        }

        // 处理指定价格范围内任选的情况
        if ("指定价格范围内任选".equals(eyeglassFrame)) {
            String priceRange = getFirstAttr(productAttr, "availablePriceRange");
            if (StringUtils.isNotBlank(priceRange)) {
                return String.format("店内镜框%s元内任选", priceRange);
            }
            return "";
        }

        // 处理指定品牌的情况
        if ("指定品牌".equals(eyeglassFrame)) {
            String brandName = getFirstAttr(productAttr, "MirrorFrameBrandName");
            if (StringUtils.isNotBlank(brandName)) {
                return brandName;
            }
        }

        return "";
    }

    /**
     * 获取验光人员信息
     */
    private String getOptometryStaffInfo(ProductAttr productAttr) {
        return EyesAttrUtils.handleMultiValueAttr(productAttr, "OperatorsSelection", "/", "可选");
    }

    private List<DealDetailStructuredDetailVO> buildSingleExcel(ProductAttr productAttr, List<ItemConfig> items) {
        if (CollectionUtils.isEmpty(items)) {
            return Lists.newArrayList();
        }
        List<DealDetailStructuredDetailVO> detailStructuredDetailVOS = Lists.newArrayList();

        // 2.表格内容
        List<DealDetailStructuredDetailVO> itemList = items.stream().map(i -> getSingleItem(i, productAttr)).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return Lists.newArrayList();
        }
        detailStructuredDetailVOS.add(getExcelContent(itemList));

        return detailStructuredDetailVOS;
    }

    private DealDetailStructuredDetailVO getModuleTitle(String moduleName) {
        return DealDetailStructuredDetailVO.builder()
                .title(moduleName)
                .type(ViewComponentTypeEnum.TITLE.getType())
                .build();
    }

    private DealDetailStructuredDetailVO getModuleTitle(String moduleName, int thirdCategoryId) {
        DealDetailStructuredDetailVO glassesPopUpData = getGlassesPopUpData();
        if (Objects.isNull(glassesPopUpData)) {
            return DealDetailStructuredDetailVO.builder()
                    .title(moduleName)
                    .type(ViewComponentTypeEnum.TITLE.getType())
                    .build();
        }
        return DealDetailStructuredDetailVO.builder()
                .title(moduleName)
                .type(ViewComponentTypeEnum.TITLE.getType())
                .detail(glassesPopUpData.getDetail())
                .popupData(glassesPopUpData.getPopupData())
                .build();
    }

    private DealDetailStructuredDetailVO getExcelTitle(String excelName) {
        return DealDetailStructuredDetailVO.builder()
                .title(excelName)
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .build();
    }

    private DealDetailStructuredDetailVO getExcelTitle(String excelName, int thirdCategoryId) {
        DealDetailStructuredDetailVO glassesPopUpData = getGlassesPopUpData();
        if (!EXCEL_EXPLANATION_THIRD_CATEGORY_ID.contains(thirdCategoryId) || Objects.isNull(glassesPopUpData)) {
            return DealDetailStructuredDetailVO.builder()
                    .title(excelName)
                    .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                    .titleFontWeight("600")
                    .build();
        }

        return DealDetailStructuredDetailVO.builder()
                .title(excelName)
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .detail(glassesPopUpData.getDetail())
                .popupData(glassesPopUpData.getPopupData())
                .build();
    }

    private DealDetailStructuredDetailVO getGlassesPopUpData() {
        Map<String, String> glassesMaterialMap = LionConfigUtils.getGlassesMaterialMap();
        String explanation = glassesMaterialMap.get("glassesPowerAndRefractiveIndex");
        DealDetailStructuredDetailVO floatLayer = DealDetailStructuredDetailVO.builder()
                .icon(explanation)
                .build();
        String content = JSON.toJSONString(Collections.singletonList(floatLayer));
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder result = DealDetailStructuredDetailVO.builder();
        String popUpData = JSON.toJSONString(result.title("常见度数与镜片折射率的关系")
                .type(ViewComponentTypeEnum.IMAGE_POPUP.getType())
                .content(content).build());
        return DealDetailStructuredDetailVO.builder()
                .detail("常见度数与镜片折射率的关系")
                .popupData(popUpData)
                .build();
    }

    private boolean onlyLens(int thirdCategoryId, List<DealDetailStructuredDetailVO> excelList) {
        if (CollectionUtils.isEmpty(excelList)) {
            return false;
        }
        boolean hasLensTitle = excelList.stream().anyMatch(item -> Objects.equals(item.getTitle(), "镜片"));
        return Objects.equals(thirdCategoryId, LENS_ONLY) && !hasLensTitle;
    }

    private DealDetailStructuredDetailVO getExcelContent(List<DealDetailStructuredDetailVO> itemList) {
        return DealDetailStructuredDetailVO.builder()
                .content(JSON.toJSONString(itemList))
                .type(ViewComponentTypeEnum.EXCEL.getType())
                .build();
    }

    private DealDetailStructuredDetailVO getSingleItem(ItemConfig itemConfig, ProductAttr productAttr) {
        // 如果配置了自定义方法，则执行自定义方法
        if (itemConfig.getCustomizedMethodEnum() != null) {
            String customValue = executeCustomizedMethod(itemConfig.getCustomizedMethodEnum(), productAttr);
            if (StringUtils.isNotBlank(customValue)) {
                return DealDetailStructuredDetailVO.builder()
                        .title(itemConfig.getTitle())
                        .content(customValue)
                        .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                        .titleFontWeight("600")
                        .build();
            }
            return null;
        }

        // 原有逻辑
        List<String> values = getAttr(productAttr, itemConfig.getKey());
        if (CollectionUtils.isEmpty(values) || StringUtils.isBlank(values.get(0))) {
            return null;
        }
        return DealDetailStructuredDetailVO.builder()
                .title(itemConfig.getTitle())
                .content(getShowValue(values, itemConfig))
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .build();
    }

    /**
     * 执行自定义方法
     * @param methodEnum 方法枚举
     * @param productAttr 商品属性
     * @return 执行结果
     */
    private String executeCustomizedMethod(CustomizedMethodEnum methodEnum, ProductAttr productAttr) {
        if (methodEnum == null) {
            return "";
        }

        switch (methodEnum) {
//            case GlassApplicableDegrees:
//                return getGlassApplicableDegrees(productAttr);
            case EyeglassFrameInfo:
                return getEyeglassFrameInfo(productAttr);
            case EyeglassFrameSelection:
                return getEyeglassFrameSelection(productAttr);
            case LensSelectionInfo:
                return getLensSelectionInfo(productAttr);
            case EyeDegreeRanges:
                return getEyeDegreeRanges(productAttr);
            default:
                return "";
        }
    }

    @Data
    @AllArgsConstructor
    private static class ItemConfig {
        // 表格标题文本
        private String title;
        // 字段key
        private String key;
        // 分隔符
        private String delimiter;
        // 1-代表N选1，
        private int suffixType = 0;
        // 方法名
        private CustomizedMethodEnum customizedMethodEnum;
    }

    private String getShowValue(List<String> values, ItemConfig itemConfig) {
        if (values.size() == 1) {
            return values.get(0);
        }
        return String.format("%s%s", buildMultiValue(values, itemConfig), buildSuffix(values, itemConfig));
    }

    private String buildMultiValue(List<String> values, ItemConfig itemConfig) {
        return StringUtils.join(values, StringUtils.isBlank(itemConfig.getDelimiter()) ? "、" : itemConfig.getDelimiter());
    }

    private String buildSuffix(List<String> values, ItemConfig itemConfig) {
        switch (itemConfig.getSuffixType()) {
            case 1: return String.format("%d选1", values.size());
            default: return "";
        }
    }

    private List<String> getAttr(ProductAttr productAttr, String key) {
        return Optional.ofNullable(productAttr.getSkuAttrValue(key)).orElse(Lists.newArrayList());
    }

    private String getFirstAttr(ProductAttr productAttr, String key) {
        List<String> attrs = getAttr(productAttr, key);
        return CollectionUtils.isEmpty(attrs) ? "" : attrs.get(0);
    }

}
