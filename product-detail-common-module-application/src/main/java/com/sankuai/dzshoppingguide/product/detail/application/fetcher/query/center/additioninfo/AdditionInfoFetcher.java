package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo;

import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.OriginDetailBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 20:08
 */
@ComponentFetcher(aggregateFetcher = QueryCenterAggregateFetcher.class)
@Slf4j
public class AdditionInfoFetcher extends
        ComponentFetcherContext<QueryByDealGroupIdRequestBuilder, QueryCenterAggregateReturnValue, AdditionInfoResult> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder.originDetail(OriginDetailBuilder.builder().all());
    }

    @Override
    protected FetcherResponse<AdditionInfoResult>
            mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Map<String, String> originDetailMap = Optional.ofNullable(aggregateResult).map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO).map(DealGroupDTO::getOriginDetails)
                .orElse(Maps.newHashMap());
        if (MapUtils.isEmpty(originDetailMap)) {
            return FetcherResponse.succeed(null);
        }
        String detailInfo = originDetailMap.getOrDefault("detailInfo", StringUtils.EMPTY);
        return FetcherResponse.succeed(new AdditionInfoResult(detailInfo));
    }
}
