package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve;

import com.dianping.gm.marketing.member.card.api.dto.DiscountCardShelfDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/2/6 15:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveDiscountCard extends FetcherReturnValueDTO {

    private DiscountCardShelfDTO discountCard;

}
