package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.base.BOStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.DescriptionComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class AdditionalInfoBOStyle implements BOStyle<ComponentBO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<ComponentBO> build(DealDetailBuildContext context) {
        if (context == null) {
            return Collections.emptyList();
        }
        AdditionInfoResult additionInfo = context.getAdditionInfo();
        String additionInfoStr = Optional.ofNullable(additionInfo).map(AdditionInfoResult::getAdditionInfo).orElse(StringUtils.EMPTY);
        String desc = FootMassageUtils.getDesc(additionInfoStr);
        if (StringUtils.isBlank(desc)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(
                DescriptionComponentBO.builder().description("补充说明").build(),
                DescriptionComponentBO.builder().description(desc).build()
        );
    }
}
