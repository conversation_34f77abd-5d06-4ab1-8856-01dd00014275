package com.sankuai.dzshoppingguide.product.detail.application.utils;


import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
public class DealCtxHelper {


//    public static boolean isOdpSource(DealCtx ctx) {
//        return RequestSourceEnum.ODP.getSource().equals(ctx.getRequestSource());
//    }
//
//    public static boolean isCubeExhibitNail(DealCtx ctx) {
//        return RequestSourceEnum.CUBE_EXHIBIT_NAIL.getSource().equals(ctx.getRequestSource());
//    }

    public static boolean isPreOrderDeal(DealCtx ctx) {
        if (Objects.isNull(ctx)) {
            return false;
        }
//        EnvCtx envCtx = ctx.getEnvCtx();
        // 当前仅作用于美团APP、点评APP
        // 标识和二级类目决定是否为强预订团单
//        return ctx.getIsApp() && RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(ctx.getRequestSource()) && LionConfigUtils.hitPreOrderDeal(ctx);
        return ctx.getIsApp() && RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(ctx.getRequestSource());
    }
}
