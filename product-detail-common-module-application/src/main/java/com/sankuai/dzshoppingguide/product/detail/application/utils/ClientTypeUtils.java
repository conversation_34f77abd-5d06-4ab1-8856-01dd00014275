package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Set;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/27 20:36
 */
public class ClientTypeUtils {
    private static final Set<ClientTypeEnum> miniClientEnums = Sets.newHashSet(ClientTypeEnum.DP_XCX,
            ClientTypeEnum.DP_BAIDU_MAP_XCX, ClientTypeEnum.MT_XCX, ClientTypeEnum.MT_ZJ_XCX,
            ClientTypeEnum.MT_WWJKZ_XCX, ClientTypeEnum.MT_KUAI_SHOU_XCX, ClientTypeEnum.MT_MAO_YAN_XCX);


    public static boolean isMiniProgram(ClientTypeEnum clientTypeEnum) {
        return miniClientEnums.contains(clientTypeEnum);
    }

    private static final Set<ClientTypeEnum> mtClientEnums = Sets.newHashSet(ClientTypeEnum.MT_APP, ClientTypeEnum.MT_XCX);
    public static boolean isMtMainOrMiniClient(ClientTypeEnum clientTypeEnum) {
        return mtClientEnums.contains(clientTypeEnum);
    }

    private static final Set<ClientTypeEnum> dpClientEnums = Sets.newHashSet(ClientTypeEnum.DP_APP, ClientTypeEnum.DP_XCX);
    public static boolean isDpMainOrMiniClient(ClientTypeEnum clientTypeEnum) {
        return dpClientEnums.contains(clientTypeEnum);
    }

    public static boolean isAndroid(String osType) {
        if ( StringUtils.isBlank(osType)) {
            return false;
        }
        return osType.equalsIgnoreCase("android");
    }

    public static boolean isIos(String osType) {
        if ( StringUtils.isBlank(osType)) {
            return false;
        }
        return osType.equalsIgnoreCase("ios");
    }

    public static boolean isPureHarmony(String userAgent) {
        if (org.apache.commons.lang.StringUtils.isBlank(userAgent)) {
            return false;
        }
        return userAgent.toLowerCase(Locale.ROOT).contains("openharmony");
    }
}
