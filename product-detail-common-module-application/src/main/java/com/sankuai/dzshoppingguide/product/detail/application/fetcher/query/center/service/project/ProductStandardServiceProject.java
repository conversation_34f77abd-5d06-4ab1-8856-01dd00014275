package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper.getComplexAttrValue;
import static com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper.getSimpleAttrValue;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/29 00:44
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductStandardServiceProject extends FetcherReturnValueDTO {

    /**
     * 新团详标准化服务项目信息
     */
    private final StandardServiceProjectDTO standardServiceProject;

    public ProductStandardServiceProject(StandardServiceProjectDTO standardServiceProject) {
        this.standardServiceProject = standardServiceProject;
    }

    // ==================== 服务项目处理方法 ====================

    /**
     * 业务强相关, 用于将ProductStandardServiceProject.getValue()方法的返回值List<String>转换为String
     * 连接列表为字符串，使用指定分隔符
     */
    public static String joinListByDelimiter(List<String> list, String delimiter) {
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return String.join(delimiter, list);
    }

    /**
     * 与业务强相关, 用于将ProductStandardServiceProject.getValue()方法的返回值List<String>转换为String
     * 连接列表为字符串，默认无分隔符
     */
    public static String joinListByDelimiter(List<String> list) {
        return joinListByDelimiter(list, "");
    }

    /**
     * 获取属性值并连接为字符串
     *
     * @param attr    标准属性
     * @param attrKey 属性名
     * @return 连接后的字符串，如果属性不存在则返回空字符串
     */
    public static String getValue(StandardAttributeDTO attr, String attrKey) {
        List<String> values = getSimpleAttrValue(attr, attrKey);
        return CollectionUtils.isNotEmpty(values) ? joinListByDelimiter(values, "") : StringUtils.EMPTY;
    }

    /**
     * 获取属性值并使用指定分隔符连接
     *
     * @param attr      标准属性
     * @param attrKey   属性名
     * @param separator 分隔符
     * @return 连接后的字符串，如果属性不存在则返回空字符串
     */
    public static String getValue(StandardAttributeDTO attr, String attrKey, String separator) {
        List<String> values = getSimpleAttrValue(attr, attrKey);
        return CollectionUtils.isNotEmpty(values) ? joinListByDelimiter(values, separator) : StringUtils.EMPTY;
    }

    // ==================== 属性处理基础方法 ====================

    /**
     * 获取属性值并添加后缀
     *
     * @param attr    标准属性
     * @param attrKey 属性名
     * @param suffix  后缀
     * @return 添加后缀的字符串，如果属性不存在则返回空字符串
     */
    public static String getValueWithSuffix(StandardAttributeDTO attr, String attrKey, String suffix) {
        String value = getValue(attr, attrKey);
        return StringUtils.isNotBlank(value) ? value + suffix : StringUtils.EMPTY;
    }

    /**
     * 获取属性值并添加前缀
     *
     * @param attr    标准属性
     * @param attrKey 属性名
     * @param prefix  前缀
     * @return 添加前缀的字符串，如果属性不存在则返回空字符串
     */
    public static String getValueWithPrefix(StandardAttributeDTO attr, String attrKey, String prefix) {
        String value = getValue(attr, attrKey);
        return StringUtils.isNotBlank(value) ? prefix + value : StringUtils.EMPTY;
    }

    /**
     * 获取时间范围
     *
     * @param attr         标准属性
     * @param startTimeKey 开始时间属性名
     * @param endTimeKey   结束时间属性名
     * @return 时间范围字符串，格式为"开始时间-结束时间"，如果属性不存在则返回空字符串
     */
    public static String getTimeRange(StandardAttributeDTO attr, String startTimeKey, String endTimeKey) {
        String startTime = getValue(attr, startTimeKey);
        String endTime = getValue(attr, endTimeKey);
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            return startTime + "-" + endTime;
        }
        return StringUtils.EMPTY;
    }

    /**
     * 处理复杂类型属性
     *
     * @param attr         标准属性
     * @param attrKey      属性名
     * @param processor    处理函数
     * @param defaultValue 默认值
     * @return 处理结果，如果属性不存在则返回默认值
     */
    public static <T> T processComplexValue(StandardAttributeDTO attr, String attrKey, Function<List<StandardAttributeDTO>, T> processor, T defaultValue) {
        List<StandardAttributeDTO> complexValues = getComplexAttrValue(attr, attrKey);
        if (CollectionUtils.isNotEmpty(complexValues)) {
            return processor.apply(complexValues);
        }
        return defaultValue;
    }

    /**
     * 获取复杂类型属性的所有值
     *
     * @param attr    标准属性
     * @param attrKey 属性名
     * @return 所有复杂类型值，如果属性不存在则返回null
     */
    public static List<StandardAttributeDTO> getAllComplexValues(StandardAttributeDTO attr, String attrKey) {
        return getComplexAttrValue(attr, attrKey);
    }

    public List<StandardServiceProjectGroupDTO> getSameCPVObjectIdFromOptionGroups(Long cpvObjectId, StandardServiceProjectDTO standardServiceProject) {
        if (Objects.isNull(cpvObjectId) || Objects.isNull(standardServiceProject)) {
            return Lists.newArrayList();
        }

        List<StandardServiceProjectGroupDTO> optionalGroups = standardServiceProject.getOptionalGroups();
        if (CollectionUtils.isEmpty(optionalGroups)) {
            return Lists.newArrayList();
        }
        List<StandardServiceProjectGroupDTO> result = Lists.newArrayList();
        for (StandardServiceProjectGroupDTO group : optionalGroups) {
            if (Objects.isNull(group) || CollectionUtils.isEmpty(group.getServiceProjectItems())) {
                continue;
            }

            Integer optionalCount = group.getOptionalCount();
            if (optionalCount == 0) {
                // 全部可享，筛选匹配的项
                List<StandardServiceProjectItemDTO> matchedItems = group.getServiceProjectItems().stream()
                        .filter(Objects::nonNull)
                        .filter(item -> {
                            if (Objects.isNull(item.getStandardAttribute()) || Objects.isNull(item.getStandardAttribute().getCpvObjectId())) {
                                return false;
                            }
                            return cpvObjectId.equals(item.getStandardAttribute().getCpvObjectId());
                        })
                        .collect(Collectors.toList());

                if (!matchedItems.isEmpty()) {
                    // 创建新的组，只包含匹配的项
                    StandardServiceProjectGroupDTO newGroup = new StandardServiceProjectGroupDTO();
                    newGroup.setOptionalCount(group.getOptionalCount());
                    newGroup.setServiceProjectItems(matchedItems);
                    result.add(newGroup);
                }
            } else {
                // M选N，使用allMatch逻辑
                boolean allMatch = group.getServiceProjectItems().stream()
                        .filter(Objects::nonNull)
                        .allMatch(item -> {
                            if (Objects.isNull(item.getStandardAttribute()) || Objects.isNull(item.getStandardAttribute().getCpvObjectId())) {
                                return false;
                            }
                            return cpvObjectId.equals(item.getStandardAttribute().getCpvObjectId());
                        });

                if (allMatch) {
                    result.add(group);
                }
            }
        }
        return result;
    }

    public List<StandardServiceProjectGroupDTO> getSameCPVObjectIdFromMustGroups(Long cpvObjectId, StandardServiceProjectDTO standardServiceProject) {
        if (Objects.isNull(cpvObjectId) || Objects.isNull(standardServiceProject)) {
            return Lists.newArrayList();
        }

        List<StandardServiceProjectGroupDTO> mustGroups = standardServiceProject.getMustGroups();
        if (CollectionUtils.isEmpty(mustGroups)) {
            return Lists.newArrayList();
        }
        List<StandardServiceProjectGroupDTO> result = Lists.newArrayList();
        for (StandardServiceProjectGroupDTO group : mustGroups) {
            if (Objects.isNull(group) || CollectionUtils.isEmpty(group.getServiceProjectItems())) {
                continue;
            }

            Integer optionalCount = group.getOptionalCount();
            if (optionalCount == 0) {
                // 全部可享，筛选匹配的项
                List<StandardServiceProjectItemDTO> matchedItems = group.getServiceProjectItems().stream()
                        .filter(Objects::nonNull)
                        .filter(item -> {
                            if (Objects.isNull(item.getStandardAttribute()) || Objects.isNull(item.getStandardAttribute().getCpvObjectId())) {
                                return false;
                            }
                            return cpvObjectId.equals(item.getStandardAttribute().getCpvObjectId());
                        })
                        .collect(Collectors.toList());

                if (!matchedItems.isEmpty()) {
                    // 创建新的组，只包含匹配的项
                    StandardServiceProjectGroupDTO newGroup = new StandardServiceProjectGroupDTO();
                    newGroup.setOptionalCount(group.getOptionalCount());
                    newGroup.setServiceProjectItems(matchedItems);
                    result.add(newGroup);
                }
            } else {
                // M选N，使用allMatch逻辑
                boolean allMatch = group.getServiceProjectItems().stream()
                        .filter(Objects::nonNull)
                        .allMatch(item -> {
                            if (Objects.isNull(item.getStandardAttribute()) || Objects.isNull(item.getStandardAttribute().getCpvObjectId())) {
                                return false;
                            }
                            return cpvObjectId.equals(item.getStandardAttribute().getCpvObjectId());
                        });

                if (allMatch) {
                    result.add(group);
                }
            }
        }
        return result;
    }

    /**
     * 获取同一cpvObjectId的服务项目组
     *
     * @param cpvObjectId            服务项目唯一标识
     * @param standardServiceProject 标准服务项目
     * @return 服务项目组
     */
    public List<StandardServiceProjectGroupDTO> getSameCPVObjectId(Long cpvObjectId, StandardServiceProjectDTO standardServiceProject) {
        List<StandardServiceProjectGroupDTO> result = Lists.newArrayList();
        List<StandardServiceProjectGroupDTO> mustGroups = getSameCPVObjectIdFromMustGroups(cpvObjectId, standardServiceProject);
        List<StandardServiceProjectGroupDTO> optionalGroups = getSameCPVObjectIdFromOptionGroups(cpvObjectId, standardServiceProject);
        result.addAll(mustGroups);
        result.addAll(optionalGroups);
        return result;
    }

    /**
     * 专门处理【以下M选N】模块, 筛选数据逻辑如下:
     * 1. 若内部所有cpvObjectId都相同, 则说明是同一个模块, 需要展示为特定模块, 如: 按摩/足疗M选N、自助餐M选N. 此时不会被本方法过滤到
     * 2. 若内部所有cpvObjectId数量 >= 2且不完全相同, 则说明是杂揉模块, 即出现【以下M选N】模块. 此时会被本方法过滤到
     *
     * @param standardServiceProject 标准服务项目
     * @return 服务项目组
     */
    public List<StandardServiceProjectGroupDTO> getDifferentCPVObjectId(StandardServiceProjectDTO standardServiceProject) {
        if (Objects.isNull(standardServiceProject)) {
            return Lists.newArrayList();
        }
        List<StandardServiceProjectGroupDTO> result = Lists.newArrayList();
        // 先处理mustGroups
        List<StandardServiceProjectGroupDTO> mustGroups = standardServiceProject.getMustGroups();
        if (CollectionUtils.isNotEmpty(mustGroups)) {
            List<StandardServiceProjectGroupDTO> mustGroupsList = mustGroups.stream()
                    .filter(Objects::nonNull)
                    .filter(mustGroup -> CollectionUtils.isNotEmpty(mustGroup.getServiceProjectItems()))
                    .filter(mustGroup -> mustGroup.getServiceProjectItems().size() >= 2)
                    // 必须是M选N
                    .filter(mustGroup -> mustGroup.getOptionalCount() >= 1)
                    .filter(mustGroup -> mustGroup.getServiceProjectItems().stream()
                            .filter(Objects::nonNull)
                            .filter(item -> Objects.nonNull(item.getStandardAttribute()))
                            .filter(item -> Objects.nonNull(item.getStandardAttribute().getCpvObjectId()))
                            .map(item -> item.getStandardAttribute().getCpvObjectId())
                            .distinct()
                            .count() > 1)
                    .collect(Collectors.toList());
            result.addAll(mustGroupsList);
        }

        // 再处理optionalGroups
        List<StandardServiceProjectGroupDTO> optionalGroups = standardServiceProject.getOptionalGroups();
        if (CollectionUtils.isNotEmpty(optionalGroups)) {
            List<StandardServiceProjectGroupDTO> optionalGroupsList = optionalGroups.stream()
                    .filter(Objects::nonNull)
                    .filter(optionalGroup -> CollectionUtils.isNotEmpty(optionalGroup.getServiceProjectItems()))
                    .filter(optionalGroup -> optionalGroup.getServiceProjectItems().size() >= 2)
                    // 必须是M选N
                    .filter(mustGroup -> mustGroup.getOptionalCount() >= 1)
                    .filter(optionalGroup -> optionalGroup.getServiceProjectItems().stream()
                            .filter(Objects::nonNull)
                            .filter(item -> Objects.nonNull(item.getStandardAttribute()))
                            .filter(item -> Objects.nonNull(item.getStandardAttribute().getCpvObjectId()))
                            .map(item -> item.getStandardAttribute().getCpvObjectId())
                            .distinct()
                            .count() > 1)
                    .collect(Collectors.toList());
            result.addAll(optionalGroupsList);
        }
        return result;
    }

}
