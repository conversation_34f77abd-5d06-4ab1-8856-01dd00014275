package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import org.apache.commons.lang.StringUtils;

/**
 * 平台参数工具
 */
public class PlatformUtil {

    /**
     * platForm转为mt dp
     * @param platform
     * @return
     */
    public static String getPlatformName(int platform) {
        int tempPlatform = getPlatform(platform);
        for (VCPlatformEnum platformEnum : VCPlatformEnum.values()) {
            if (platformEnum.getType() == tempPlatform) {
                return platformEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * platform统一转成1或2
     *
     * @param platform
     * @return
     */
    public static int getPlatform(int platform) {
        if (platform == VCPlatformEnum.MT.getType() || platform == VCPlatformEnum.DP.getType() ) {
            return platform;
        }
        return VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType();
    }

    public static int getPlatform(ClientTypeEnum clientTypeEnum) {
        if (clientTypeEnum.isDpClientType()) {
            return VCPlatformEnum.DP.getType();
        } else if (clientTypeEnum.isMtClientType()) {
            return VCPlatformEnum.MT.getType();
        } else {
            return VCPlatformEnum.MT.getType();
        }
    }

    /**
     * 根据platform判断是否是美团平台
     * @param platform
     * @return
     */
    public static boolean isMT(int platform) {
        if (platform == VCPlatformEnum.MT.getType() || VCClientTypeEnum.isMtClientTypeByCode(platform) ) {
            return true;
        }
        return false;
    }

    /**
     * 根据platform判断是否是点评平台
     * @param platform
     * @return
     */
    public static boolean isDP(int platform) {
        return !isMT(platform);
    }

    /**
     * 根据uaCode判断是否在端上
     * @param userAgent
     * @return
     */
    public static boolean isApp(int userAgent) {
        if (userAgent == VCClientTypeEnum.DP_APP.getCode() || userAgent == VCClientTypeEnum.MT_APP.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 根据uaCode判断是否在H5端上
     *
     * @param userAgent
     * @return
     */
    public static boolean isH5(int userAgent) {
        if (userAgent == VCClientTypeEnum.DP_M.getCode() || userAgent == VCClientTypeEnum.MT_I.getCode()) {
            return true;
        }
        return false;
    }
}
