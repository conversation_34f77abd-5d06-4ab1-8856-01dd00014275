package com.sankuai.dzshoppingguide.product.detail.application.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 10:07
 */
@Getter
public enum MetaVersionEnum {
    OLD_VERSION(1,"旧版本上单模板团单"),
    NEW_VERSION(2,"新版本上单模板团单"),
    MULTI_SKU_VERSION(3,"足疗三级类目多sku版本"),
    ;

    /**
     * 场景顺序,不要修改已有的场景的顺序,场景执行器是按照顺序执行的,命中任意一个即返回结果
     * order越小,优先级越高
     */
    private final int code;
    private final String des;

    MetaVersionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
