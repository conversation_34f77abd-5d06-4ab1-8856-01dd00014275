package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 按摩三级类目枚举
 * <AUTHOR>
 * @date 2025/4/27 17:39
 */
@Getter
@AllArgsConstructor
public enum MassageThirdCategoryEnum {

    /**
     * 足疗
     */
    FOOT_MASSAGE(106010, "足疗"),

    /**
     * 推拿/按摩
     */
    MASSAGE(100016, "推拿/按摩"),

    /**
     * 精油SPA
     */
    OIL_SPA(108009, "精油SPA"),

    /**
     * 采耳
     */
    EAR_PICKING(109016, "采耳"),

    /**
     * 艾灸
     */
    MOXIBUSTION(101019, "艾灸"),

    /**
     * 拔罐
     */
    CUPPING(103025, "拔罐"),

    /**
     * 刮痧
     */
    GUA_SHA(102020,"刮痧"),

    /**
     * 头疗
     */
    SCALP_MASSAGE(135015,"头疗"),

    /**
     * 推拿正骨
     */
    MASSAGE_BONE_SETTING(108010,"推拿正骨"),

    /**
     * 足部治理
     */
    FOOT_MANAGEMENT(109017,"足部治理"),

    /**
     * 女性保养
     */
    WOMEN_CARE(103027,"女性保养"),

    /**
     * 体态矫正
     */
    POSTURE_CORRECTION(102021, "体态矫正"),

    /**
     * 小儿推拿
     */
    PEDIATRIC_MASSAGE(103026, "小儿推拿"),

    /**
     * 运动复健
     */
    SPORTS_REHABILITATION(100017, "运动复健"),

    DEFAULT(Integer.MAX_VALUE, "其他")
    ;

    /**
     * 三级类目ID
     */
    private final int categoryId;

    /**
     * 三级类目名称
     */
    private final String categoryName;

    /**
     * 根据类目ID获取枚举值
     *
     * @param categoryId 三级类目ID
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MassageThirdCategoryEnum getByCategoryId(int categoryId) {
        for (MassageThirdCategoryEnum value : values()) {
            if (value.getCategoryId() == categoryId) {
                return value;
            }
        }
        return DEFAULT;
    }

    public static boolean isHit(int categoryId) {
        return getByCategoryId(categoryId) != DEFAULT;
    }

    /**
     * 根据类目名称获取枚举值
     *
     * @param categoryName 三级类目名称
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static MassageThirdCategoryEnum getByCategoryName(String categoryName) {
        if (categoryName == null) {
            return null;
        }
        for (MassageThirdCategoryEnum value : values()) {
            if (value.getCategoryName().equals(categoryName)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断给定的类目ID是否为有效的按摩三级类目
     *
     * @param categoryId 三级类目ID
     * @return 如果是有效的按摩三级类目则返回true，否则返回false
     */
    public static boolean isValidMassageCategory(int categoryId) {
        return getByCategoryId(categoryId) != null;
    }

    public static List<Integer> getNewReminderCategoryIds() {
        // 足疗行业,只有这三个三级分类的须知条走新的逻辑
        return Lists.newArrayList(
                FOOT_MASSAGE.getCategoryId(),
                MASSAGE.getCategoryId(),
                OIL_SPA.getCategoryId(),
                SCALP_MASSAGE.getCategoryId(),
                FOOT_MANAGEMENT.getCategoryId()
        );
    }

    /**
     * 足疗行业需要走新版团购详情的三级类目id集合
     * @return
     */
    public static List<Integer> getNewDetailCategoryIds() {
        return Lists.newArrayList(
                FOOT_MASSAGE.getCategoryId(),
                MASSAGE.getCategoryId(),
                OIL_SPA.getCategoryId(),
                EAR_PICKING.getCategoryId(),
                MOXIBUSTION.getCategoryId(),
                CUPPING.getCategoryId(),
                PEDIATRIC_MASSAGE.getCategoryId(),
                SPORTS_REHABILITATION.getCategoryId(),
                GUA_SHA.getCategoryId(),
                SCALP_MASSAGE.getCategoryId(),
                MASSAGE_BONE_SETTING.getCategoryId(),
                FOOT_MANAGEMENT.getCategoryId(),
                WOMEN_CARE.getCategoryId(),
                POSTURE_CORRECTION.getCategoryId()
        );
    }

}
