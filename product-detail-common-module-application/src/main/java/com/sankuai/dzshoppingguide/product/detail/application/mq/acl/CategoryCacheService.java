package com.sankuai.dzshoppingguide.product.detail.application.mq.acl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class CategoryCacheService {

    @Autowired
    @Qualifier("redisGuide")
    private RedisStoreClient storeClient;

    private static final String SQUIRREL_CATEGORY = "ProductCategory";

    public boolean clean(IdTypeEnum idTypeEnum, Long productId) {
        if (productId == null || productId == 0) {
            return false;
        }
        StoreKey productStoreKey = new StoreKey(SQUIRREL_CATEGORY, idTypeEnum.getCode(), productId);
        return storeClient.delete(productStoreKey);
    }

}
