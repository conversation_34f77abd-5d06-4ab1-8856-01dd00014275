package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 18:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealTagQueryResult extends FetcherReturnValueDTO {
    private Boolean hasTag;
}
