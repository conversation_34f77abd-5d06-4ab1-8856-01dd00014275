package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeLayerEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GuaranteeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.FeaturesLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 标品保障模块构造器
 */
@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
            ProductCategoryFetcher.class, ProductAttrFetcher.class,
                ProductBaseInfoFetcher.class
        }
)
public abstract class StandardGuaranteeModuleBuilder extends BaseVariableBuilder<ProductDetailGuaranteeVO> {
    private static final int ORAL_TEETH_CATEGORY = 506;

    protected final String FONT_SIZE = "12";

    @Override
    public ProductDetailGuaranteeVO doBuild() {
        return null;
    }

    protected List<GuaranteeInstructionsContentVO> buildStandardGuaranteeContents() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        GuaranteeLayerEnum guaranteeType = GuaranteeLayerEnum.DEFAULT;
        if (checkRefundByProduct(productCategory, productAttr)) {
            contents.add(new GuaranteeInstructionsContentVO("未预约可退", FONT_SIZE, guaranteeType.getKey()));
        } else if (checkAutoRefundSwitch(productBaseInfo)) {
            contents.add(new GuaranteeInstructionsContentVO("随时退", FONT_SIZE, guaranteeType.getKey()));
        }
        if (checkOverdueAutoRefund(productBaseInfo)) {
            contents.add(new GuaranteeInstructionsContentVO("过期退", FONT_SIZE, guaranteeType.getKey()));
        }
        return contents;
    }

    protected FeaturesLayer buildGuaranteeFloatingLayer(ProductCategory productCategory, ProductGuaranteeTagInfo productGuaranteeTagInfo) {
        List<String> layerKeys = Lists.newArrayList();
        FeaturesLayer guaranteeLayer = new FeaturesLayer();
        guaranteeLayer.setTitle("保障说明");
        boolean priceProtectionValid = LionConfigUtils.showPriceProtectionInfo(productCategory.getProductSecondCategoryId())
                && GuaranteeUtils.checkPriceProtectionValid(productGuaranteeTagInfo.getPriceProtectionInfo());
        // 价保
        if (priceProtectionValid) {
            guaranteeLayer.setPriceProtectionTag(productGuaranteeTagInfo.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天");
            layerKeys.add(GuaranteeLayerEnum.PRICE_PROTECTION.getKey());
            LayerConfig priceProtectionLayerConfig = getLayerConfig(GuaranteeLayerEnum.PRICE_PROTECTION.getKey());
            guaranteeLayer.setTitle(priceProtectionLayerConfig.getTitle() + guaranteeLayer.getPriceProtectionTag());
        }
        layerKeys.add(GuaranteeLayerEnum.DEFAULT.getKey());
        List<LayerConfig> layerConfigs = buildLayerConfigs(layerKeys);
        guaranteeLayer.setLayerConfigs(layerConfigs);
        return guaranteeLayer;
    }

    protected List<LayerConfig> buildLayerConfigs(List<String> layerConfigKeys) {
        if (CollectionUtils.isEmpty(layerConfigKeys)) {
            return Collections.emptyList();
        }
        Map<String, LayerConfig> layerConfigMap = LionConfigUtils.getDealGuaranteeLayerConfig();
        return layerConfigKeys.stream()
                .map(layerConfigMap::get)
                .collect(Collectors.toList());
    }

    protected LayerConfig getLayerConfig(String layerConfigKey) {
        return LionConfigUtils.getDealGuaranteeLayerConfig().get(layerConfigKey);
    }

    protected String getApplicablePeople(ProductAttr productAttr, ProductCategory productCategory) {
        if (ORAL_TEETH_CATEGORY == productCategory.getProductSecondCategoryId()) {
            return null;
        }
        List<String> peopleApplicable = productAttr.getSkuAttrValue("tooth_suit_people");
        if (CollectionUtils.isEmpty(peopleApplicable)) {
            return null;
        }
        if (peopleApplicable.contains("成人") && peopleApplicable.contains("儿童")) {
            return "成人/儿童通用";
        }
        if (peopleApplicable.contains("成人")) {
            return "限成人";
        }
        if (peopleApplicable.contains("儿童")) {
            return "限儿童";
        }
        return null;
    }

    protected String getApplicableTime(ProductAttr productAttr, ProductCategory productCategory) {
        if (ORAL_TEETH_CATEGORY == productCategory.getProductSecondCategoryId()) {
            return null;
        }
        //口腔齿科定制化适用时间展示tag
        if (DealAttrHelper.workDayAvailable(productAttr.getSkuAttrList())) {
            return "仅工作日可用";
        }
        return null;
    }

    /**
     * 判断是否支持自动退款
     * @param productBaseInfo 商品基本信息
     * @return 是否支持自动退款
     */
    protected boolean checkAutoRefundSwitch(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getRule())
                || Objects.isNull(productBaseInfo.getRule().getRefundRule())) {
            return false;
        }
        // 支持退款类型，0-不支持退款 1-支持随时退 2-支持7天退换
        return productBaseInfo.getRule().getRefundRule().getSupportRefundType() > 0;
    }

    /**
     * 判断是否支持过期自动退
     * @param productBaseInfo 商品基本信息
     * @return 是否支持过期自动退
     */
    protected boolean checkOverdueAutoRefund(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getRule())
                || Objects.isNull(productBaseInfo.getRule().getRefundRule())) {
            return false;
        }
        // 是否支持过期自动退
        return productBaseInfo.getRule().getRefundRule().isSupportOverdueAutoRefund();
    }

    /**
     * 判断是否支持预约后退改
     * @param productCategory 商品类目
     * @param productAttr 商品属性
     * @return 是否支持退改
     */
    protected boolean checkRefundByProduct(ProductCategory productCategory, ProductAttr productAttr) {
        if (!LionConfigUtils.hitCustomRefundCategoryConfig(productCategory.getProductSecondCategoryId())) {
            return false;
        }
        String refundDesc = productAttr.getSkuAttrFirstValue("reservation_policy");
        return Objects.equals("预约成功后不可退改", refundDesc);
    }

    protected void addRightArrow(GuaranteeInstructionsContentVO content) {
        Icon icon = new Icon();
        icon.setIcon("https://p0.meituan.net/ingee/5bdf750a730fd2999c49e6a32a4753f0424.png");
        content.setSuffixIcon(icon);
    }
}
