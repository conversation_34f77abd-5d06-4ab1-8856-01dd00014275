package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 15/8/3
 * @time 下午4:03
 */
@Slf4j
public class ImageUtils {

    /**
     * 图片拼接：默认拼接，返回jpg文件
     *
     * @param width
     * @param height
     * @param mode      1. cut 0. scale
     * @param waterMark
     * @return
     */
    public static String getImageUrl(String key, int width, int height, int mode, WaterMark waterMark) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.ImageUtils.getImageUrl(java.lang.String,int,int,int,com.dianping.piccentercloud.display.api.enums.WaterMark)");
        return ImageUtils.getJpgUrl(key, width, height, mode, waterMark);
    }

    public static String getFullImageUrl(String key) {
        return ImageUtils.getFullJpgUrl(key, 0, 0, 0, WaterMark.EMPTY);
    }


    /**
     * 获取jpg格式的图片url
     *
     * @param key
     * @param width
     * @param height
     * @param mode
     * @param waterMark
     * @return
     */
    public static String getJpgUrl(String key, int width, int height, int mode, WaterMark waterMark) {
        return ImageUtils.getJpgUrl(key, width, height, mode, waterMark, null);
    }

    public static String getFullJpgUrl(String key, int width, int height, int mode, WaterMark waterMark) {
        return ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, null);
    }

    /**
     * 获取jpg格式的图片url
     *
     * @param key
     * @param width
     * @param height
     * @param mode
     * @param waterMark
     * @param quality
     * @return
     */
    public static String getJpgUrl(String key, int width, int height, int mode, WaterMark waterMark, String quality) {
        Map<String, String> optionalParams = ImageUtils.createOptionalParams("1", quality);
        return ImageUtils.getPicUrl(key, width, height, mode, waterMark, optionalParams);
    }

    public static String getFullJpgUrl(String key, int width, int height, int mode, WaterMark waterMark, String quality) {
        Map<String, String> optionalParams = ImageUtils.createOptionalParams("1", quality);
        return ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
    }

    /**
     * 获取gif格式的图片url
     *
     * @param key
     * @param width
     * @param height
     * @param mode
     * @param waterMark
     * @return
     */
    public static String getGifUrl(String key, int width, int height, int mode, WaterMark waterMark) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.util.ImageUtils.getGifUrl(java.lang.String,int,int,int,com.dianping.piccentercloud.display.api.enums.WaterMark)");
        return ImageUtils.getGifUrl(key, width, height, mode, waterMark, null);
    }

    /**
     * 获取gif格式的图片url
     *
     * @param key
     * @param width
     * @param height
     * @param mode
     * @param waterMark
     * @param quality
     * @return
     */
    public static String getGifUrl(String key, int width, int height, int mode, WaterMark waterMark, String quality) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.ImageUtils.getGifUrl(java.lang.String,int,int,int,com.dianping.piccentercloud.display.api.enums.WaterMark,java.lang.String)");
        Map<String, String> optionalParams = ImageUtils.createOptionalParams("2", quality);
        if (null != optionalParams) {
            optionalParams.put("cgif", "100");
        }
        String url = ImageUtils.getPicUrl(key, width, height, mode, waterMark, optionalParams);

        if (StringUtils.isNotBlank(url)) {
            url = url.replace(".jpg", ".gif");
        }
        return url;
    }

    /**
     * 获取图片url
     *
     * @param key
     * @param width
     * @param height
     * @param mode
     * @param waterMark
     * @param optionalParams
     * @return
     */
    public static String getPicUrl(String key, int width, int height, int mode, WaterMark waterMark,
                                   Map<String, String> optionalParams) {
        if (StringUtils.isEmpty(key)) {
            return StringUtils.EMPTY;
        }
        PictureVisitParams params;
        params = new PictureVisitParams("pc", key, 0, mode, width, height, waterMark);
        if (null != optionalParams) {
            params.setOptionalParams(optionalParams);
        }
        PictureUrlGenerator generator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        return PictureUrlUtils.generateUrl(key, generator);
    }

    public static String getFullPicUrl(String key, int width, int height, int mode, WaterMark waterMark,
                                   Map<String, String> optionalParams){
        if (StringUtils.isEmpty(key)) {
            return StringUtils.EMPTY;
        }
        PictureVisitParams params;
        params = new PictureVisitParams("pc", key, 0, mode, width, height, waterMark);
        if (null != optionalParams){
            params.setOptionalParams(optionalParams);
        }
        PictureUrlGenerator generator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return generator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            log.error("ImageUtil.getPicUrl error, key: {}", key, e);
        }
        return generator.getFullPictureURL();
    }

    /**
     * 构建获取图片url的可选参数
     *
     * @param type    图片返回类型：<br/>
     *                1-jpg；<br/>
     *                2-gif；<br/>
     *                3-png；<br/>
     *                4-bmp；<br/>
     *                5-webp<br/>
     * @param quality 图片质量
     * @return
     */
    private static Map<String, String> createOptionalParams(String type, String quality) {
        Map<String, String> optionalParams = new HashMap<>(1);
        if (StringUtils.isNotEmpty(type)) {
            optionalParams.put("t", type);
        }

        if (StringUtils.isNotEmpty(quality)) {
            optionalParams.put("qa", quality);
        }

        return optionalParams;
    }

    public static String format(String key, int width, int height) {
        if (StringUtils.isEmpty(key)) {
            return StringUtils.EMPTY;
        }
        PictureVisitParams params = new PictureVisitParams("pc", key, 1, 1, width, height, WaterMark.DIANPING);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            log.error("ImageUtils.format getPictureURLWithHTTPSProtocol error, key: {}", key, e);
            try {
                return pictureUrlGenerator.getFullPictureURL();
            } catch (Exception e1) {
                log.error("ImageUtils.format getFullPictureURL error, key: {}", key, e1);
                return key;
            }
        }
    }
}
