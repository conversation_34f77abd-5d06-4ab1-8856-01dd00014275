package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.factory;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-04-29
 * @desc
 */
@Component
public class StyleFactory {

    @Resource
    private FreeMealStyle freeMealStyle;

    @Resource
    private ChessCardPackagesStyle chessCardPackagesStyle;

    @Resource
    private AdditionNotesStyle additionNotesStyle;

    @Autowired
    private FootMassageStyle footMassageStyle;

    @Autowired
    private AdditionalInfoStyle additionalInfoStyle;

    @Autowired
    private AdditionalInfoStyle0 additionalInfoStyle0;

    @Autowired
    private ServiceFacilityStyle serviceFacilityStyle;

    @Autowired
    private ServiceFacilityStyle0 serviceFacilityStyle0;

    @Resource
    private GameCurrencyStyle gameCurrencyStyle;

    @Autowired
    private BuffetFootStyle buffetFootStyle;

    @Resource
    private BathStyle bathStyle;

    @Resource
    private OverNightStyle overNightStyle;

    public FreeMealStyle createFreeMealStyle() {
        return freeMealStyle;
    }

    public ChessCardPackagesStyle createChessCardPackagesStyle() {
        return chessCardPackagesStyle;
    }

    public AdditionNotesStyle createAdditionNotesStyle() {
        return additionNotesStyle;
    }

    public FootMassageStyle createFootMassageStyle() {
        return footMassageStyle;
    }

    public AdditionalInfoStyle createAdditionalInfoStyle() {
        return additionalInfoStyle;
    }

    public AdditionalInfoStyle0 createAdditionalInfoStyle0() {
        return additionalInfoStyle0;
    }

    public ServiceFacilityStyle createServiceFacilityStyle() {
        return serviceFacilityStyle;
    }

    public ServiceFacilityStyle0 createServiceFacilityStyle0() {
        return serviceFacilityStyle0;
    }

    public GameCurrencyStyle createGameCurrencyStyle() {
        return gameCurrencyStyle;
    }

    public BuffetFootStyle createBuffetFootStyle() {
        return buffetFootStyle;
    }

    public OverNightStyle createOverNightStyle() {
        return overNightStyle;
    }

    public BathStyle createBathStyle() {
        return bathStyle;
    }
}
