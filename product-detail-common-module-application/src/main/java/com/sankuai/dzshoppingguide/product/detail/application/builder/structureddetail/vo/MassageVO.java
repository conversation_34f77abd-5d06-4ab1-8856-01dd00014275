package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 15:11
 * @Description: 二级类目: 按摩足疗
 */
@Data
@Builder
@TypeDoc(description = "按摩足疗结构化详情")
public class MassageVO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "图标地址")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "内容")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "详情")
    @MobileDo.MobileField(key = 0xa83b)
    private String detail;

    @FieldDoc(description = "对应前端展示类型")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;
}
