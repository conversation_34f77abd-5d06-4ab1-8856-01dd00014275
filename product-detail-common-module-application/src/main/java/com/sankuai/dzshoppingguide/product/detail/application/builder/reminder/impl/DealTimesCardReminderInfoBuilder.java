package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 团购次卡须知条
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/10 20:55
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class, ProductAttrFetcher.class, GuaranteeTagFetcher.class, ProductPurchaseNoteFetcher.class,ProductCategoryFetcher.class}
)
@Slf4j
public class DealTimesCardReminderInfoBuilder extends AbstractReminderInfoBuilder {

    ProductBaseInfo baseInfo;
    ProductAttr productAttr;
    ProductGuaranteeTagInfo guaranteeTagResult;
    ProductPurchaseNote productPurchaseNote;
    ProductCategory productCategory;

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }
        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();

        baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productAttr = getDependencyResult(ProductAttrFetcher.class);
        if  (baseInfo == null) {
            return null;
        }

        // 足疗行业的团购次卡的须知条中间拼接特殊的日期和时间信息
        processMassageSpecialCategory(contents);

        ReminderInfoUtils.buildReminderInfo(TimesDealUtil.onlyVerificationOne(productAttr) ? "单次仅可核销一份" : "可单次核销多份").ifPresent(contents::add);

        guaranteeTagResult = getDependencyResult(GuaranteeTagFetcher.class);

        if (guaranteeTagResult != null && isAnXinXue(guaranteeTagResult.getSafeLearnGuaranteeInfo())) {
            // 教育次卡，命中安心学标签，不展示“仅支持整单退”
            return baseReminderInfo;
        }

        productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
        if (productPurchaseNote != null && TimesDealUtil.hasPurchaseNoteTable(productPurchaseNote.getPurchaseNote(), baseInfo)) {
            // 命中有阶梯退款规则的团购次卡,不展示“仅支持整单退”
            return baseReminderInfo;
        }

        ReminderInfoUtils.buildReminderInfo("仅支持整单退").ifPresent(contents::add);
        return baseReminderInfo;
    }

    private void processMassageSpecialCategory(List<GuaranteeInstructionsContentVO> contents) {
        // 按摩足疗相关的须知条中间需要拼接
        productCategory = getDependencyResult(ProductCategoryFetcher.class);
        int thirdCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
        boolean massageNewReminder = MassageThirdCategoryEnum.getNewReminderCategoryIds().contains(thirdCategoryId);
        if (!massageNewReminder) {
            return;
        }

        // 【预约信息】·【日期】【时间段】可用·【有效时间】
        if (AvailableTimeHelper.hasAvailableTimePeriod(productAttr)) {
            // 如果选择了部分时间可用
            AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        } else {
            // 选择了营业时间内全部可用
            AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
        }
    }

    public boolean isAnXinXue(ObjectGuaranteeTagDTO objectGuaranteeTagDTO){
        if (objectGuaranteeTagDTO == null || CollectionUtils.isEmpty(objectGuaranteeTagDTO.getTags())) {
            return false;
        }
        //安心学
        int tagCode = objectGuaranteeTagDTO.getTags().stream()
                .filter(Objects::nonNull)
                .filter(tagDTO-> GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode() == tagDTO.getCode())
                .findFirst().map(TagDTO::getCode)
                .orElse(-1);
        return GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode() == tagCode;
    }
}
