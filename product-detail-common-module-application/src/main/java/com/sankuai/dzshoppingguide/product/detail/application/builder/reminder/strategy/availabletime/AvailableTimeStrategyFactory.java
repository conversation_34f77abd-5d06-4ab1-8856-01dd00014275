package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime;

import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class AvailableTimeStrategyFactory implements InitializingBean {
    @Autowired
    private List<AvailableTimeStrategy> strategyList;

    private static final Map<AvailableTimeStrategyEnum, AvailableTimeStrategy> AVAILABLE_TIME_STRATEGY_MAP = Maps
            .newEnumMap(AvailableTimeStrategyEnum.class);

    public AvailableTimeStrategy getStrategy(int categoryId) {
        AvailableTimeStrategyEnum availableTimeStrategyType = AvailableTimeStrategyEnum
                .valueOf(categoryId);
        AvailableTimeStrategy availableTimeStrategy = AVAILABLE_TIME_STRATEGY_MAP.get(availableTimeStrategyType);
        if (availableTimeStrategy == null) {
            return AVAILABLE_TIME_STRATEGY_MAP.get(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        }
        return availableTimeStrategy;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        strategyList.forEach(strategy -> AVAILABLE_TIME_STRATEGY_MAP.put(strategy.getStrategyType(), strategy));
    }
}