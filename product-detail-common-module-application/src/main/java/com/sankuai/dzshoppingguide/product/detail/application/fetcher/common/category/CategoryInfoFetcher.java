package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Optional;
/**
 * <AUTHOR>
 * @date 2025/4/15 10:52
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
@Slf4j
public class CategoryInfoFetcher extends
        ComponentFetcherContext<QueryByDealGroupIdRequestBuilder, QueryCenterAggregateReturnValue, CategoryInfo> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder request) {}

    @Override
    protected FetcherResponse<CategoryInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        String categoryName = Optional.ofNullable(aggregateResult).map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO).map(DealGroupDTO::getCategory).map(DealGroupCategoryDTO::getServiceType).orElse(StringUtils.EMPTY);
        return FetcherResponse.succeed(new CategoryInfo(categoryName));
    }
}
