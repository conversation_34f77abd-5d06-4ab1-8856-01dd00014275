package com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician;

import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.technician.query.center.query.TechSearchQuery;
import com.sankuai.technician.query.center.query.info.TechSearchInfo;
import com.sankuai.technician.query.center.query.info.TechSearchLbsInfo;
import com.sankuai.technician.query.center.query.page.TechSearchPage;
import com.sankuai.technician.query.center.result.TechItem;
import com.sankuai.technician.query.center.result.TechSearchResult;
import com.sankuai.technician.query.center.service.TechUniqueQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: litengfei04
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class, ProductAttrFetcher.class, ShopIdMapperFetcher.class},
        timeout = 3000
)
@Slf4j
public class DoctorFetcher extends NormalFetcherContext<DoctorReturnValue> {

    @MdpPigeonClient(url = "com.sankuai.technician.query.center.service.TechUniqueQueryService", callType = CallMethod.CALLBACK, timeout = 3000)
    private TechUniqueQueryService techUniqueQueryService;

    private static final String TECHNICIAN_ATTR_KEY = "OperateAssociatedPhysician";
    private static final int MAX_DOCTOR_NUM = 20;

    @Override
    protected CompletableFuture<DoctorReturnValue> doFetch() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);

        List<String> technicianList = productAttr.getSkuAttrValues(TECHNICIAN_ATTR_KEY);
        if (CollectionUtils.isEmpty(technicianList)) {
            return CompletableFuture.completedFuture(null);
        }
        List<Integer> techIdList = technicianList.stream().filter(NumberUtils::isDigits).map(NumberUtils::toInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(techIdList)) {
            return CompletableFuture.completedFuture(null);
        }
        TechSearchQuery techSearchQuery = buildSearchQuery(techIdList);
        return query(techSearchQuery)
                .thenApply(r -> {
                    if (r == null || !r.respSuccess() || r.getData() == null) {
                        return null;
                    }
                    TechSearchResult techSearchResult = (TechSearchResult) r.getData();
                    if (CollectionUtils.isEmpty(techSearchResult.getTechItems())) {
                        return null;
                    }
                    DoctorReturnValue doctorReturnValue = new DoctorReturnValue();
                    doctorReturnValue.setTechItems(sortAndFilter(techIdList, techSearchResult.getTechItems(), shopIdMapper));
                    return doctorReturnValue;
                })
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "DoctorFetcher")
                            .putTag("method", "doFetch")
                            .message(String.format("doFetch error, request : %s", JsonCodec.encode(techSearchQuery))));
                    return null;
                });
    }

    private List<TechItem> sortAndFilter(List<Integer> techIdList, List<TechItem> techItems, ShopIdMapper shopIdMapper) {
        Map<Integer, TechItem> techItemMap = techItems.stream().collect(Collectors.toMap(TechItem::getTechId, Function.identity(), (a1, a2) -> a1));
        return techIdList.stream().map(techItemMap::get).filter(Objects::nonNull).filter(i -> isSameShop(i, shopIdMapper)).collect(Collectors.toList());
    }

    private boolean isSameShop(TechItem techItem, ShopIdMapper shopIdMapper) {
        if (!Lion.getBoolean(MdpContextUtils.getAppKey(), "enable.technician.same.shop.filter", true)) {
            return true;
        }
        if (MapUtils.isEmpty(techItem.getValues())) {
            return false;
        }
        return String.valueOf(shopIdMapper.getDpBestShopId()).equals(String.valueOf(techItem.getValues().get("es.field.shopid")));
    }

    private TechSearchQuery buildSearchQuery(List<Integer> techIdList) {
        List<Integer> techIds = techIdList.subList(0, NumberUtils.min(techIdList.size(), MAX_DOCTOR_NUM));
        return TechSearchQuery.builder()
                .queryKey("ophthalmology_deal_apply_doctor_query") // 仅眼科可用，如拓展其他行业，联系wb_subeibei新增
                .info(TechSearchInfo.builder().techIds(techIds).lbsInfo(TechSearchLbsInfo.builder()
                        .latitude(BigDecimal.valueOf(request.getUserLat())).longitude(BigDecimal.valueOf(request.getUserLng())).build()).build())
                .page(TechSearchPage.builder().pageNo(1).pageSize(techIds.size()).build())
                .build();
    }

    private CompletableFuture<TechnicianResp> query(TechSearchQuery techSearchQuery) {
        if (techSearchQuery == null) {
            return CompletableFuture.completedFuture(null);
        }
        CompletableFuture<TechnicianResp> future = PigeonCallbackUtils.setPigeonCallback(TechnicianResp.class);
        techUniqueQueryService.searchTech(techSearchQuery);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "techUniqueQueryService")
                    .putTag("method", "searchTech")
                    .message(String.format("searchTech error, request : %s, error message:%s", JsonCodec.encodeWithUTF8(techSearchQuery), e)));
            return null;
        });
        return future;
    }

}