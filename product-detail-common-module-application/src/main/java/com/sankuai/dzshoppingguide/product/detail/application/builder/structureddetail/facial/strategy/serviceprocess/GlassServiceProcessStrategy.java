package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.dto.EyesProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.ServiceProcessUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/16 20:18
 */
@Component
@Slf4j
public class GlassServiceProcessStrategy extends AbstractSubModuleBuildStrategy {
    @Override
    public String getSubModuleName() {
        return SubModuleKey.SERVICE_PROCESS;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        return categoryId == SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId();
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = doBuild(context);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        // 增加分隔符
        DealDetailStructuredDetailVO separator = DealDetailStructuredUtils.buildLimiter();
        result.add(separator);
        return result;
    }

    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return Collections.emptyList();
        }
        // 服务流程信息
        List<String> servicesProcessStr = productAttr.getSkuAttrValue("serviceProcess");
        List<EyesProcessDTO> processDTOS = ServiceProcessUtils.parseProcess(servicesProcessStr);
        if (CollectionUtils.isEmpty(processDTOS)) {
            return Collections.emptyList();
        }

        List<DealDetailStructuredDetailVO> result = new ArrayList<>();


        processDTOS.stream().filter(Objects::nonNull).forEach(processDTO -> {
            if ("验光".equals(processDTO.getOphthalmic_services_process())) {
                DealDetailStructuredUtils.buildProcessInfo("验光", processDTO.getDuration()).ifPresent(result::add);
                // 验光特有的信息
                List<String> optometryProcess = productAttr.getSkuAttrValue("optometry_process");
                if (CollectionUtils.isNotEmpty(optometryProcess)) {
                    String content = optometryProcess.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
                    DealDetailStructuredUtils.buildSubProcessInfo(content, null).ifPresent(result::add);
                }
            } else {
                DealDetailStructuredUtils.buildProcessInfo(processDTO.getOphthalmic_services_process(), processDTO.getDuration()).ifPresent(result::add);
            }
        });

        // 标题模块
        int totalDuration = processDTOS.stream().map(EyesProcessDTO::getDuration).map(ServiceProcessUtils::parseDuration).reduce(Integer::sum).orElse(0);
        if (CollectionUtils.isNotEmpty(result)) {
            DealDetailStructuredUtils.buildServiceProcessTitle(totalDuration).ifPresent(item -> result.add(0,item));
        }

        // 底部的注释
        DealDetailStructuredUtils.buildAnnotation().ifPresent(result::add);

        // 填充order
        ServiceProcessUtils.setOrder(result);

        return result;
    }
}
