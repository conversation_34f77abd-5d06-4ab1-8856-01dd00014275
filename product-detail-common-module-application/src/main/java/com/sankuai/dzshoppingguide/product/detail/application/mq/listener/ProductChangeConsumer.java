package com.sankuai.dzshoppingguide.product.detail.application.mq.listener;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mpproduct.general.model.dzMessage.DzProductChangeMsg;
import com.sankuai.dzshoppingguide.product.detail.application.mq.acl.CategoryCacheService;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 统一商品发布变更
 * https://km.sankuai.com/collabpage/**********
 */
@Slf4j
public class ProductChangeConsumer implements IMessageListener {

    @Resource
    private CategoryCacheService categoryCacheService;
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
        if (message == null || StringUtils.isBlank((String) message.getBody())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            DzProductChangeMsg changeMessage = JSON.parseObject((String) message.getBody(), DzProductChangeMsg.class);
            if (Objects.isNull(changeMessage) || CollectionUtils.isEmpty(changeMessage.getProductChangeDTOList()) || changeMessage.getProductChangeDTOList().get(0) == null) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 一条消息只会有一个商品，list里可能分别是product 、product+sku1、product+sku2的变更
            Long unifiedProductId = changeMessage.getProductChangeDTOList().get(0).getPlatformProductId();
            if (unifiedProductId == null || unifiedProductId <= 0) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            List<DealGroupDTO> dealGroupDTOList = compositeAtomService.getProductBaseInfoByUnifiedId(Lists.newArrayList(unifiedProductId));
            if (CollectionUtils.isEmpty(dealGroupDTOList) || dealGroupDTOList.get(0) == null) {
                return ConsumeStatus.RECONSUME_LATER;
            }
            Long bizProductId = dealGroupDTOList.get(0).getBizProductId();
            // 团购
            if (bizProductId == null || bizProductId <= 0) {
                categoryCacheService.clean(IdTypeEnum.DP, dealGroupDTOList.get(0).getDpDealGroupId());
                categoryCacheService.clean(IdTypeEnum.MT, dealGroupDTOList.get(0).getMtDealGroupId());
            } else {
                // 泛商品
                categoryCacheService.clean(IdTypeEnum.BIZ_PRODUCT, bizProductId);
            }
            Cat.logEvent("CategoryUpdate", "success");
        } catch (Exception e) {
            log.error("ProductChangeConsumer error.", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
