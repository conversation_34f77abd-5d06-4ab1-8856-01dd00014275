package com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags;

import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/6 11:39
 */
@Fetcher(
        timeout = 50000L,
        previousLayerDependencies = {DealGroupIdMapperFetcher.class}
)
@Slf4j
public class ReviewTagFetcher extends NormalFetcherContext<ReviewTagResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ReviewTagResult> doFetch() {
        DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        Long mtDealGroupId = Optional.ofNullable(dealGroupIdMapper).map(DealGroupIdMapper::getMtDealGroupId).orElse(0L);
        Long dpDealGroupId = Optional.ofNullable(dealGroupIdMapper).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);

        if (request.getClientTypeEnum().isMtClientType()) {
            return compositeAtomService.queryMtReview(mtDealGroupId).thenApply(res -> {
                ReviewTagResult result = new ReviewTagResult();
                result.setMtReviewInfo(res);
                return result;
            });
        } else {
            return compositeAtomService.queryDpReview(dpDealGroupId).thenApply(res -> {
                if (CollectionUtils.isEmpty(res)) {
                    return null;
                }
                ReviewStarDistributionDTO reviewStarDistributionDTO = res.stream().filter(Objects::nonNull).findFirst().orElse(null);
                ReviewTagResult result = new ReviewTagResult();
                result.setDpReviewInfo(reviewStarDistributionDTO);
                return result;
            });
        }
    }
}
