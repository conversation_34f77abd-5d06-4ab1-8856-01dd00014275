package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 门店标签返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class ShopTagReturnValue extends FetcherReturnValueDTO {
    private List<DisplayTagDto> shopDisplayTagDtoList;
}
