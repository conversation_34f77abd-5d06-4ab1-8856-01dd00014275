package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.enums.MetaVersionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.dto.DealVersionConfig;
import com.sankuai.general.product.query.center.client.dto.MetaObjectInfoDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class DealVersionUtils {

    /**
     * 判断数据版本是否低于新版下界
     * @return true-老版（数据版本<配置版本下界） false-新版（数据版本>=配置版本下界）
     * https://km.sankuai.com/collabpage/2666808593
     */
    public static MetaVersionEnum isOldMetaVersion(ProductBaseInfo baseInfo, ProductCategory productCategory, String lionKey) {
        ProductMetaInfo productMetaInfo = toProductMetaInfo(baseInfo,productCategory);
        if (productMetaInfo.getObjectVersion() == null || productMetaInfo.getObjectVersion() <= 0 || productMetaInfo.getSecondId() == null) {
            return MetaVersionEnum.OLD_VERSION;
        }
        int categoryId = productMetaInfo.getSecondId();
        // 查询新版的版本下界配置
        Config config = Lion.getBean(MdpContextUtils.getAppKey(), lionKey, Config.class);
        if (config == null) {
            return MetaVersionEnum.NEW_VERSION;
        }
        Map<Integer, List<DealVersionConfig>> versionBoundMap = config.getVersionBoundMap();
        if (MapUtils.isEmpty(versionBoundMap) || !versionBoundMap.containsKey(categoryId)) {
            return MetaVersionEnum.NEW_VERSION;
        }
        List<DealVersionConfig> versionConfigList = versionBoundMap.get(categoryId);
        if (CollectionUtils.isEmpty(versionConfigList)) {
            return MetaVersionEnum.NEW_VERSION;
        }
        // 判断商品结构id
        if (productMetaInfo.getObjectId() == null || productMetaInfo.getObjectId() <= 0) {
            return MetaVersionEnum.OLD_VERSION;
        }
        DealVersionConfig versionConfig = versionConfigList.stream().filter(c -> productMetaInfo.getObjectId().equals(c.getObjectId())).findFirst().orElse(null);
        // 如果无版本约束返回新版
        if (versionConfig == null || versionConfig.getObjectId() == null || versionConfig.getObjectId() <= 0) {
            return MetaVersionEnum.NEW_VERSION;
        }
        // 判断商品版本，当数据版本<新版下界时返回老版(true)，否则返回新版(false)
        if (versionConfig.getMinVersion() == null || versionConfig.getMinVersion() <= 0) {
            return MetaVersionEnum.NEW_VERSION;
        }
        
        // 足疗多sku特殊逻辑
        MassageThirdCategoryEnum thirdCategoryEnum = CategoryUtils.massageThirdCategoryEnum(CategoryUtils.getThirdCategoryId(productCategory));
        if (Objects.equals(thirdCategoryEnum,MassageThirdCategoryEnum.FOOT_MASSAGE)) {
            return footMassageVersionHandle(versionConfig,productMetaInfo);
        }

        return productMetaInfo.getObjectVersion().compareTo(versionConfig.getMinVersion()) < 0 ? MetaVersionEnum.OLD_VERSION : MetaVersionEnum.NEW_VERSION;
    }
    
    private static MetaVersionEnum footMassageVersionHandle(DealVersionConfig versionConfig, ProductMetaInfo productMetaInfo) {
        if (productMetaInfo.getObjectVersion() == null || productMetaInfo.getObjectVersion() <= 0 || productMetaInfo.getSecondId() == null) {
            return MetaVersionEnum.OLD_VERSION;
        }

        // 判断商品结构id
        if (productMetaInfo.getObjectId() == null || productMetaInfo.getObjectId() <= 0) {
            return MetaVersionEnum.OLD_VERSION;
        }

        // 如果无版本约束返回新版
        if (versionConfig == null || versionConfig.getObjectId() == null || versionConfig.getObjectId() <= 0) {
            return MetaVersionEnum.NEW_VERSION;
        }
        // 判断商品版本，当数据版本<新版下界时返回老版(true)，否则返回新版(false)
        if (versionConfig.getMinVersion() == null || versionConfig.getMinVersion() <= 0 || CollectionUtils.isEmpty(versionConfig.getMultiSkuVersion())) {
            return MetaVersionEnum.NEW_VERSION;
        }

        Long minVersion = versionConfig.getMinVersion();
        List<Long> multiSkuVersion = versionConfig.getMultiSkuVersion();
        Long objectVersion = productMetaInfo.getObjectVersion();

        if (objectVersion.compareTo(minVersion) >= 0) {
            return MetaVersionEnum.NEW_VERSION;
        }

        if (multiSkuVersion.contains(objectVersion)) {
            return MetaVersionEnum.MULTI_SKU_VERSION;
        }

        return MetaVersionEnum.OLD_VERSION;
    }

    private static ProductMetaInfo toProductMetaInfo(ProductBaseInfo baseInfo, ProductCategory productCategory) {
        ProductMetaInfo productMetaInfo = new ProductMetaInfo();
        productMetaInfo.setSecondId(Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0));
        productMetaInfo.setObjectId(Optional.ofNullable(baseInfo).map(ProductBaseInfo::getMetaObjectInfo).map(MetaObjectInfoDTO::getObjectId).orElse(0L));
        productMetaInfo.setObjectVersion(Optional.ofNullable(baseInfo).map(ProductBaseInfo::getMetaObjectInfo).map(MetaObjectInfoDTO::getObjectVersion).orElse(0L));
        return productMetaInfo;
    }

    @Data
    private static class Config implements Serializable {
        private Map<Integer, List<DealVersionConfig>> versionBoundMap;
    }

    @Data
    private static class ProductMetaInfo implements Serializable {
        /**
         * 二级分类
         */
        private Integer secondId;
        /**
         * 商品结构id
         */
        private Long objectId;
        /**
         * 商品结构版本
         */
        private Long objectVersion;
    }
}