package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils;

import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/12 13:09
 */
public class SkuIdUtils {

    public static long getSkuId(final ProductTypeEnum productType,
                                final DealGroupDealDTO sku) {
        Long skuId;
        switch (productType.getProductModelType()) {
            case DEAL:
                skuId = sku.getDealId();
                break;
            case FUN_PRODUCT:
                skuId = sku.getBizDealId();
                break;
            default:
                throw new IllegalArgumentException("暂不支持该商品模型类型:" + productType.getProductModelType().name());
        }
        return Optional.ofNullable(skuId).orElse(0L);
    }

}
