package com.sankuai.dzshoppingguide.product.detail.application.enums;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealreserve.DealOnlineReserveResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.DealTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 13:32
 */
@Getter
public enum FetcherResultEnum {
    PRODUCT_ATTR("ProductAttr", ProductAttr.class),
    PRODUCT_CATEGORY("ProductCategory", ProductCategory.class),
    DEAL_ONLINE_RESERVE("DealOnlineReserveResult", DealOnlineReserveResult.class),
    DEAL_TAG("DealTagQueryResult", DealTagQueryResult.class),
    PRODUCT_SERVICE_PROJECT("ProductServiceProject",ProductServiceProject .class),
    PRODUCT_BASE_INFO("ProductBaseInfo", ProductBaseInfo.class),
    GUARANTEE_TAG("ProductGuaranteeTagInfo", ProductGuaranteeTagInfo.class),
    PRODUCT_PURCHASE_NOTE("ProductPurchaseNote", ProductPurchaseNote.class),
    SKU_ATTR("SkuAttr", SkuAttr.class),
    SKU_DEFAULT_SELECT("SkuDefaultSelect", SkuDefaultSelect.class),
    ;

    ;

    FetcherResultEnum(String name, Class<? extends FetcherReturnValueDTO> fetcherReturnClass) {
        this.name = name;
        this.fetcherReturnClass = fetcherReturnClass;
    }

    private final String name;
    private final Class<? extends FetcherReturnValueDTO> fetcherReturnClass;

    @SuppressWarnings("unchecked")
    public <Result extends FetcherReturnValueDTO> Result getResult(Map<FetcherResultEnum, FetcherReturnValueDTO> fetcherResultMap) {
        if (MapUtils.isEmpty(fetcherResultMap)) {
            return null;
        }
        FetcherReturnValueDTO result = fetcherResultMap.getOrDefault(this, null);
        if (result == null) {
            return null;
        }
        if( this.fetcherReturnClass.isInstance(result) ) {
            return (Result) this.fetcherReturnClass.cast(result);
        }
        return null;
    }
}
