package com.sankuai.dzshoppingguide.product.detail.application.fetcher.review;

import com.sankuai.beautycontent.beautylaunchapi.model.dto.mtreview.ReviewDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MtReserveReviewReturnValue extends FetcherReturnValueDTO {

    private ReviewDTO data;

}
