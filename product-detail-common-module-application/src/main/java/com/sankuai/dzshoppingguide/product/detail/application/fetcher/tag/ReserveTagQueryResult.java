package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag;

import com.dianping.deal.tag.dto.MetaTagDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/17 15:43
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ReserveTagQueryResult extends FetcherReturnValueDTO {
    private MetaTagDTO metaTagDTO;
}
