package com.sankuai.dzshoppingguide.product.detail.application.builder.tab;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.detailtab.vo.ModuleDetailTabVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/5 16:54
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_TAB,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class
        }
)
public class ModuleDetailTabBuilder extends BaseBuilder<ModuleDetailTabVO> {
    @Override
    public ModuleDetailTabVO doBuild() {
        return null;
    }
}
