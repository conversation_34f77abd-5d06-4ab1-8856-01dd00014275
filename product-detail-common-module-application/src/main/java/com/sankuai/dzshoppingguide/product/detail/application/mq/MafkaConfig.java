package com.sankuai.dzshoppingguide.product.detail.application.mq;

import com.dianping.lion.Environment;
import com.meituan.mafka.client.bean.MafkaConsumer;
import com.sankuai.dzshoppingguide.product.detail.application.mq.listener.ProductChangeConsumer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MafkaConfig {

    @Bean(initMethod = "start", destroyMethod = "close")
    public MafkaConsumer mafkaProductChangeConsumer() {
        MafkaConsumer mafkaConsumer = new MafkaConsumer();
        mafkaConsumer.setNamespace("daozong");
        mafkaConsumer.setAppkey(Environment.getAppName());
        mafkaConsumer.setTopic("dz_product_change_msg");
        mafkaConsumer.setGroup("product.detail.category.update");
        mafkaConsumer.setListener(productChangeConsumer());
        mafkaConsumer.setClassName("java.lang.String");
        return mafkaConsumer;
    }

    @Bean
    public ProductChangeConsumer productChangeConsumer() {
        return new ProductChangeConsumer();
    }
}
