package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-09
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductReadjustPriceRule extends FetcherReturnValueDTO {
    private Map<Long, List<ReadjustPriceRuleDTO>> readjustPriceRuleMap;

    public ProductReadjustPriceRule(Map<Long, List<ReadjustPriceRuleDTO>> readjustPriceRuleMap) {
        this.readjustPriceRuleMap = readjustPriceRuleMap;
    }
}
