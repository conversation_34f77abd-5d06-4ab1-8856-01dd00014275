package com.sankuai.dzshoppingguide.product.detail.application.utils;


import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: created by hang.yu on 2024/1/11 15:02
 */
@Slf4j
public class TimesDealUtil {

    /**
     * 判断是否是多次卡
     */
    public static boolean isMultiTimesCard(DealCtx dealCtx) {
        return dealCtx != null
                && dealCtx.getDealGroupDTO() != null
                && dealCtx.getDealGroupDTO().getBasic() != null
                && dealCtx.getDealGroupDTO().getBasic().getTradeType() != null
                && dealCtx.getDealGroupDTO().getBasic().getTradeType().equals(19);
    }

    public static boolean isDealTimesCard(ProductBaseInfo baseInfo) {
        int tradeType = Optional.ofNullable(baseInfo).map(ProductBaseInfo::getBasic).map(DealGroupBasicDTO::getTradeType).orElse(0);
        return tradeType == 19;
    }

    /**
     * 判断是否 单次到店仅可核销一次
     */
    public static boolean onlyVerificationOne(ProductAttr productAttr) {
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return false;
        }
        return DealAttrHelper.onlyVerificationOne(productAttr.getSkuAttrList());
    }


    public static boolean hasPurchaseNoteTable(PurchaseNoteDTO sourcePurchaseNoteDTO, ProductBaseInfo baseInfo) {
        // 非团购次卡
        if (!TimesDealUtil.isDealTimesCard(baseInfo)) {
            return false;
        }
        if ( Objects.isNull(sourcePurchaseNoteDTO)) {
            return false;
        }
        List<StandardDisplayModuleDTO> modules = sourcePurchaseNoteDTO.getModules();

        if (CollectionUtils.isEmpty(modules)) {
            return false;
        }

        // 说否含有新增的退款规则模块
        boolean isContainRefundModule = modules.stream().filter(Objects::nonNull)
                .anyMatch(module -> Objects.equals("退款规则", module.getModuleName()));

        // 是否含有退款表格信息
        boolean isContainRefundTable = modules.stream().filter(Objects::nonNull).map(StandardDisplayModuleDTO::getItems)
                .filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull)
                .map(StandardDisplayItemDTO::getItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .anyMatch(item -> item != null && item.getType() == 4);

        // type为4 或者 含有退款规则的module 则说明含有退款表格信息
        return isContainRefundModule || isContainRefundTable;
    }

    public static String additionalInfoParse(String html) {
        if (StringUtils.isBlank(html)) {
            return StringUtils.EMPTY;
        }
        try {
            Document doc = Jsoup.parse(html);
            // 获取最后一个div的内容
            Element lastDiv = doc.select("div").last();
            if (lastDiv == null || StringUtils.isBlank(lastDiv.text())) {
                return null;
            }
            return lastDiv.text();
        } catch (Exception e) {
            log.error("parse html error,htmlContent:{}",html,e);
        }
        return StringUtils.EMPTY;
    }
}
