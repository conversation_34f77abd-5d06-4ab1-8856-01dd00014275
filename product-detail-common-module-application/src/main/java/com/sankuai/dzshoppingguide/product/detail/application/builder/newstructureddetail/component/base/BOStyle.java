package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.base;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.BaseComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;

import java.util.List;

public interface BOStyle<T extends ComponentBO> extends BaseComponent0 {

    /**
     * 构建样式详情
     */
    List<T> build(DealDetailBuildContext context);
}
