package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.theme;

import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.enums.BusinessStateEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dztheme.shop.enums.ClientOS;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.enums.SourceEnum;
import com.sankuai.dztheme.shop.enums.ThemeCoordType;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.AttrItemDTO;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.nib.mkt.common.base.enums.PageSourceEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 团购主题Fetcher
 */
@Fetcher(
        previousLayerDependencies = {
                ShopIdMapperFetcher.class
        }
)
@Slf4j
public class ShopThemeFetcher extends NormalFetcherContext<ShopCard> {

    @MdpPigeonClient(url = "com.sankuai.dztheme.shop.service.DzThemeShopService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private DzThemeShopService dzThemeShopService;

    private static final String IOS = "ios";
    private static final String SOURCE_MAPPING_LION_KEY = "deal.source.2.mkt.source.mapping";
    private static final String MT_PLAN_ID = "10500872";
    private static final String DP_PLAN_ID = "10500873";
    private static final String BUSINESS_HOUR_ATTR_KEY = "shopBusinessInfoDTO";

    private long shopId;

    @Override
    protected CompletableFuture<ShopCard> doFetch() {
        try {
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            shopId = getShopId(shopIdMapper);

            return queryTheme(buildThemeRequest(request)).thenApply(this::toShopCard);
        } catch (Exception e) {
            log.error("ShopThemeFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private CompletableFuture<ShopThemeResponse> queryTheme(ShopThemePlanRequest request) {
        CompletableFuture<ShopThemeResponse> future = PigeonCallbackUtils.setPigeonCallback(ShopThemeResponse.class);
        dzThemeShopService.queryShopTheme(request);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "ShopThemeFetcher")
                    .putTag("method", "queryTheme")
                    .message(String.format("searchBestShop error, request : %s, error message:%s", JsonCodec.encodeWithUTF8(request),e)));
            return null;
        });
        return future;
    }


    private ShopThemePlanRequest buildThemeRequest(ProductDetailPageRequest request) {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        String planId = isMt ? MT_PLAN_ID : DP_PLAN_ID;
        ShopThemePlanRequest shopThemePlanRequest = new ShopThemePlanRequest();
        shopThemePlanRequest.setLongShopIds(Lists.newArrayList(shopId));
        shopThemePlanRequest.setClientType(toClientType(request));
        shopThemePlanRequest.setMiniProgram(request.getClientTypeEnum().isInWxXCX());
        shopThemePlanRequest.setNativeClient(request.getClientTypeEnum().isInApp());
        shopThemePlanRequest.setCityId(request.getCityId());
        shopThemePlanRequest.setUserId(request.getUserId());
        shopThemePlanRequest.setDeviceId(request.getShepherdGatewayParam().getDeviceId());
        shopThemePlanRequest.setUnionId(request.getShepherdGatewayParam().getUnionid());
        shopThemePlanRequest.setPlanId(planId);
        shopThemePlanRequest.setClientVersion(request.getShepherdGatewayParam().getAppVersion());
        shopThemePlanRequest.setPlatform(isMt ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        shopThemePlanRequest.setClientOS(IOS.equals(request.getShepherdGatewayParam().getMobileOSType()) ? ClientOS.IOS.getType() : ClientOS.ANDROID.getType());
        shopThemePlanRequest.setCoordType(ThemeCoordType.GCJ02.getType());
        shopThemePlanRequest.setUserLat(request.getUserLat());
        shopThemePlanRequest.setUserLng(request.getUserLng());

        // 业务参数
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("localCityId", request.getGpsCityId());
        extParams.put("mtUserId", request.getMtUserId());
        extParams.put("mktPageSource", PageSourceEnum.dealDetailPage.getCode());
        extParams.put("appVersion", request.getShepherdGatewayParam().getAppVersion());
        extParams.put("dealId", request.getProductId());
        extParams.put("trafficFlag", getMktSource(request.getPageSource()));
        extParams.put("source", SourceEnum.LISTING.getCode());
        shopThemePlanRequest.setExtParams(extParams);

        return shopThemePlanRequest;
    }

    private ShopCard toShopCard(ShopThemeResponse shopThemeResponse) {
        ShopCard shop = new ShopCard();
        if (shopThemeResponse == null || !shopThemeResponse.isSuccess() || MapUtils.isEmpty(shopThemeResponse.getShopCardDTOMap()) || shopThemeResponse.getShopCardDTOMap().get(shopId) == null) {
            return shop;
        }
        ShopCardDTO cardDTO = shopThemeResponse.getShopCardDTOMap().get(shopId);
        shop.setShopId(shopId);
        shop.setShopMapUrl(getMapUrl(cardDTO));
        shop.setShopUrl(ShopUrlUtils.getShopDetailUrl(request));
        shop.setPhoneNos(cardDTO.getContact() != null ? cardDTO.getContact().getPhoneList() : null);
        shop.setShopName(cardDTO.getShopName());
        ShopBusinessInfo shopBusinessInfo = getObjectFromAttr(cardDTO, BUSINESS_HOUR_ATTR_KEY, ShopBusinessInfo.class);
        shop.setBusinessHour(shopBusinessInfo == null ? "" : shopBusinessInfo.getBusinessHour());
        shop.setBusinessStateEnum(formatCurrentState(shopBusinessInfo));
        shop.setAvgPrice(cardDTO.getAvgPrice());
        shop.setShopPower(formatShopPower(cardDTO));
        shop.setDistance(formatShopAddress(cardDTO.getDistanceStr()));
        shop.setShopPic(cardDTO.getHeadPic());
        return shop;
    }

    private String getMapUrl(ShopCardDTO shopCardDTO) {
        // 主站APP
        if (request.getClientTypeEnum().isInApp()) {
            return Optional.ofNullable(shopCardDTO).map(ShopCardDTO::getMapUrl).orElse("");
        }
        if (Double.compare(shopCardDTO.getLng(), 0.0) == 0 || Double.compare(shopCardDTO.getLat(), 0.0) == 0) {
            return "";
        }
        // H5场景
        String mtH5MapSchema = "https://m-map.meituan.com/navi/?key=m84068fb336d4f07b059e25ee07d509j&dest=%s&destName=%s&start=%s";
        if (!request.getClientTypeEnum().isInWxXCX()) {
            return String.format(mtH5MapSchema, getDestGps(shopCardDTO), shopCardDTO.getShopName(), getCurrentGps());
        }

        // 微信小程序场景
        String preSchema = request.getClientTypeEnum() == ClientTypeEnum.DP_XCX ? "/pages/webview/webview?url=" : "/index/pages/h5/h5?weburl=";
        try {
            return preSchema + URLEncoder.encode(String.format(mtH5MapSchema, getDestGps(shopCardDTO), shopCardDTO.getShopName(), getCurrentGps()), StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("DzDealShopsFacade.getMapUrl failed, shopCardDTO={}", JsonUtils.toJson(shopCardDTO), e);
        }
        return "";
    }

    private String getDestGps(ShopCardDTO shopCardDTO) {
        return String.format("%.6f,%.6f", shopCardDTO.getLng(), shopCardDTO.getLat());
    }

    private String getCurrentGps() {
        if (Double.compare(request.getUserLng(), 0.0) == 0 || Double.compare(request.getUserLat(), 0.0) == 0) {
            return "";
        }
        return String.format("%.6f,%.6f", request.getUserLng(), request.getUserLat());
    }

    private String formatShopAddress(String distanceStr) {
        if (StringUtils.isBlank(distanceStr)) {
            return "";
        }
        return String.format("距您 %s", distanceStr);
    }

    private int toClientType(ProductDetailPageRequest request) {
        if (request.getClientTypeEnum().isInApp()) {
            return ClientType.APP.getType();
        }
        if (request.getClientTypeEnum().isInWxXCX()) {
            return ClientType.MINI_PROGRAM.getType();
        }
        if (request.getClientTypeEnum() == ClientTypeEnum.MT_I) {
            return ClientType.M_SITE.getType();
        }
        return ClientType.APP.getType();
    }

    private String getMktSource(String dealSource) {
        if (StringUtils.isBlank(dealSource)) {
            return "";
        }
        Map<String, String> mapping = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", SOURCE_MAPPING_LION_KEY, String.class);
        return MapUtils.isEmpty(mapping) ? "" : mapping.get(dealSource);
    }

    private <T> T getObjectFromAttr(ShopCardDTO shopCardDTO, String attrKey, Class<T> clazz) {
        if (CollectionUtils.isEmpty(shopCardDTO.getAttrs())) {
            return null;
        }
        AttrItemDTO attrItemDTO = shopCardDTO.getAttrs().stream().filter(s -> attrKey.equals(s.getAttrName())).findFirst().orElse(null);
        if (attrItemDTO == null || StringUtils.isBlank(attrItemDTO.getAttrValue())) {
            return null;
        }
        return JsonUtils.fromJson(attrItemDTO.getAttrValue(), clazz);
    }

    private static int formatShopPower(ShopCardDTO shopCardDTO) {
        if (shopCardDTO == null) {
            return -1;
        }
        return (int) (NumberUtils.toDouble(shopCardDTO.getStarStr(), -0.1) * 10);
    }

    private long getShopId(ShopIdMapper shopIdMapper) {
        if (shopIdMapper == null) {
            return request.getPoiId();
        }
        return request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId();
    }

    private BusinessStateEnum formatCurrentState(ShopBusinessInfo shopBusinessInfo) {
        if (shopBusinessInfo == null || StringUtils.isBlank(shopBusinessInfo.getTodayFlag())) {
            return BusinessStateEnum.UNKNOWN;
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY); // 获取当前小时
        int minute = calendar.get(Calendar.MINUTE); // 获取当前分钟
        int index = 2 * hour + (minute >= 30 ? 1 : 0);
        char flag = shopBusinessInfo.getTodayFlag().charAt(index);
        return flag == '0' ? BusinessStateEnum.REST : BusinessStateEnum.OPENING;
    }

    @Data
    public static class ShopBusinessInfo {
        /**
         * https://km.sankuai.com/page/140458470
         * 当日营业时间文案
         */
        private String businessHour;
        /**
         * 今日营业标识
         */
        private String todayFlag;
        /**
         * 明日营业标识
         */
        private String tomorrowFlag;
    }


}
