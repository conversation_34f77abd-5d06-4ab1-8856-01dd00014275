package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/14 14:27
 */
@Data
public class RestaurantTimeDTO {

    @FieldDoc(description = "用餐起始时间")
    @JsonProperty("selfservicerestaurantstarttime")
    private String startTime;

    @FieldDoc(description = "用餐结束时间")
    @JsonProperty("selfservicerestaurantclosingtime")
    private String endTime;

    @FieldDoc(description = "时间片段")
    @JsonProperty("timeframe1")
    private String timeFrame;
}
