package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 职业教育:0元规划
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/10 20:42
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class}
)
@Slf4j
public class VocationalEduPlanReminderInfoBuilder extends AbstractReminderInfoBuilder {
    public static final String EDU_VOCATIONAL_ZERO_EFFECTIVE = "每人限购%s次*购买后商家将致电为您服务";

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if  (baseInfo == null) {
            return null;
        }
        ProductDetailReminderVO bookingDetailsReminderInfoVO = new ProductDetailReminderVO();
        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        bookingDetailsReminderInfoVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        if ( StringUtils.isEmpty(getEffectiveDateForVoCaEduZero(baseInfo))) {
            return null;
        }
        ReminderInfoUtils.buildReminderInfo(getEffectiveDateForVoCaEduZero(baseInfo)).ifPresent(contents::add);
        bookingDetailsReminderInfoVO.setContents(contents);
        return bookingDetailsReminderInfoVO;
    }

    private String getEffectiveDateForVoCaEduZero(ProductBaseInfo baseInfo) {
        if (baseInfo == null) {
            return "";
        }
        Integer maxPerUser = Optional.of(baseInfo)
                .map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getBuyRule)
                .map(DealGroupBuyRuleDTO::getMaxPerUser)
                .orElse(0);
        if ( maxPerUser < 1 ) {
            return EffectiveDateHelper.getEffectiveDate(baseInfo);
        }
        return String.format(EDU_VOCATIONAL_ZERO_EFFECTIVE, maxPerUser);
    }
}
