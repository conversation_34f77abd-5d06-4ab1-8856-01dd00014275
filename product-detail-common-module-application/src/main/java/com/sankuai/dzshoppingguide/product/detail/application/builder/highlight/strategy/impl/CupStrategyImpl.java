package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 拔罐
 *
 * @author: created by hang.yu on 2023/8/17 11:04
 */
@Component("cupStrategyImpl")
public class CupStrategyImpl extends AbstractMassageStrategy {

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 拔罐罐体
        return getAttrValue(serviceProjectAttrs, CUPPING_TOOL);
    }

}
