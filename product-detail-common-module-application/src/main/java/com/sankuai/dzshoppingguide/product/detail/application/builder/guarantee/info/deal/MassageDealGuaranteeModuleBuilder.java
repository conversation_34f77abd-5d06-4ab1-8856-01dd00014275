package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeLayerEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GuaranteeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FloatingLayerOpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.FeaturesLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 足疗保障模块构造器
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class, ProductAttrFetcher.class,
                ProductBaseInfoFetcher.class, GuaranteeTagFetcher.class
        }
)
public class MassageDealGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {



    @Override
    public ProductDetailGuaranteeVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductGuaranteeTagInfo productGuaranteeTagInfo = getDependencyResult(GuaranteeTagFetcher.class);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        // 价保
        if (LionConfigUtils.showPriceProtectionInfo(productCategory.getProductSecondCategoryId()) && GuaranteeUtils.checkPriceProtectionValid(productGuaranteeTagInfo.getPriceProtectionInfo())) {
            String text = "价保" + productGuaranteeTagInfo.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天";
            contents.add(new GuaranteeInstructionsContentVO(text, FONT_SIZE, GuaranteeLayerEnum.PRICE_PROTECTION.getKey()));
        }
        // 过期退、随时退
        List<GuaranteeInstructionsContentVO> standardGuaranteeContents = buildStandardGuaranteeContents();
        if (CollectionUtils.isNotEmpty(standardGuaranteeContents)) {
            contents.addAll(standardGuaranteeContents);
        }
        // 适用时间
        String applicableTime = getApplicableTime(productAttr, productCategory);
        if (StringUtils.isNotBlank(applicableTime)) {
            contents.add(new GuaranteeInstructionsContentVO(applicableTime, FONT_SIZE, GuaranteeLayerEnum.NONE.getKey()));
        }
        // 使用人群
        String applicablePeople = getApplicablePeople(productAttr, productCategory);
        if (StringUtils.isNotBlank(applicablePeople)) {
            contents.add(new GuaranteeInstructionsContentVO(applicablePeople, FONT_SIZE, GuaranteeLayerEnum.NONE.getKey()));
        }
        // 最后一个标签右边加上右箭头
        if (CollectionUtils.isNotEmpty(contents)) {
            addRightArrow(contents.get(contents.size() - 1));
        }
        FeaturesLayer floatingLayer = buildGuaranteeFloatingLayer(productCategory, productGuaranteeTagInfo);
        return new ProductDetailGuaranteeVO("保障", contents, floatingLayer, FloatingLayerOpenTypeEnum.OPEN_LAYER);
    }
}
