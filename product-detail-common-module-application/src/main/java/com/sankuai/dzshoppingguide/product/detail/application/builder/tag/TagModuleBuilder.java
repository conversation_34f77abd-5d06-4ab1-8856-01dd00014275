package com.sankuai.dzshoppingguide.product.detail.application.builder.tag;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.dto.StartFetcherReturnValue;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.TagModuleVO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 13:30
 */
@Builder(
        moduleKey = "module_tags",
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class TagModuleBuilder extends BaseBuilder<TagModuleVO> {

    @Override
    public TagModuleVO doBuild() {

        StartFetcherReturnValue startFetcherReturnValue = getDependencyResult(CommonModuleStarter.class);

        return null;
    }

}
