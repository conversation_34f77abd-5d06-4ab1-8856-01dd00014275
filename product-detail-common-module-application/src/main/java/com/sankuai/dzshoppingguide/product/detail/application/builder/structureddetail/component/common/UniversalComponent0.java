package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 10:49
 * @Description: TODO
 */
@Component
public class UniversalComponent0<T> implements CommonComponent0<T> {

    @Override
    public List<T> build(T vo) {
        if (vo == null) {
            return Lists.newArrayList();
        }
        return doBuild(vo);
    }

    protected List<T> doBuild(T vo) {
        return Lists.newArrayList(vo);
    }
}
