package com.sankuai.dzshoppingguide.product.detail.application.enums;

import lombok.Getter;

/**
 * 须知条场景枚举
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 13:08
 */
@Getter
public enum DealReminderInfoSceneEnum {
    MASSAGE_REMINDER_INFO(1,"按摩足疗"),
    MT_LIVE_MINI_APP_REMINDER_INFO(2,"美团美播小程序"),
    REASSURED_REPAIR_DEAL_REMINDER_INFO(3,"美团安心改"),
    FREE_VACCINE_DEAL_REMINDER_INFO(4,"疫苗0元预约场景"),
    FREE_DEAL_REMINDER_INFO(5,"免费团购"),
    EDU_ONLINE_DEAL_REMINDER_INFO_SCENE_ENUM(6,"在线教育类团购"),
    VOCATIONAL_EDU_PLAN_REMINDER_INFO_SCENE_ENUM(7,"职业教育:0元规划"),
    DEAL_TIMES_CARD_REMINDER_INFO(8,"团购次卡"),
    // 兜底场景必须排在最后面
    DEFAULT_REMINDER_INFO(9999,"兜底须知条场景"),
    ;

    /**
     * 场景顺序,不要修改已有的场景的顺序,场景执行器是按照顺序执行的,命中任意一个即返回结果
     * order越小,优先级越高
     */
    private final int order;
    private final String des;

    DealReminderInfoSceneEnum (int order,String des) {
        this.order = order;
        this.des = des;
    }
}
