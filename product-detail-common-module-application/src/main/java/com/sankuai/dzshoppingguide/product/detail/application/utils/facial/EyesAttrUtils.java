package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/16 17:41
 */
public class EyesAttrUtils {
    // 镜片
    /**
     * 品牌
     */
    public static Optional<DealDetailStructuredDetailVO> buildBrand(ProductAttr productAttr) {
        String lensSelection = productAttr.getSkuAttrFirstValue("LensSelection");
        if (StringUtils.isBlank(lensSelection)) {
            return Optional.empty();
        }

        String content = "";
        if (StringUtils.equals("指定价格范围内任选", lensSelection)) {
            String lensPrice = productAttr.getSkuAttrFirstValue("LensPrice");
            if (StringUtils.isBlank(lensPrice)) {
                return Optional.empty();
            }
            content = lensPrice + "元以内任选";
        }

        if (StringUtils.equals("指定品牌", lensSelection)) {
            List<String> brands = productAttr.getSkuAttrValues("lens_brand");
            if (CollectionUtils.isEmpty(brands)) {
                return Optional.empty();
            }

            if (brands.size() == 1) {
                content = brands.get(0);
            } else {
                content = String.join("、", brands) + " " + brands.size() + "选1";
            }
        }

        if (StringUtils.isBlank(content)) {
            return Optional.empty();
        }

        return DealDetailStructuredUtils.buildTitleAndContent("品牌", content);
    }

    /**
     * 型号
     */
    public static Optional<DealDetailStructuredDetailVO> buildModel(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("lens_model","型号", productAttr);
    }

    /**
     * 折射率
     */
    public static Optional<DealDetailStructuredDetailVO> buildRefractiveIndex(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("refractivity", "折射率", productAttr);
    }

    /**
     * 功能  lens_function
     */
    public static Optional<DealDetailStructuredDetailVO> buildFunction(ProductAttr productAttr) {
        List<String> functions = productAttr.getSkuAttrValues("lens_function");
        if (CollectionUtils.isEmpty(functions)) {
            return Optional.empty();
        }

        String content = String.join("、", functions);
        return DealDetailStructuredUtils.buildTitleAndContent("功能", content);
    }

    /**
     * 技术 lens_technology
     */
    public static Optional<DealDetailStructuredDetailVO> buildTechnology(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("lens_technology", "技术", productAttr);
    }

    /**
     * 适用度数
     */
    public static Optional<DealDetailStructuredDetailVO> buildApplicableDegree(ProductAttr productAttr) {
        List<String> applicableDegrees = productAttr.getSkuAttrValues("applicable_degrees");
        if (CollectionUtils.isEmpty(applicableDegrees)) {
            return Optional.empty();
        }

        List<String> degreeRanges = Lists.newArrayList();
        
        if (applicableDegrees.contains("近视")) {
            processMyopia(productAttr, degreeRanges);
        }

        if (applicableDegrees.contains("散光")) {
            processAstigmatism(productAttr, degreeRanges);
        }
        
        if (applicableDegrees.contains("远视")) {
            processHyperopia(productAttr, degreeRanges);
        }
        
        if (degreeRanges.isEmpty()) {
            return Optional.empty();
        }
        
        return DealDetailStructuredUtils.buildTitleAndContent("适用度数", String.join("、", degreeRanges));
    }

    private static void processMyopia(ProductAttr productAttr, List<String> degreeRanges) {
        String minDiameter = productAttr.getSkuAttrFirstValue("ApplicableMinDiameter");
        String maxValue = productAttr.getSkuAttrFirstValue("ApplicableMyopiaMaxValue");

        if (StringUtils.isBlank(minDiameter) && StringUtils.isBlank(maxValue)) {
            return;
        }
        
        if (StringUtils.isBlank(minDiameter) || "0".equals(minDiameter)) {
            degreeRanges.add("近视" + maxValue + "度以下");
        } else if (StringUtils.isBlank(maxValue)) {
            degreeRanges.add("近视" + minDiameter + "度以上");
        } else {
            degreeRanges.add("近视" + minDiameter + "-" + maxValue + "度");
        }
    }

    private static void processAstigmatism(ProductAttr productAttr, List<String> degreeRanges) {
        String minAstigmatism = productAttr.getSkuAttrFirstValue("ApplicableMinimumAstigmatism");
        String maxAstigmatism = productAttr.getSkuAttrFirstValue("ApplicableMaximumAstigmatism");
        
        if (StringUtils.isBlank(minAstigmatism) && StringUtils.isBlank(maxAstigmatism)) {
            return;
        }
        
        if (StringUtils.isBlank(minAstigmatism) || "0".equals(minAstigmatism)) {
            degreeRanges.add("散光" + maxAstigmatism + "度以下");
        } else if (StringUtils.isBlank(maxAstigmatism)) {
            degreeRanges.add("散光" + minAstigmatism + "度以上");
        } else {
            degreeRanges.add("散光" + minAstigmatism + "-" + maxAstigmatism + "度");
        }
    }

    private static void processHyperopia(ProductAttr productAttr, List<String> degreeRanges) {
        String minHyperopic = productAttr.getSkuAttrFirstValue("ApplicableHyperopicMinimumValue");
        String maxHyperopic = productAttr.getSkuAttrFirstValue("ApplicableHyperopicMaximumValue");
        
        if (StringUtils.isBlank(minHyperopic) && StringUtils.isBlank(maxHyperopic)) {
            return;
        }
        
        if (StringUtils.isBlank(minHyperopic) || "0".equals(minHyperopic)) {
            degreeRanges.add("远视" + maxHyperopic + "度以下");
        } else if (StringUtils.isBlank(maxHyperopic)) {
            degreeRanges.add("远视" + minHyperopic + "度以上");
        } else {
            degreeRanges.add("远视" + minHyperopic + "-" + maxHyperopic + "度");
        }
    }

    // 镜框
    /**
     * 镜框品牌
     */
    public static Optional<DealDetailStructuredDetailVO> buildFrameBrand(ProductAttr productAttr) {
        String eyeglassFrame = productAttr.getSkuAttrFirstValue("EyeglassFrame");
        if (StringUtils.isBlank(eyeglassFrame)) {
            return Optional.empty();
        }

        if ("指定价格范围内任选".equals(eyeglassFrame)) {
            String availablePriceRange = productAttr.getSkuAttrFirstValue("AvailablePriceRange");
            if (StringUtils.isNotBlank(availablePriceRange)) {
                return DealDetailStructuredUtils.buildTitleAndContent("镜框品牌", availablePriceRange + "元以内任选");
            }
        } else if ("指定品牌".equals(eyeglassFrame)) {
            String mirrorFrameBrandName = productAttr.getSkuAttrFirstValue("MirrorFrameBrandName");
            if (StringUtils.isNotBlank(mirrorFrameBrandName)) {
                return DealDetailStructuredUtils.buildTitleAndContent("镜框品牌", mirrorFrameBrandName);
            }
        }
        return Optional.empty();
    }

    public static String handleMultiValueAttr(ProductAttr productAttr, String attrName, String delimiter, String suffix) {
        List<String> attrValue = productAttr.getSkuAttrValue(attrName);
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        if (attrValue.size() == 1) {
            return attrValue.get(0);
        }
        return String.join(delimiter, attrValue) + suffix;
    }

    public static String buildOptometristBubblePopupData(ProductAttr productAttr, String attrName, Long shopCount) {
        List<String> attrValue = productAttr.getSkuAttrValue(attrName);
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        if (attrValue.size() == 1) {
            return StringUtils.EMPTY;
        }
        if (shopCount == null || shopCount <= 1) {
            return StringUtils.EMPTY;
        }
        return JSON.toJSONString(DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.BUBBLE_POPUP.getType())
                .content("该团购多门店可用，实际验光操作人员可联系商家确认")
                .build());
    }
}
