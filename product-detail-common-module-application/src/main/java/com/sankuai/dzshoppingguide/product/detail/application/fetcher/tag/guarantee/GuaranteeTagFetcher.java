package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.QueryExtKeyEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
import com.sankuai.nib.sp.common.enums.Owner;
import com.sankuai.nib.sp.common.enums.TradeType;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.*;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_EXERCISE_GUARANTEE_NOT_CONTINUOUS_MONTHLY;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.PRODUCT;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障标签
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class, ProductCategoryFetcher.class, PlatformProductIdMapperFetcher.class, ShopIdMapperFetcher.class}
)
public class GuaranteeTagFetcher extends NormalFetcherContext<ProductGuaranteeTagInfo> {

    private static final Set<ClientTypeEnum> MAIN_WEB_CLIENT_TYPE = Sets.newHashSet(ClientTypeEnum.MT_I, ClientTypeEnum.DP_M, ClientTypeEnum.MT_PC, ClientTypeEnum.DP_PC);

    /**
     * 口腔-放心种植标签
     */
    private static final Set<Integer> SAFE_IMPLANT_TAGS = Sets.newHashSet(
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_3_YEARS_GUARANTEE.getCode(),
            GuaranteeTagNameEnum.REASSURING_DENTAL_IMPLANT_5_YEARS_GUARANTEE.getCode());

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ProductGuaranteeTagInfo> doFetch() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        PlatformProductIdMapper platformProductIdMapper = getDependencyResult(PlatformProductIdMapperFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        SessionContextDTO sessionContextDTO = buildSessionContextDTO(productCategory);
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = buildQueryGuaranteeTagRequest(productCategory, platformProductIdMapper, shopIdMapper);
        return compositeAtomService.batchQueryGuaranteeTag(sessionContextDTO, guaranteeTagRequest).thenApply(result -> {
            if (CollectionUtils.isEmpty(result)) {
                return new ProductGuaranteeTagInfo();
            }
            ProductGuaranteeTagInfo productGuaranteeTagInfo = new ProductGuaranteeTagInfo();
            // 价保
            ObjectGuaranteeTagDTO priceProtectionTag = getPriceProtectionTag(result);
            productGuaranteeTagInfo.setPriceProtectionInfo(priceProtectionTag);
            // 买贵必赔
            ObjectGuaranteeTagDTO bestPriceGuaranteeTag = getBestPriceGuaranteeTag(result);
            productGuaranteeTagInfo.setBestPriceGuaranteeInfo(bestPriceGuaranteeTag);
            // 安心医
            ObjectGuaranteeTagDTO safeMedicalGuaranteeTag = getSafeMedicalGuaranteeTag(result);
            productGuaranteeTagInfo.setSafeMedicalGuaranteeInfo(safeMedicalGuaranteeTag);
            //安心学
            ObjectGuaranteeTagDTO safeLearnGuaranteeTag = getSafeLearnGuaranteeTag(result);
            productGuaranteeTagInfo.setSafeLearnGuaranteeInfo(safeLearnGuaranteeTag);
            // 放心种植
            TagDTO safeImplantTag = getSafeImplantGuaranteeTag(result);
            productGuaranteeTagInfo.setSafeImplantTagDTO(safeImplantTag);
            //安心练
            ObjectGuaranteeTagDTO safeExerciseGuaranteeTag = getSafeExerciseGuaranteeTag(result);
            productGuaranteeTagInfo.setSafeExerciseGuaranteeInfo(safeExerciseGuaranteeTag);
            return productGuaranteeTagInfo;
        });
    }

    private TagDTO getSafeImplantGuaranteeTag(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getTags)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(tag -> SAFE_IMPLANT_TAGS.contains(tag.getCode()))
                .findFirst()
                .orElse(null);
    }

    private ObjectGuaranteeTagDTO getSafeLearnGuaranteeTag(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(tag -> Objects.equals(tag.getGuaranteeType(), ANXIN_LEARNING_GUARANTEE.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安心练
     * @param guaranteeTags
     * @return
     */
    private ObjectGuaranteeTagDTO getSafeExerciseGuaranteeTag(List<ObjectGuaranteeTagDTO> guaranteeTags){
        return guaranteeTags.stream()
                .filter( tag -> GuaranteeTypeEnum.isAnXinExerciseGuarantee(tag.getGuaranteeType()))
                .findFirst()
                .orElse(null);

    }

    private ObjectGuaranteeTagDTO getSafeMedicalGuaranteeTag(List<ObjectGuaranteeTagDTO> guaranteeTags) {
            return guaranteeTags.stream()
                .filter(tag -> Objects.equals(tag.getGuaranteeType(), ANXIN_MEDICAL_GUARANTEE.getCode()))
                .findFirst()
                .orElse(null);
    }

    private ObjectGuaranteeTagDTO getBestPriceGuaranteeTag(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(tag -> Objects.equals(tag.getGuaranteeType(), BEST_PRICE_GUARANTEE.getCode()))
                .findFirst()
                .orElse(null);
    }

    private ObjectGuaranteeTagDTO getPriceProtectionTag(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(tag -> Objects.equals(tag.getGuaranteeType(), PRICE_PROTECTION.getCode()))
                .findFirst()
                .orElse(null);
    }

    private BatchQueryGuaranteeTagRequest buildQueryGuaranteeTagRequest(ProductCategory productCategory, PlatformProductIdMapper platformProductIdMapper, ShopIdMapper shopIdMapper) {
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = new BatchQueryGuaranteeTagRequest();
        guaranteeTagRequest.setObjects(getObjects(productCategory.getProductSecondCategoryId(), platformProductIdMapper.getPlatformProductId(), shopIdMapper.getMtBestShopId()));
        guaranteeTagRequest.setGuaranteeTypes(getGuaranteeTypes());
        guaranteeTagRequest.setUserInfo(getUserInfoDTO(request.getClientTypeEnum(), request.getPageSource()));
        guaranteeTagRequest.setQueryTagOption(getQueryTagOption());
        return guaranteeTagRequest;
    }

    public QueryTagOptionDTO getQueryTagOption(){
        QueryTagOptionDTO queryTagOptionDTO=new QueryTagOptionDTO();
        queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
        return queryTagOptionDTO;
    }

    public UserInfoDTO getUserInfoDTO(ClientTypeEnum clientTypeEnum, String pageSource) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        ChannelDTO channelDTO = new ChannelDTO();

        PlatformEnum platformEnum;
        if (clientTypeEnum.isMtClientType()) {
            platformEnum = PlatformEnum.MT_PLATFORM;
        } else {
            platformEnum = PlatformEnum.DP_PLATFORM;
        }
        channelDTO.setPlatform(platformEnum.getCode());

        TerminalTypeEnum terminalTypeEnum;
        if (clientTypeEnum.isInApp()) {
            terminalTypeEnum = TerminalTypeEnum.APP;
        } else if (clientTypeEnum.isInWxXCX()) {
            terminalTypeEnum = TerminalTypeEnum.APPLETS;
        } else if (MAIN_WEB_CLIENT_TYPE.contains(clientTypeEnum)) {
            terminalTypeEnum = TerminalTypeEnum.PC;
        } else {
            terminalTypeEnum = TerminalTypeEnum.MOBILE;
        }
        channelDTO.setTerminalType(terminalTypeEnum.getCode());

        ChannelNoEnum channelNoEnum;
        if (RequestSourceEnum.fromLive(pageSource)) {
            channelNoEnum = ChannelNoEnum.LIVE_STREAMING;
        } else {
            channelNoEnum = ChannelNoEnum.UNKNOWN;
        }
        channelDTO.setChannelNo(channelNoEnum.getCode());

        userInfoDTO.setChannel(channelDTO);
        return userInfoDTO;
    }

    private Set<Integer> getGuaranteeTypes() {
        Set<Integer> guaranteeTypes = new HashSet<>();
        guaranteeTypes.add(PRICE_PROTECTION.getCode());
        guaranteeTypes.add(BEST_PRICE_GUARANTEE.getCode());
        // 安心医、放心种植用的都是 ANXIN_MEDICAL_GUARANTEE
        guaranteeTypes.add(ANXIN_MEDICAL_GUARANTEE.getCode());
        guaranteeTypes.add(ANXIN_LEARNING_GUARANTEE.getCode());
        // 安心练需求
        guaranteeTypes.add(ANXIN_EXERCISE_GUARANTEE_CONTINUOUS_MONTHLY.getCode());
        guaranteeTypes.add(ANXIN_EXERCISE_GUARANTEE_NOT_CONTINUOUS_MONTHLY.getCode());
        return guaranteeTypes;
    }

    public Set<GuaranteeObjectQueryDTO> getObjects(int secondCategoryId, long platformProductId, long mtShopId) {
        if (platformProductId <= 0L) {
            return new HashSet<>();
        }
        Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
        GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();
        object.setObjectId(String.valueOf(platformProductId));
        object.setObjectType(PRODUCT.getCode());
        objects.add(object);
        object.setExt(getExt(secondCategoryId, mtShopId));
        return objects;
    }

    private Map<String, String> getExt(int secondCategoryId, long mtShopId) {
        //齿科
        if (secondCategoryId == 506){
            Map<String, String> ext = new HashMap<>();
            ext.put(QueryExtKeyEnum.POI_ID.getCode(), String.valueOf(mtShopId));
            return ext;
        }
        return Maps.newHashMap();
    }

    private SessionContextDTO buildSessionContextDTO(ProductCategory productCategory) {
        SessionContextDTO sessionContext = new SessionContextDTO();
        sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
        // 判断是否是教育行业
        if (LionConfigUtils.isSafeLearnCategoryIds(productCategory.getProductSecondCategoryId())){
            sessionContext.setTradeType(TradeType.COUNT_CARD.getCode());
        }else {
            // 原有逻辑
            sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
        }

        return sessionContext;
    }

}
