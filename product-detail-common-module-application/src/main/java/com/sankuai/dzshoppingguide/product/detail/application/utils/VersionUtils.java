package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025-02-21
 * @desc  版本号工具类
 */
public class VersionUtils {

    /**
     * 判断当前版本是否 >= 目标版本
     * @param currentVersion 当前版本（如 "12.27.400"）
     * @param targetVersion  目标版本（如 "12.27.400"）
     * @return 当前版本 >= 目标版本时返回 true
     */
    public static boolean isGreaterThanOrEqual(String currentVersion, String targetVersion) {
        return compareVersions(currentVersion, targetVersion) >= 0;
    }

    /**
     * 判断当前版本是否 <= 目标版本
     * @param currentVersion 当前版本（如 "11.31.0"）
     * @param targetVersion  目标版本（如 "11.31.0"）
     * @return 当前版本 <= 目标版本时返回 true
     */
    public static boolean isLessThanOrEqual(String currentVersion, String targetVersion) {
        return compareVersions(currentVersion, targetVersion) <= 0;
    }

    /**
     * 核心比较逻辑：比较两个版本号
     * @return 正数表示 current > target，负数表示 current < target，0 表示相等
     */
    private static int compareVersions(String currentVersion, String targetVersion) {
        int[] current = parseVersion(currentVersion);
        int[] target = parseVersion(targetVersion);

        int maxLength = Math.max(current.length, target.length);
        for (int i = 0; i < maxLength; i++) {
            int currPart = (i < current.length) ? current[i] : 0;
            int targPart = (i < target.length) ? target[i] : 0;

            if (currPart != targPart) {
                // 直接返回差值，由外层判断正负
                return currPart - targPart;
            }
        }
        // 完全相等
        return 0;
    }

    // 解析版本号为整数数组（如 "12.27.400" → [12, 27, 400]）
    private static int[] parseVersion(String version) {
        if (version == null || version.isEmpty()) {
            return new int[0];
        }
        return Arrays.stream(version.split("\\."))
                .mapToInt(Integer::parseInt)
                .toArray();
    }
}