package com.sankuai.dzshoppingguide.product.detail.application.builder.shop;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShop;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.theme.ShopCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.theme.ShopThemeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.shop.AvailableShopVO;
import org.apache.commons.lang.StringUtils;

@Builder(
        moduleKey = ModuleKeyConstants.AVAILABLE_SHOP,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductBestShopFetcher.class,
                ShopThemeFetcher.class,
                DetailIMFetcher.class,
        }
)
public class AvailableShopBuilder extends BaseBuilder<AvailableShopVO> {

    @Override
    public AvailableShopVO doBuild() {
        ProductBestShop productBestShop = getDependencyResult(ProductBestShopFetcher.class);
        ShopCard shopCard = getDependencyResult(ShopThemeFetcher.class);
        if (shopCard == null) {
            return null;
        }
        long shopCount = getShopCount(productBestShop);
        AvailableShopVO availableShopVO = new AvailableShopVO();
        availableShopVO.setModuleName("适用门店");
        availableShopVO.setShopId(shopCard.getShopId());
        availableShopVO.setTotalCount(shopCount);
        availableShopVO.setShopPic(shopCard.getShopPic());
        availableShopVO.setDistance(shopCard.getDistance());
        availableShopVO.setShopPower(shopCard.getShopPower());
        availableShopVO.setAvgPrice(shopCard.getAvgPrice());
        availableShopVO.setBusinessHour(getBusinessInfo(shopCard));
        availableShopVO.setShopName(shopCard.getShopName());
        availableShopVO.setPhoneNos(shopCard.getPhoneNos());
        availableShopVO.setImUrl(getImUrl());
        availableShopVO.setShopUrl(shopCard.getShopUrl());
        availableShopVO.setShopMapUrl(shopCard.getShopMapUrl());
        availableShopVO.setShopListUrl(ShopUrlUtils.getShopListUrl(request));
        return availableShopVO;
    }

    private String getBusinessInfo(ShopCard shopCard) {
        return String.format("%s %s", shopCard.getBusinessStateEnum().getDesc(), StringUtils.isNotBlank(shopCard.getBusinessHour()) ? shopCard.getBusinessHour() : "");
    }

    private String getImUrl() {
        DetailIMResult detailImResult = getDependencyResult(DetailIMFetcher.class);
        if (detailImResult == null || StringUtils.isBlank(detailImResult.getImUrl())) {
            return "";
        }
        return detailImResult.getImUrl();
    }

    private long getShopCount(ProductBestShop productBestShop) {
        if (productBestShop == null) {
            return 1;
        }
        return productBestShop.getTotalCount();
    }


}
