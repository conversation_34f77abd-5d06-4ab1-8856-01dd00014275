package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.DoctorReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.TechnicianUrlReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.technician.DoctorModuleVO;
import com.sankuai.technician.query.center.result.TechItem;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.ThirdCategoryIdConstant.*;

/**
 * 医师模块
 */
@Component
public class DoctorModuleBuilder extends AbstractDealDetailBuilder {


    private static final List<Integer> THIRD_CATEGORY_LIST = Lists.newArrayList(
            MYOPIA_PRE_SURGERY_EXAM,
            DRY_EYE_EXAM,
            SMILE_SURGERY,
            FEMTOSECOND_SURGERY,
            EXCIMER_SURGERY,
            CRYSTAL_SURGERY,
            DRY_EYE_LASER,
            DRY_EYE_THERAPY);


    @Override
    public List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        if (!enable(context.getProductCategory())) {
            return null;
        }
        if (context.getDoctorReturnValue() == null || CollectionUtils.isEmpty(context.getDoctorReturnValue().getTechItems())) {
            return null;
        }
        DoctorModuleVO doctorModuleVO = buildDoctorModuleVO(context);
        if (doctorModuleVO == null) {
            return null;
        }
        return Lists.newArrayList(
                DealDetailStructuredDetailVO.builder()
                .content(JSON.toJSONString(doctorModuleVO))
                .type(ViewComponentTypeEnum.CRAFTSMAN.getType())
                .build(),
                DealDetailStructuredUtils.buildLimiter()
                );
    }

    private DoctorModuleVO buildDoctorModuleVO(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> doctorList = buildDoctorList(context.getDoctorReturnValue(), context.getTechnicianUrlReturnValue());
        if (CollectionUtils.isEmpty(doctorList)) {
            return null;
        }
        DoctorModuleVO doctorModuleVO = new DoctorModuleVO();
        doctorModuleVO.setTitle(String.format("%s(%d)", getTitlePrefix(context.getProductCategory()), context.getDoctorReturnValue().getTechItems().size()));
        doctorModuleVO.setContent(JSON.toJSONString(doctorList));
        doctorModuleVO.setJumpText("");
        doctorModuleVO.setJumpUrl("");
        return doctorModuleVO;
    }

    private List<DealDetailStructuredDetailVO> buildDoctorList(DoctorReturnValue doctorReturnValue, TechnicianUrlReturnValue technicianUrlReturnValue) {
        return doctorReturnValue.getTechItems().stream().map(i -> buildDoctor(i, technicianUrlReturnValue)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealDetailStructuredDetailVO buildDoctor(TechItem techItem, TechnicianUrlReturnValue technicianUrlReturnValue) {
        if (MapUtils.isEmpty(techItem.getValues())) {
            return null;
        }
        Map<String, Object> valuesMap = techItem.getValues();
        return DealDetailStructuredDetailVO.builder()
                .icon(String.valueOf(valuesMap.getOrDefault("es.field.photourl", ""))) // 头像
                .title(String.valueOf(valuesMap.getOrDefault("es.field.name", ""))) // 姓名
                .content(String.valueOf(valuesMap.getOrDefault("es.eav.online.1001", ""))) // 职称
                .detail("") // 科室
                .jumpUrl(getDetailUrl(techItem.getTechId(), technicianUrlReturnValue))
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .itemId(String.valueOf(techItem.getTechId()))
                .build();
    }

    private String getDetailUrl(int techId, TechnicianUrlReturnValue technicianUrlReturnValue) {
        if (technicianUrlReturnValue == null || MapUtils.isEmpty(technicianUrlReturnValue.getUrlMap())) {
            return "";
        }
        return technicianUrlReturnValue.getUrlMap().get(techId);
    }

    private boolean enable(ProductCategory productCategory) {
        return THIRD_CATEGORY_LIST.contains(productCategory.getProductThirdCategoryId());
    }

    private String getTitlePrefix(ProductCategory productCategory) {
        switch (productCategory.getProductThirdCategoryId()) {
            case MYOPIA_PRE_SURGERY_EXAM:
            case DRY_EYE_EXAM:
                return "可检查医师";
            case SMILE_SURGERY:
            case FEMTOSECOND_SURGERY:
            case EXCIMER_SURGERY:
            case CRYSTAL_SURGERY:
                return "可主刀医师";
            case DRY_EYE_LASER:
            case DRY_EYE_THERAPY:
                return "可治疗医师";
            default:
                return "可选医师";
        }
    }

}
