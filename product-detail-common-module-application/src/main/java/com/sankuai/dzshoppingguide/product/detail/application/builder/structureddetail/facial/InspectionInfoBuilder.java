package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.ThirdCategoryIdConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检查项目
 */
@Slf4j
@Component
public class InspectionInfoBuilder extends AbstractDealDetailBuilder  {

    private static final String CHECK_PROJECT_DURATION = "CheckProjectDuration";
    private static final String OPTOMETRY_PROCESS = "optometry_process";
    private static final String DEFAULT_DURATION_NOTE = "注：服务时长仅供参考，由于个体差异，实际时长请以到店服务为准";
    private static final List<Integer> POP_UP_THIRD_CATEGORY = Lists.newArrayList(
            ThirdCategoryIdConstant.CONTACT_LENS_EXAMINATION,
            ThirdCategoryIdConstant.CHILD_REGULAR_GLASSES,
            ThirdCategoryIdConstant.MEDICAL_GLASSES,
            ThirdCategoryIdConstant.DEFOCUS_GLASSES,
            ThirdCategoryIdConstant.OK_GLASSES,
            ThirdCategoryIdConstant.DEFOCUS_SOFT_LENS,
            ThirdCategoryIdConstant.RGP_LENS,
            ThirdCategoryIdConstant.OTHER_CONTACT_LENS,
            ThirdCategoryIdConstant.VISION_TRAINING,
            ThirdCategoryIdConstant.SMILE_SURGERY,
            ThirdCategoryIdConstant.FEMTOSECOND_SURGERY,
            ThirdCategoryIdConstant.EXCIMER_SURGERY,
            ThirdCategoryIdConstant.CRYSTAL_SURGERY,
            ThirdCategoryIdConstant.DRY_EYE_LASER,
            ThirdCategoryIdConstant.DRY_EYE_THERAPY
    );


    @Override
    public List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        if (context == null || context.getProductCategory() == null) {
            return null;
        }
        
        int thirdCategoryId = context.getProductCategory().getProductThirdCategoryId();

        switch (thirdCategoryId) {
            // 眼镜-眼光、儿童眼光
            case ThirdCategoryIdConstant.OPTICAL_EXAMINATION:
            case ThirdCategoryIdConstant.CHILD_OPTICAL_EXAMINATION: return getGlassInspection(context);
            // 眼科-儿童医学验光、成人医学验光、近视术前检查、干眼检查
            case ThirdCategoryIdConstant.CHILD_MEDICAL_OPTICAL:
            case ThirdCategoryIdConstant.ADULT_MEDICAL_OPTICAL:
            case ThirdCategoryIdConstant.MYOPIA_PRE_SURGERY_EXAM:
            case ThirdCategoryIdConstant.DRY_EYE_EXAM: return getEyeInspection(context);
            default: return null;
        }
    }

    private List<DealDetailStructuredDetailVO> getEyeInspection(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> inspectionList = Lists.newArrayList();

        // 2.检查子项
        inspectionList.addAll(buildSingleInspection(context, "视力检查", "VisualCheck", "VisionCheckProcess", "VisionCheckInstructions", "VisualExamDuration2", "VisualExamDurationRangeMax"));
        inspectionList.addAll(buildSingleInspection(context, "眼科检查", "EyeCheck", "EyeCheckProcess", "EyeCheckInstructions", "Ophthalmicexaminationduration", "OphthalmicExaminationDurationRangeMax"));
        inspectionList.addAll(buildSingleInspection(context, "整体健康评估", "HealthAssessment", "HealthAssessmentProcess", "HealthAssessmentNote", "HealthAssessmentDuration", "HealthAssessmentDurationRangeMax"));
        List<DealDetailStructuredDetailVO> inspectionFinal = inspectionList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inspectionFinal)) {
            return null;
        }
        int visualExamDuration = getAvgDurationFromAttr(context.getProductAttr(), "VisualExamDuration2", "VisualExamDurationRangeMax");
        int ophthalmicExaminationDuration = getAvgDurationFromAttr(context.getProductAttr(), "Ophthalmicexaminationduration", "OphthalmicExaminationDurationRangeMax");
        int healthAssessmentDuration = getAvgDurationFromAttr(context.getProductAttr(), "HealthAssessmentDuration", "HealthAssessmentDurationRangeMax");
        // 1.标题
        inspectionFinal.add(0, DealDetailStructuredDetailVO.builder()
                .title("检查项目")
                .type(ViewComponentTypeEnum.TITLE.getType())
                .detail(getTotalDurationFromAttr(visualExamDuration, ophthalmicExaminationDuration, healthAssessmentDuration))
                .build());
        inspectionFinal.add(buildBottomNote());

        // 3.分隔符
        inspectionFinal.add(DealDetailStructuredUtils.buildCurrentFoldLimiter());
        return inspectionFinal;
    }

    private int getAvgDurationFromAttr(ProductAttr productAttr, String durationKey, String maxDurationKey) {
        String duration = productAttr.getSkuAttrFirstValue(durationKey);
        String maxDuration = productAttr.getSkuAttrFirstValue(maxDurationKey);
        if (StringUtils.isBlank(duration) && StringUtils.isBlank(maxDuration)) {
            return 0;
        }
        try {
            if (StringUtils.isNotBlank(duration) && StringUtils.isNotBlank(maxDuration)
                    && NumberUtils.isCreatable(duration) && NumberUtils.isCreatable(maxDuration)) {
                return Math.round((NumberUtils.toInt(duration) + NumberUtils.toInt(maxDuration)) / 2f);
            }
            if (StringUtils.isNotBlank(duration) && NumberUtils.isCreatable(duration)) {
                return NumberUtils.toInt(duration);
            }
            return StringUtils.isNotBlank(maxDuration) && NumberUtils.isCreatable(maxDuration) ? NumberUtils.toInt(maxDuration) : 0;
        } catch (Exception e) {
            log.error("getAvgDurationFromAttr error", e);
            return 0;
        }
    }

    private String getTotalDurationFromAttr(int... durations) {
        int total = 0;
        for (int duration : durations) {
            total += duration;
        }
        if (total == 0) {
            return "";
        }
        return String.format("共%s分钟", total);
    }

    public List<DealDetailStructuredDetailVO> getEyeInspectionPopUp(DealDetailBuildContext context) {
        if (!POP_UP_THIRD_CATEGORY.contains(context.getProductCategory().getProductThirdCategoryId())) {
            return null;
        }

        List<DealDetailStructuredDetailVO> inspectionList = Lists.newArrayList();
        inspectionList.addAll(buildSingleInspection(context, "视力检查", "VisualCheck", "VisionCheckProcess", "VisionCheckInstructions", "", ""));
        inspectionList.addAll(buildSingleInspection(context, "眼科检查", "EyeCheck", "EyeCheckProcess", "EyeCheckInstructions", "", ""));
        inspectionList.addAll(buildSingleInspection(context, "整体健康评估", "HealthAssessment", "HealthAssessmentProcess", "HealthAssessmentNote", "", ""));
        inspectionList.addAll(buildSingleInspection(context, "干眼理疗", "EyeTherapy", "EyeTherapyProcess", "EyeTherapyInstructions", "", ""));
        List<DealDetailStructuredDetailVO> inspectionFinal = inspectionList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inspectionFinal)) {
            return null;
        }
        return inspectionFinal;
    }

    private List<DealDetailStructuredDetailVO> buildSingleInspection(DealDetailBuildContext context, String inspectionName, String attrKey, String subTitleKey, String subDescKey, String durationKey, String maxDurationKey) {
        List<String> values = context.getProductAttr().getSkuAttrValue(attrKey);
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        // 一级文案
        DealDetailStructuredDetailVO mainRow = DealDetailStructuredDetailVO.builder()
                .title(inspectionName)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .detail(StringUtils.isBlank(durationKey) ? getDurationFromServiceProcess(context, inspectionName) : getDurationFromAttr(context, durationKey, maxDurationKey))
                .backgroundColor("#f6f6f6")
                .endBackgroundColor("#fdfdfd")
                .build();
        result.add(mainRow);
        // 二级文案 +  二级描述
        for (String value : values) {
            JSONObject jsonObject = null;
            try{
                jsonObject = JSONObject.parseObject(value);
            } catch (Exception e){
                log.error("InspectionInfoBuilder.buildSingleInspection json:{} parse error ", value, e);
                jsonObject = null;
            }
            if (jsonObject == null) {
                continue;
            }
            if (StringUtils.isBlank(jsonObject.getString(subTitleKey))) {
                continue;
            }
            result.add(DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                    .titleFontWeight("600")
                    .content(jsonObject.getString(subTitleKey))
                    .prefix(ViewComponentTypeEnum.PREFIX_DOT.getType())
                    .build());
            if (StringUtils.isBlank(jsonObject.getString(subDescKey))) {
                continue;
            }
            result.add(DealDetailStructuredDetailVO.builder()
                    .title(inspectionName)
                    .type(ViewComponentTypeEnum.SUB_TEXT.getType())
                    .content(jsonObject.getString(subDescKey))
                    .build());
        }
        return result;
    }

    private String getDurationFromAttr(DealDetailBuildContext context, String durationKey, String maxDurationKey) {
        String duration = context.getProductAttr().getSkuAttrFirstValue(durationKey);
        String maxDuration = context.getProductAttr().getSkuAttrFirstValue(maxDurationKey);
        if (StringUtils.isBlank(duration) && StringUtils.isBlank(maxDuration)) {
            return "";
        }
        if (StringUtils.isNotBlank(duration) && StringUtils.isNotBlank(maxDuration)) {
            if (Objects.equals(duration, maxDuration)) {
                return String.format("%s分钟", duration);
            }
            return String.format("%s-%s分钟", duration, maxDuration);
        }
        if (StringUtils.isNotBlank(duration)) {
            return String.format("%s分钟", duration);
        }
        return String.format("%s分钟", maxDuration);
    }

    private String getDurationFromServiceProcess(DealDetailBuildContext context, String inspectionName) {
        List<String> values = context.getProductAttr().getSkuAttrValue("serviceProcess");
        Map<String, ServiceProcess> serviceProcessMap = values.stream().filter(StringUtils::isNotBlank).map(v ->  JSON.parseObject(v, ServiceProcess.class)).filter(Objects::nonNull).collect(Collectors.toMap(ServiceProcess::getOphthalmic_services_process, Function.identity(), (k1, k2) -> k1));
        if (MapUtils.isEmpty(serviceProcessMap) || serviceProcessMap.get(inspectionName) == null || StringUtils.isBlank(serviceProcessMap.get(inspectionName).getDuration())) {
            return "";
        }
        return String.format("%s分钟", serviceProcessMap.get(inspectionName).getDuration());
    }


    private List<DealDetailStructuredDetailVO> getGlassInspection(DealDetailBuildContext context) {
        if (context == null || context.getProductAttr() == null) {
            return null;
        }
        
        // Get optometry process
        List<String> optometryProcess = context.getProductAttr().getSkuAttrValue(OPTOMETRY_PROCESS);
        if (CollectionUtils.isEmpty(optometryProcess)) {
            return null;
        }
        
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        
        // First row: Title and duration
        String duration = context.getProductAttr().getSkuAttrFirstValue(CHECK_PROJECT_DURATION);
        String durationText = StringUtils.isBlank(duration) ? "" : duration + "分钟";
        
        DealDetailStructuredDetailVO titleRow = DealDetailStructuredDetailVO.builder()
                .title("检查项目")
                .content(durationText)
                .type(ViewComponentTypeEnum.TITLE.getType())
                .build();
        result.add(titleRow);
        
        // Middle rows: Inspection items
        for (String item : optometryProcess) {
            if (StringUtils.isNotBlank(item)) {
                DealDetailStructuredDetailVO itemRow = DealDetailStructuredDetailVO.builder()
                        .content(item)
                        .prefix(ViewComponentTypeEnum.PREFIX_DOT.getType())
                        .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                        .titleFontWeight("600")
                        .build();
                result.add(itemRow);
            }
        }
        
        // Last row: Default note
        result.add(buildBottomNote());
        // 3.分隔符
        result.add(DealDetailStructuredUtils.buildCurrentFoldLimiter());
        return result;
    }


    private DealDetailStructuredDetailVO buildBottomNote() {
        return DealDetailStructuredDetailVO.builder()
                .content(DEFAULT_DURATION_NOTE)
                .type(ViewComponentTypeEnum.ANNOTATION.getType())
                .build();
    }

    @Data
    private static class ServiceProcess {
        private String ophthalmic_services_process;
        private String duration;
    }
}
