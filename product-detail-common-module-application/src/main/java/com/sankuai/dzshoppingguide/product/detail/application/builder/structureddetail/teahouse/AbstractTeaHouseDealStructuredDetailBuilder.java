package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructContentModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructModel;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-23
 * @desc 茶馆 棋牌套餐 629
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                AdditionInfoFetcher.class
        }
)
abstract class AbstractTeaHouseDealStructuredDetailBuilder extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> {

    private static final String RICH_TEXT = "richtext";

    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductServiceProject productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        AdditionInfoResult additionInfo = getDependencyResult(AdditionInfoFetcher.class);
        if (Objects.isNull(productServiceProject) || Objects.isNull(productAttr)) {
            return null;
        }

        // 构造服务详情模块
        List<DealDetailStructuredDetailVO> serviceDetailModule = buildServiceDetailModule(productAttr, productServiceProject, additionInfo);
        ModuleDetailStructuredDetailVO result = new ModuleDetailStructuredDetailVO();
        result.setDealDetails(serviceDetailModule);
        return result;
    }

    private List<DealDetailStructuredDetailVO> buildServiceDetailModule(ProductAttr productAttr, ProductServiceProject serviceProject, AdditionInfoResult additionInfo) {

        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        // 1. 套餐信息、棋牌套餐、门票套餐、体验项目
        List<DealDetailStructuredDetailVO> packageDetailsModule = buildPackageDetailsModule(productAttr);
        safeAddAllWithNewLine(result, packageDetailsModule, false);

        // 2. 茶饮
        List<DealDetailStructuredDetailVO> teaModule = buildTeaModule(serviceProject);
        safeAddAllWithNewLine(result, teaModule, true);

        // 3. 补充说明
        List<DealDetailStructuredDetailVO> additionalInfoModule = buildAdditionalInfo(additionInfo);
        safeAddAllWithNewLine(result, additionalInfoModule, false);

        // 过滤空结果
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public void safeAddAllWithNewLine(List<DealDetailStructuredDetailVO> result, List<DealDetailStructuredDetailVO> addition, boolean newLine){
        if (Objects.nonNull(result) && CollectionUtils.isNotEmpty(addition)) {
            result.addAll(addition);
        }
        if (newLine && CollectionUtils.isNotEmpty(result)) {
            // 换行符
            result.add(buildStructuredDetailVO(null, null, null, ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType()));
        }
    }

    private List<DealDetailStructuredDetailVO> buildPackageDetailsModule(ProductAttr productAttr) {
        List<DealDetailStructuredDetailVO> serviceProcessModule = Lists.newArrayList();
        // 构造套餐标题模块
        DealDetailStructuredDetailVO packageDetailsTitle = buildPackageDetailsTitle(productAttr);
        serviceProcessModule.add(packageDetailsTitle);
        // 构造套餐详情
        List<DealDetailStructuredDetailVO> packageDetails = buildPackageDetails(productAttr);
        // 构造套餐详情
        safeAddAllWithNewLine(serviceProcessModule, packageDetails, false);
        return serviceProcessModule;
    }

    private List<DealDetailStructuredDetailVO> buildTeaModule(ProductServiceProject serviceProject) {

        if (Objects.isNull(serviceProject) || Objects.isNull(serviceProject.getServiceProject()) || CollectionUtils.isEmpty(serviceProject.getServiceProject().getOptionGroups())) {
            return null;
        }
        OptionalServiceProjectGroupDTO serviceProjectGroupDTO = serviceProject.getServiceProject().getOptionGroups().get(0);
        // 可选数
        int optionalCount = serviceProjectGroupDTO.getOptionalCount()!=null ? serviceProjectGroupDTO.getOptionalCount() : 0;
        int total = serviceProjectGroupDTO.getGroups().size();
        List<DealDetailStructuredDetailVO> teaModule = Lists.newArrayList();
        String title = "";
        if (total == optionalCount) {
            title = "茶饮 全部可享";
        } else {
            title = String.format("茶饮 %s选%s", total, optionalCount);
        }
        // 构建标题
        teaModule.add(buildStructuredDetailVO(title, null,null, ViewComponentTypeEnum.DETAIL_TYPE_11.getType()));
        // 构造茶饮可选模块
        List<DealDetailStructuredDetailVO> optionalDetails = buildTeaOptionalDetailModule(serviceProjectGroupDTO);
        safeAddAllWithNewLine(teaModule, optionalDetails, false);
        return teaModule;
    }

    private List<DealDetailStructuredDetailVO> buildTeaOptionalDetailModule(OptionalServiceProjectGroupDTO serviceProjectGroupDTO) {
        if (CollectionUtils.isEmpty(serviceProjectGroupDTO.getGroups())){
            return null;
        }
        List<DealDetailStructuredDetailVO> teaOptionalDetail = Lists.newArrayList();
        List<ServiceProjectDTO> serviceProjectDTOS = serviceProjectGroupDTO.getGroups();
        for (int i = 0; i <serviceProjectDTOS.size(); i++) {
            ServiceProjectDTO serviceProjectDTO = serviceProjectDTOS.get(i);
            Map<String, String> nameToAttr = serviceProjectDTO.getAttrs().stream().collect(Collectors.toMap(e->e.getAttrName(), e->e.getAttrValue()));
            DealDetailStructuredDetailVO optionalDetail = buildOptionalDetail(nameToAttr);
            teaOptionalDetail.add(optionalDetail);
        }
        return teaOptionalDetail;
    }

    private DealDetailStructuredDetailVO buildOptionalDetail(Map<String, String> nameToAttr){
        // 茶种类
        String teaKind = nameToAttr.get("teakind");
        // 茶水数量
        String teaQuantity = nameToAttr.get("teaquantity");
        // 茶水数量单位
        String teaVolumeUnits = nameToAttr.get("TeaVolumeUnits");
        // 是否可续水
        String addWaterOrNot = nameToAttr.get("addwaterornot");
        DealDetailStructuredDetailVO teaOptionalDetail = buildStructuredDetailVO(null, teaKind, addWaterOrNot, ViewComponentTypeEnum.DETAIL_TYPE_12.getType());
        teaOptionalDetail.setUnit(String.format("%s%s", teaQuantity, teaVolumeUnits));
        return teaOptionalDetail;
    }

    private List<DealDetailStructuredDetailVO> buildAdditionalInfo(AdditionInfoResult additionInfo){
        List<DealDetailStructuredDetailVO> additionalInfoModule = Lists.newArrayList();
        // 补充说明
        String additionInfoStr = Optional.ofNullable(additionInfo).map(AdditionInfoResult::getAdditionInfo).orElse(StringUtils.EMPTY);
        String additionalText = getDesc(additionInfoStr);
        if (StringUtils.isNotBlank(additionalText)) {
            // 标题
            additionalInfoModule.add(buildStructuredDetailVO("补充说明", null,null, ViewComponentTypeEnum.DETAIL_TYPE_14.getType()));
            additionalInfoModule.add(buildStructuredDetailVO(null, additionalText,null, ViewComponentTypeEnum.DETAIL_TYPE_13.getType()));
            return additionalInfoModule;
        }
        return null;
    }

    protected DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type){
        DealDetailStructuredDetailVO structuredDetailVO = DealDetailStructuredDetailVO.builder()
                .type(type)
                .build();
        if (title != null) {
            structuredDetailVO.setTitle(title);
        }
        if (content != null) {
            structuredDetailVO.setContent(content);
        }
        if (subcontent != null) {
            structuredDetailVO.setSubContent(subcontent);
        }
        return structuredDetailVO;
    }

    private String getDesc(String additionInfoStr) {
        if (StringUtils.isEmpty(additionInfoStr)) {
            return StringUtils.EMPTY;
        }
        UniformStructModel uniformStruct = JsonCodec.decode(additionInfoStr, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        if (CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        UniformStructContentModel struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals(RICH_TEXT)).findFirst().orElse(null);
        if (struct == null || struct.getData() == null) {
            return null;
        }
        return struct.getData().toString();
    }



    /**
     * 构造套餐标题模块
     * @param productAttr 商品属性
     * @return 结构化服务详情最小模型单元
     */
    abstract DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr);

    /**
     * 构造套餐详情模块
     * @param productAttr
     * @return
     */
    abstract List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr);
}
