package com.sankuai.dzshoppingguide.product.detail.application.utils.thread;

import com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzshoppingguide.product.detail.application.enums.ThreadPoolNameEnum;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-02-26
 * @desc 线程池工具类
 */
public class ThreadPoolUtils {

    private static final ThreadPool DEFAULT_THREAD_POOL = Rhino.newThreadPool("defaultExecutor");

    /**
     * 获取线程池对象，保证trace信息在多线程环境下正常传递
     * @param threadPoolNameEnum 线程池名称枚举
     * @return 线程池
     */
    public static ExecutorService getExecutor(ThreadPoolNameEnum threadPoolNameEnum) {
        switch (threadPoolNameEnum) {
            default:
                return DEFAULT_THREAD_POOL.getExecutor();
        }
    }
}
