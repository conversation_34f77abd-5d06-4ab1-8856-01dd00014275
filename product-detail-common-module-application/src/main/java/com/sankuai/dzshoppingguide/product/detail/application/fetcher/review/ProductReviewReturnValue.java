package com.sankuai.dzshoppingguide.product.detail.application.fetcher.review;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewTag;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.ProductReviewTagFilter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductReviewReturnValue extends FetcherReturnValueDTO {

    /**
     * 筛选标签
     */
    private List<ProductReviewTagFilter> tagList;

    /**
     * 评价列表
     */
    @MobileDo.MobileField
    private List<ProductReviewDetail> reviewList;

    /**
     * 更多跳转链接
     */
    @MobileDo.MobileField
    private String moreUrl;

    /**
     * 模块名
     */
    @MobileDo.MobileField
    private String title;

    /**
     * 评价总数
     */
    @MobileDo.MobileField
    private int totalCount;
}
