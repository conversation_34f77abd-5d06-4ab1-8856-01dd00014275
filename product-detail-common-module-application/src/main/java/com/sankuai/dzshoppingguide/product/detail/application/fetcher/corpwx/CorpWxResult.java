// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.corpwx;
//
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
// import com.sankuai.dz.srcm.flow.dto.CorpWxFlowMaterialDTO;
// import lombok.AllArgsConstructor;
// import lombok.Data;
// import lombok.EqualsAndHashCode;
// import lombok.NoArgsConstructor;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/2/26 17:23
//  */
// @EqualsAndHashCode(callSuper = true)
// @Data
// @AllArgsConstructor
// @NoArgsConstructor
// public class CorpWxResult extends FetcherReturnValueDTO {
//     private CorpWxFlowMaterialDTO corpWxFlowMaterialDTO;
// }
