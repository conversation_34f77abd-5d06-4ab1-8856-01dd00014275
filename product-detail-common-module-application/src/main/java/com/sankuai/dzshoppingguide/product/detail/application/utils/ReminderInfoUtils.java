package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;

public class ReminderInfoUtils {

    public static ProductDetailReminderVO buildModel(String title, List<String> reminderInfo, GuaranteeInstructionsBarLayerVO layer, boolean isOldDetail) {
        ProductDetailReminderVO data = new ProductDetailReminderVO();
        data.setTitle(title);
        data.setContents(buildContent(reminderInfo, isOldDetail));
        data.setLayer(layer);
        return data;
    }

    public static ProductDetailGuaranteeVO buildModel0(String title, List<String> reminderInfo) {
        ProductDetailGuaranteeVO data = new ProductDetailGuaranteeVO();
        data.setTitle(title);
        data.setContents(buildContent(reminderInfo, false));
        return data;
    }

    public static GuaranteeInstructionsBarLayerVO buildLayer(int type, String jumpUrl, String moduleKey) {
        GuaranteeInstructionsBarLayerVO layer = new GuaranteeInstructionsBarLayerVO();
        layer.setJumpUrl(jumpUrl);
        layer.setType(type);
        layer.setModulekey(moduleKey);
        return layer;
    }

    public static List<GuaranteeInstructionsContentVO> buildContent(List<String> reminderInfo, boolean isOldDetail) {
        if ( CollectionUtils.isEmpty(reminderInfo)) {
            return Collections.emptyList();
        }
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        for (String info : reminderInfo) {
            GuaranteeInstructionsContentVO guaranteeInstructionsContentVO = new GuaranteeInstructionsContentVO();
            guaranteeInstructionsContentVO.setText(info);
            guaranteeInstructionsContentVO.setFontSize("12");
            if (isOldDetail) {
                // 老团详色号
                guaranteeInstructionsContentVO.setFontColor("#222222");
            }else {
                guaranteeInstructionsContentVO.setFontColor("#555555");
            }
            contents.add(guaranteeInstructionsContentVO);
        }
        // 20250402 前端写死箭头图标了,不需要后端传
        // Icon icon = new Icon();
        // icon.setIcon("https://p1.meituan.net/travelcube/f98ad26e0448da2bddc5c47dcf3de5ff358.png");
        // // 须知条的最后一个元素后面拼接箭头图标
        // contents.get(reminderInfo.size() - 1).setSuffixIcon(icon);
        return contents;
    }

    public static Optional<GuaranteeInstructionsContentVO> buildReminderInfo(String text) {
        if (StringUtils.isEmpty(text)) {
            return Optional.empty();
        }
        GuaranteeInstructionsContentVO guaranteeInstructionsContentVO = new GuaranteeInstructionsContentVO();
        guaranteeInstructionsContentVO.setText(text);
        guaranteeInstructionsContentVO.setFontSize("12");
        guaranteeInstructionsContentVO.setFontColor("#555555");
        return Optional.of(guaranteeInstructionsContentVO);
    }

    public static Optional<GuaranteeInstructionsContentVO> buildReminderInfo(String text, boolean oldDetail) {
        if (StringUtils.isEmpty(text)) {
            return Optional.empty();
        }
        GuaranteeInstructionsContentVO guaranteeInstructionsContentVO = new GuaranteeInstructionsContentVO();
        guaranteeInstructionsContentVO.setText(text);
        guaranteeInstructionsContentVO.setFontSize("12");
        if (oldDetail) {
            guaranteeInstructionsContentVO.setFontColor("#222222");
        } else {
            guaranteeInstructionsContentVO.setFontColor("#555555");
        }
        return Optional.of(guaranteeInstructionsContentVO);
    }

    public static GuaranteeInstructionsContentVO buildUnavailableReminderInfo(String text) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        GuaranteeInstructionsContentVO guaranteeInstructionsContentVO = new GuaranteeInstructionsContentVO();
        guaranteeInstructionsContentVO.setText(text);
        guaranteeInstructionsContentVO.setFontSize("12");
        // todo 这个地方的颜色需要具体的确认下
        guaranteeInstructionsContentVO.setFontColor("#555555");
        guaranteeInstructionsContentVO.setStyle(1);
        return guaranteeInstructionsContentVO;
    }

    public static GuaranteeInstructionsContentVO buildUnavailableReminderInfo(String text, boolean isOldDetail) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        GuaranteeInstructionsContentVO guaranteeInstructionsContentVO = new GuaranteeInstructionsContentVO();
        guaranteeInstructionsContentVO.setText(text);
        guaranteeInstructionsContentVO.setFontSize("12");
        // todo 这个地方的颜色需要具体的确认下
        if (isOldDetail) {
            guaranteeInstructionsContentVO.setFontColor("#222222");
        } else {
            guaranteeInstructionsContentVO.setFontColor("#555555");
        }
        guaranteeInstructionsContentVO.setStyle(1);
        return guaranteeInstructionsContentVO;
    }

    public static boolean fromOldDetail(ProductDetailPageRequest request){
        return "olddetail".equals(request.getCustomParam().getCustomParams().get("productdetail"));
    }

}
