package com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage.DealImageTextDetailModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage.ReserveImageTextDetailModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;

/**
 * <AUTHOR>
 * @date 2025-03-24
 * @desc 商品图文详情模块工厂
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.DETAIL_IMAGE,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class
        }
)
public class ProductImageTextDetailModuleFactory extends BaseBuilderFactory<ImageTextDetailVO> {
    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return ReserveImageTextDetailModuleBuilder.class;
        }
        return DealImageTextDetailModuleBuilder.class;
    }
}
