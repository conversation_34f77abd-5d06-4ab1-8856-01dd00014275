package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/14
 */
public class AtomServiceUtils {
    private static final String PLAN_ID = "11900007";

    public static ReserveQueryRequest buildReserveQueryRequest(List<Integer> productIds, double lat, double lng, long userId, Map<Long, Long> productIdShopIdMap, boolean isMt) {
        ReserveQueryRequest reserveQueryRequest = new ReserveQueryRequest();
        reserveQueryRequest.setPlanId(PLAN_ID);
        reserveQueryRequest.setProductIds(productIds);
        reserveQueryRequest.setExtParams(AtomServiceUtils.buildParams(lat, lng, userId, productIds, productIdShopIdMap, isMt));
        return reserveQueryRequest;
    }

    public static Map<String, Object> buildParams(double lat, double lng, long userId, List<Integer> productIds, Map<Long, Long> productIdShopIdMap, boolean isMt) {
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("lat", lat);
        extParams.put("lng", lng);
        extParams.put("scene", RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        extParams.put("userId", userId);
        if (isMt) {
            extParams.put("platform", VCPlatformEnum.MT.getType());
            extParams.put("uaCode", VCClientTypeEnum.MT_APP.getCode());
        } else {
            extParams.put("platform", VCPlatformEnum.DP.getType());
            extParams.put("uaCode", VCClientTypeEnum.DP_APP.getCode());
        }
        extParams.put("promoScene", RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        extParams.put("productId2ShopIdLMap", buildProductId2ShopIdMap(productIds, productIdShopIdMap));
        extParams.put("selectDates", Lists.newArrayList(TimeUtils.getDaysAgoMidnightTimestamp()));
        return extParams;
    }

    private static Map<Integer, Long> buildProductId2ShopIdMap(List<Integer> productIds, Map<Long, Long> productIdShopIdMap) {
        Map<Integer, Long> productId2ShopIdMap = Maps.newHashMap();
        productIds.forEach(productId -> productId2ShopIdMap.put(productId, productIdShopIdMap.getOrDefault((long)productId, 0L)));
        return productId2ShopIdMap;
    }

    public static Map<Long, Long> getProductId2ShopIdMap(List<DealGroupDTO> dealGroupDTOList, boolean isMt) {

        Map<Long, Long> productIdShopIdMap = dealGroupDTOList.stream()
                .filter(item -> validShopId(item, isMt))
                .collect(Collectors.toMap(DealGroupDTO::getBizProductId, e -> getShopId(e, isMt), (a, b) -> a));
        if (MapUtils.isEmpty(productIdShopIdMap)) {
            return null;
        }
        return productIdShopIdMap;
    }

    private static Long getShopId(DealGroupDTO dealGroupDTO, boolean isMt) {
        if (isMt) {
            return dealGroupDTO.getDisplayShopInfo().getMtDisplayShopIds().get(0);
        }
        return dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds().get(0);
    }

    private static boolean validShopId(DealGroupDTO dealGroupDTO, boolean isMt) {
        if (isMt) {
            return Objects.nonNull(dealGroupDTO.getDisplayShopInfo()) && CollectionUtils.isNotEmpty(dealGroupDTO.getDisplayShopInfo().getMtDisplayShopIds());
        }
        return Objects.nonNull(dealGroupDTO.getDisplayShopInfo()) && CollectionUtils.isNotEmpty(dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds());
    }
}
