package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/13 11:22
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
@Slf4j
public class DealGroupIdMapperFetcher extends NormalFetcherContext<DealGroupIdMapper> {
    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;
    @Override
    protected CompletableFuture<DealGroupIdMapper> doFetch() {
        if (request.getProductType() != ProductTypeEnum.DEAL.getCode()) {
            return CompletableFuture.completedFuture(null);
        }
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        long productId = request.getProductId();
        long mtDealGroupId;
        long dpDealGroupId;
        if (isMt) {
            mtDealGroupId = productId;
            dpDealGroupId = mapperCacheWrapper.fetchDpDealId(productId);
        } else {
            dpDealGroupId = productId;
            mtDealGroupId = mapperCacheWrapper.fetchMtDealId(productId);
        }
        return CompletableFuture.completedFuture(new DealGroupIdMapper(mtDealGroupId, dpDealGroupId));
    }
}
