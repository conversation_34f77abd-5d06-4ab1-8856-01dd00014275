package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.discountcard.DiscountCardModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.Configs;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.FeatureDetailDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GlassProductTagRuleConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.config.StructuredDetailDataProcessLayerConfig;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CollectedProductUrlConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CpvImpactAnalyseConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarItem;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 16:05
 */
@Slf4j
public class LionConfigUtils {

    private static final List<String> dpPoiFields = Lists.newArrayList("shopId", "shopType", "mainCategoryId", "cityId", "mainRegionId",
            "backMainCategoryPath");

    private static final List<String> mtPoiFields = Lists.newArrayList("dpPoiId", "mtPoiId");

    // public static PoiFieldsDTO getPoiFields() {
    //     try {
    //         String string = Lion.getString(Environment.getAppName(), POI_FIELDS);
    //         return JsonCodec.decode(string, PoiFieldsDTO.class);
    //     } catch (Exception e) {
    //         log.error("getPoiFields fail", e);
    //     }
    //     return new PoiFieldsDTO(dpPoiFields, mtPoiFields);
    // }

    public static boolean distributeImageScaleByVersion(int categoryId) {
        List<Integer> categoryIds = Lion.getList(DZTGDETAIL_APPKEY,
                DISTRIBUTE_IMAGE_SCALE_WHITELIST, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        return categoryIds.contains(categoryId);
    }

    public static DiscountCardModuleBuilder.MidReserveConfig getMiddleReserveConfig() {
        return Lion.getBean(RESERVE_APPKEY, STOCK_GRANULARITY, DiscountCardModuleBuilder.MidReserveConfig.class);
    }

    public static List<NavBarItem> getNavBarItemList() {
        return Lion.getList(APP_KEY, NAV_BAR_ITEM_LIST, NavBarItem.class, Collections.emptyList());
    }

    public static Configs getFacilityConfigs() {
        return Lion.getBean(APP_KEY, DETAIL_FACILITY_CONFIG, Configs.class);
    }

    /**
     * 控制团购次卡退款规则新旧版本
     */
    public static boolean hasPurchaseNoteTableSwitch() {
        return Lion.getBoolean(APP_KEY, DEAL_TIMES_CARD_REFUND_TABLE, false);
    }

    /**
     * 是否为强预订团单
     *
     * @return
     */
    //TODO 后续将改为判断订单是否为强预定
    public static boolean hitPreOrderDeal(DealCtx ctx) {
        if (Objects.isNull(ctx) || Objects.isNull(ctx.getDealGroupDTO())) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = ctx.getDealGroupDTO().getCategory();
        return hitPreOrderDeal(categoryDTO);
    }

    /**
     * 是否为强预订团单
     *
     * @param categoryDTO
     * @return
     */
    public static boolean hitPreOrderDeal(DealGroupCategoryDTO categoryDTO) {
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        List<String> preOrderCategoryIds = Lion.getList(APP_KEY, PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(preOrderCategoryIds)) {
            return false;
        }
        return preOrderCategoryIds.contains(categoryId);
    }

    public static boolean hitReminderInfoConfig(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REMINDER_INFO_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }

    public static List<Long> getCustomerWhiteList() {
        return Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.DEAL_TIMES_CARD_REFUND_CUSTOMER_WHITELIST, Long.class, Lists.newArrayList());
    }

    public static boolean hitCustomRefundCategoryConfig(int categoryId) {
        List<Integer> customRefundCategoryConfig = Lion.getList(LionConstants.APP_KEY,
                LionConstants.CUSTOM_REFUND_CATGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customRefundCategoryConfig) && customRefundCategoryConfig.contains(categoryId);
    }

    /**
     * 获取收藏商品跳转链接配置
     * @return 返回收藏商品跳转链接的配置信息
     */
    public static Map<String, CollectedProductUrlConfig> getCollectedProductUrlConfig() {
        return Lion.getMap(LionConstants.APP_KEY, LionConstants.COLLECTED_PRODUCT_URL, CollectedProductUrlConfig.class, Collections.emptyMap());
    }

    /**
     * 是否是团购后预约适用类目
     */
    public static boolean reserveAfterPurchase(int categoryId) {
        List<Integer> categories = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.AVAILABLE_CATEGORY_IDS, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(categories) && categories.contains(categoryId);
    }

    /**
     * 是否强制预约
     *
     * @param categoryId
     * @return
     */
    public static boolean forceReserve(int categoryId) {
        List<Integer> config = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.FORCE_BOOKING_PUBLISH_CATEGORY, Integer.class, Lists.newArrayList());
        return CollectionUtils.isNotEmpty(config) && config.contains(categoryId);
    }

    public static boolean inCategoryList(Long categoryId) {
        List<Long> whiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.FREE_DEAL_INFO_CONFIG_LIST_LION_KEY, Long.class);
        return !CollectionUtils.isEmpty(whiteList) && whiteList.contains(categoryId);
    }

    /**
     * 是否是安心学的团购类目
     * @param categoryId
     * @return
     */
    public static boolean isAnXinXueCategoryIds(int categoryId) {
        List<Integer> categoryIds = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_XUE_CATEGORY, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }


    /**
     * 判断是否用定制化使用时间
     * @param categoryId
     * @return
     */
    public static boolean hitCustomAvailableTimeOfDays(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.CUSTOM_AVAILABLE_TIMES_OF_DAYS, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }

    public static List<Integer> supportAdditionalCategory() {
        return Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.SUPPORT_ADDITIONAL_CATEGORY, Integer.class, Lists.newArrayList());
    }

    /**
     * 是否是安心学的团购类目
     *
     * @param categoryId 类目id
     * @return true 是 false 不是
     */
    public static boolean isSafeLearnCategoryIds(int categoryId) {
        List<Integer> categoryIds = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_XUE_CATEGORY, Integer.class, Collections.emptyList());
        return categoryIds.contains(categoryId);
    }

    public static Set<String> getWuYouTongAttrValues() {
        List<String> wuyoutongValues = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.WU_YOU_TONG_ATTR_VALUES, String.class, Lists.newArrayList());
        return new HashSet<>(wuyoutongValues);
    }

    public static Map<Long, GuaranteeInstructionsBarLayerVO> getTradeAssuranceLayerConfig(boolean isMt) {
        Map<String, GuaranteeInstructionsBarLayerVO> map = Lion.getMap(LionConstants.DZTGDETAIL_APPKEY, LionConstants.TRADE_ASSURANCE_LAYER_CONFIGS, GuaranteeInstructionsBarLayerVO.class, Collections.emptyMap());
        String validPrefix = isMt ? "mt" : "dp";
        return map.entrySet().stream()
                .filter(entry -> StringUtils.startsWith(entry.getKey(), validPrefix))
                .collect(Collectors.toMap(
                        entry -> NumberUtils.toLong(StringUtils.removeStart(entry.getKey(), validPrefix)),
                        Map.Entry::getValue,
                        (oldValue, newValue) -> newValue,
                        LinkedHashMap::new
                ));

    }

    public static List<Long> getEduOnlineDealServiceLeafIds() {
        return Lion.getList(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L));
    }

    public static List<GlassProductTagRuleConfig> getGlassProductTagRuleConfigs() {
        return Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GLASS_GENUINE_GUARANTEE_CONFIG, GlassProductTagRuleConfig.class, Collections.emptyList());
    }

    public static boolean showSafeImplantTag(ProductCategory productCategory) {
        List<String> showSafeImplantTagList = Lion.getList(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.SHOW_SAFE_IMPLANT_TAG_CATEGORY_IDS, String.class, Collections.emptyList());
        // 未配置类目则不用过滤
        if (CollectionUtils.isEmpty(showSafeImplantTagList)) {
            return true;
        }
        int secondCategoryId = productCategory.getProductSecondCategoryId();
        int thirdCategoryId = productCategory.getProductThirdCategoryId();
        String cateKey = thirdCategoryId > 0 ? String.format("%s_%s", secondCategoryId, thirdCategoryId) : String.valueOf(secondCategoryId);
        return CollectionUtils.isNotEmpty(showSafeImplantTagList) && showSafeImplantTagList.contains(cateKey);
    }

    public static Long getMedicalZdcTagId(String key) {
        Map<String, Long> safeTreatZdcMap = Lion.getMap(LionConstants.DZTGDETAIL_APPKEY, LionConstants.MEDICAL_ZDC_TAG_ID, Long.class, new HashMap<>());
        if (MapUtils.isEmpty(safeTreatZdcMap)) {
            return null;
        }
        return safeTreatZdcMap.get(key);
    }

    public static boolean showPriceProtectionInfo(int categoryId) {
        List<Integer> priceProtectionCategoryIds = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.PRICE_PROTECTION_CATEGORY_IDS, Integer.class, new ArrayList<>());
        return priceProtectionCategoryIds.contains(categoryId);
    }


    public static List<FeatureDetailDTO> getPerformanceGuaranteeFeatureList(ProductCategory productCategory) {
        try {
            String categoryId = String.valueOf(productCategory.getProductSecondCategoryId());
            String key = String.format("%s.%s", categoryId, productCategory.getProductThirdCategoryId());
            String featureDetailMapStr = Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.PERFORMANCE_GUARANTEE_FEATURE_DETAIL_CONFIG, "");

            Map<String, List<FeatureDetailDTO>> featureDetailMap = JsonUtils.fromJson(featureDetailMapStr, new TypeReference<Map<String, List<FeatureDetailDTO>>>() {
            });
            if (MapUtils.isEmpty(featureDetailMap)) {
                return Collections.emptyList();
            }
            if (featureDetailMap.containsKey(key)) {
                return featureDetailMap.get(key);

            } else if (featureDetailMap.containsKey(categoryId)) {
                return featureDetailMap.get(categoryId);
            }
        } catch (Exception e) {
            log.error("[LionConfigUtils] getPerformanceGuaranteeSwitch error", e);
        }
        return Collections.emptyList();
    }

    /**
     * 判断保洁自营门店全链路 是否需要替换 IM url 为 太平洋智能门户
     * @return true 需要替换
     */
    public static boolean needReplaceImToCsForCleaning() {
        try {
            return Lion.getBoolean("dztrade-mapi-web", "dztrade-mapi-web.cleaning.self.own.csUrl.switch", false);
        } catch (Exception e) {
            log.error("needReplaceImToCsForCleaning error", e);
            return false;
        }
    }

    /**
     * 判断无忧通门店全链路 是否需要替换 IM url 为 太平洋智能门户
     * @return true 需要替换
     */
    public static boolean needReplaceImToCsForCarefree() {
        try {
            return Lion.getBoolean("dztrade-mapi-web", "dztrade-mapi-web.carefree.self.own.csUrl.switch", false);
        } catch (Exception e) {
            log.error("needReplaceImToCsForCarefree error", e);
            return false;
        }
    }

    /**
     * 安心学落地页
     * @param isMt
     * @return
     */
    public static String getAnXinXueDetailPage(Boolean isMt) {
        if (isMt) {
            return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_XUE_DETAIL_MT, "https://cube.meituan.com/cube/block/f42805397d77/317412/index.html");
        }
        return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_XUE_DETAIL_DP, "https://cube.dianping.com/cube/block/4e4ebff170e1/317745/index.html");
    }

    /**
     * 安心练落地页
     * @param isMt
     * @return
     */
    public static String getAnXinExerciseDetailPage(Boolean isMt) {
        if (isMt) {
            return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DETAIL_MT, "https://cube.meituan.com/cube/block/728e318931ce/336447/index.html");
        }
        return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DETAIL_DP, "https://cube.dianping.com/cube/block/728e318931ce/336447/index.html");
    }

    /**
     * 安心练icon
     * @return
     */
    public static String getAnXinExerciseIcon() {
        return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_ICON, "https://p0.meituan.net/ingee/b8967d6948116a467550b870451be4f93390.png");
    }

    /**
     * 安心练标签展示文案
     * @return
     */
    public static String getAnXinExerciseDisplayText() {
        return Lion.getString(LionConstants.DZTGDETAIL_APPKEY, LionConstants.GUARANTEE_AN_XIN_EXERCISE_DISPLAY_TEXT, "放心付 · 安心退 · 跑路赔");
    }

    /**
     * 团购详情页保障浮层配置
     * @return 返回团购详情页保障浮层的配置信息
     */
    public static Map<String, LayerConfig> getDealGuaranteeLayerConfig() {
        return Lion.getMap(APP_KEY, DEAL_GUARANTEE_LAYER_CONFIG, LayerConfig.class, Collections.emptyMap());
    }

    /**
     * 判断是否用新限购条
     * @param categoryId
     * @return
     */
    public static boolean hitLimitInfoConfig(int categoryId) {
        List<Integer> customReminderInfoConfig = Lion.getList(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.LIMIT_INFO_SUIT_CATEGORY_CONFIG, Integer.class, Collections.emptyList());
        return CollectionUtils.isNotEmpty(customReminderInfoConfig) && customReminderInfoConfig.contains(categoryId);
    }


    /**
     * 获取补牙材料怎么选信息
     * @return
     */
    public static Map<String,String> getDentalMaterialMap(){
        return Lion.getMap(LionConstants.APP_KEY, LionConstants.DENTAL_MATERIAL_DETAIL, String.class, Collections.emptyMap());
    }

    // 获取单值数组
    public static boolean isSingleValueArrayAttr(String attrKey) {
        List<String> attrKeys = Lion.getList(APP_KEY, SINGLE_VALUE_ARRAY_ATTR_LIST, String.class, Collections.emptyList());
        return attrKeys.contains(attrKey);
    }


    /**
     * 获取cpv影响分析配置
     * @return
     */
    public static CpvImpactAnalyseConfig getCpvImpactAnalyseConfig() {
        return Lion.getBean(APP_KEY, CPV_IMPACT_ANALYSE_CONFIG, CpvImpactAnalyseConfig.class, new CpvImpactAnalyseConfig());
    }

    /**
     * 查询中心按需索取查询product的attr配置
     * @return
     */
    public static Map<String,List> getProductAttrConfigMap(){
        return Lion.getMap(LionConstants.APP_KEY, LionConstants.PRODUCT_ATTR_CONFIG, List.class, Collections.emptyMap());
    }

    public static Map<String, String> getGlassesMaterialMap() {
        return Lion.getMap(LionConstants.APP_KEY, LionConstants.GLASSES_MATERIAL_DETAIL, String.class, Collections.emptyMap());
    }

    public static Map<String, String> getMaterialComparisonPicMap() {
            return Lion.getMap(LionConstants.APP_KEY, LionConstants.MATERIAL_COMPARISON_PIC, String.class, Collections.emptyMap());
    }

    public static List<StructuredDetailDataProcessLayerConfig> getStructuredDetailDataProcessLayerConfig(Integer secondCategoryId, Integer thirdCategoryId) {
        if (Objects.isNull(secondCategoryId) && Objects.isNull(thirdCategoryId)) {
            return Collections.emptyList();
        }
        StringBuilder keyBuilder = new StringBuilder(STRUCTURED_DETAIL_DATA_PROCESS_LAYER_CONFIG);
        if (Objects.nonNull(secondCategoryId)) {
            keyBuilder.append(".").append(secondCategoryId);
        }
        if (Objects.nonNull(thirdCategoryId)) {
            keyBuilder.append(".").append(thirdCategoryId);
        }
        return Lion.getList(APP_KEY, keyBuilder.toString(), StructuredDetailDataProcessLayerConfig.class, Collections.emptyList());
    }

    public static boolean isMultiValueArrayServiceProject(String attrKey) {
        List<String> attrKeys = Lion.getList(APP_KEY, MULTI_VALUE_ARRAY_SERVICE_PROJECT_LIST, String.class, Collections.emptyList());
        return attrKeys.contains(attrKey);
    }
}
