package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: caisiyuan03
 * @Date: 2025/6/4 14:59
 * @Description: 洗浴行业须知条
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER, moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class, ProductAttrFetcher.class, SkuAttrFetcher.class,
                ProductCategoryFetcher.class, ProductServiceProjectFetcher.class, SkuDefaultSelectFetcher.class
        }
)
@Slf4j
public class BathReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (Objects.isNull(baseReminderInfo) || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }

        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);

        if (Objects.isNull(productAttr)) {
            return null;
        }

        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();

        // 【预约信息】·【日期】【时间段】可用·【有效时间】
        if (AvailableTimeHelper.hasAvailableTimePeriod(productAttr)) {
            // 如果选择了部分时间可用
            AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        } else {
            // 选择了营业时间内全部可用
            AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
        }

        // 有效期
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(baseInfo)).ifPresent(contents::add);

        return baseReminderInfo;
    }
}
