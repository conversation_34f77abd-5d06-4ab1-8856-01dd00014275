package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.ComponentFactory;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.ChessCardRoomVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/30 11:53
 * @Description: 二级类目: 棋牌室 三级类目: 棋牌套餐 123003
 */
@Component
public class ChessCardPackagesStyle implements Style<ChessCardRoomVO> {

    private static final String SPACE_TYPE = "rooms";
    private static final String USABLE_DURATION = "serviceDuration";
    private static final String TIMEOUT_INFORMATION = "TimeoutInformation2";
    private static final String SPACE_SUPPORT = "productfeature";
    private static final String TEA_SERVICE = "desc";
    private static final String ADDITIONAL_SERVICE = "methanalFeature";

    private final CommonComponent0<ChessCardRoomVO> commonComponent;

    public ChessCardPackagesStyle(ComponentFactory componentFactory) {
        this.commonComponent = componentFactory.createComponent();
    }

    @Override
    public List<ChessCardRoomVO> build(DealDetailBuildContext context) {
        List<ChessCardRoomVO> packageStyle = Lists.newArrayList();
        // 构造标题
        packageStyle.addAll(commonComponent.build(ChessCardRoomVO.builder()
                .title("棋牌套餐")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_14.getType())
                .build()));
        // 构造内容
        packageStyle.addAll(buildPackageDetails(context.getProductAttr()));
        return packageStyle;
    }

    private List<ChessCardRoomVO> buildPackageDetails(ProductAttr productAttr) {
        List<ChessCardRoomVO> packageDetails = Lists.newArrayList();
        // 1. 空间类型
        String spaceType = productAttr.getSkuAttrFirstValue(SPACE_TYPE);
        packageDetails.addAll(commonComponent.build(ChessCardRoomVO.builder().title("空间类型").content(spaceType).type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType()).build()));
        // 2. 使用时长 TODO: 增加超时信息(选填) 逻辑修改
        String applicableDuration = productAttr.getSkuAttrFirstValue(USABLE_DURATION);
        String timeoutInfo = productAttr.getSkuAttrFirstValue(TIMEOUT_INFORMATION);
        packageDetails.addAll(commonComponent.build(ChessCardRoomVO.builder().title("使用时长").content(applicableDuration).subContent(timeoutInfo).type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType()).build()));
        // 3. 空间配套
        String spaceSupport = productAttr.getSkuAttrFirstValue(SPACE_SUPPORT);
        packageDetails.addAll(commonComponent.build(ChessCardRoomVO.builder().title("空间配套").content(spaceSupport).type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType()).build()));
        // 4. 茶水服务
        String teaService = productAttr.getSkuAttrFirstValue(TEA_SERVICE);
        packageDetails.addAll(commonComponent.build(ChessCardRoomVO.builder().title("茶水服务").content(teaService).type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType()).build()));
        // 5. 附加服务 TODO: 顿号分隔
        String additionalService = productAttr.getSkuAttrFirstValue(ADDITIONAL_SERVICE);
        packageDetails.addAll(commonComponent.build(ChessCardRoomVO.builder().title("附加服务").content(additionalService).type(ViewComponentTypeEnum.DETAIL_TYPE_15.getType()).build()));
        return packageDetails;
    }

    @Override
    public boolean needNewLine() {
        return true;
    }
}
