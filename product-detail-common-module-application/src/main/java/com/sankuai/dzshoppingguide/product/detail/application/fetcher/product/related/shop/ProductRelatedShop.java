package com.sankuai.dzshoppingguide.product.detail.application.fetcher.product.related.shop;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductRelatedShop extends FetcherReturnValueDTO {

    /**
     * 点评展示商户ID
     */
    private List<Long> dpDisplayShopIds;

    /**
     * 美团展示商户ID
     */
    private List<Long> mtDisplayShopIds;

}
