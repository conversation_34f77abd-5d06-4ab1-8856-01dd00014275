// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.guarantee;
//
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
// import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
// import lombok.AllArgsConstructor;
// import lombok.EqualsAndHashCode;
// import lombok.Getter;
// import lombok.NoArgsConstructor;
//
// import java.util.List;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/3/11 08:45
//  */
// @EqualsAndHashCode(callSuper = true)
// @Getter
// @AllArgsConstructor
// @NoArgsConstructor
// public class GuaranteeTagResult extends FetcherReturnValueDTO {
//     private List<ObjectGuaranteeTagDTO> guaranteeTagList;
// }
