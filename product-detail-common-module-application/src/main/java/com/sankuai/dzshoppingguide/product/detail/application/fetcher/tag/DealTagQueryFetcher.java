package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag;

import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeRequest;
import com.dianping.deal.tag.dto.MultiSubjectTagRelationJudge;
import com.dianping.deal.tag.enums.SubjectTagRelation;
import com.dianping.deal.tag.enums.SubjectTypeEnum;
import com.dianping.deal.tag.enums.TagSubject;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 18:00
 */
@Fetcher(
        timeout = 50000L,
        previousLayerDependencies = {CommonModuleStarter.class, ProductCategoryFetcher.class}
)
@Slf4j
public class DealTagQueryFetcher extends NormalFetcherContext<DealTagQueryResult> {
    //美团安心改标签
    public static final Long LABEL_ID = 100218044L;

    @Autowired
    private CompositeAtomService compositeAtomService;
    @Override
    protected CompletableFuture<DealTagQueryResult> doFetch() {

        try {
            if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
                return CompletableFuture.completedFuture(null);
            }

            MultiSubjectTagBatchJudgeRequest tagQueryRequest = new MultiSubjectTagBatchJudgeRequest();
            List<SubjectTagRelation> tagRelations = Lists.newArrayList(LABEL_ID).stream().map(tagId -> {
                SubjectTagRelation subjectTagRelation = new SubjectTagRelation();
                subjectTagRelation.setTagId(tagId);
                TagSubject tagSubject = new TagSubject();
                tagSubject.setSubjectType(SubjectTypeEnum.PRODUCT.getCode());
                tagSubject.setProductId(request.getProductId());
                subjectTagRelation.setTagSubject(tagSubject);
                return subjectTagRelation;
            }).collect(Collectors.toList());
            tagQueryRequest.setSubjectTagRelations(tagRelations);

            return compositeAtomService.queryTagIdByProductId(tagQueryRequest).thenApply(res -> {
                DealTagQueryResult dealTagQueryResult = new DealTagQueryResult();
                if ( res == null || !res.isSuccess() || CollectionUtils.isEmpty(res.getJudgeResult()) ) {
                    dealTagQueryResult.setHasTag(false);
                    return dealTagQueryResult;
                }
                MultiSubjectTagRelationJudge result = res.getJudgeResult().stream().filter(item -> item.getTagId() != null && LABEL_ID.compareTo(item.getTagId()) == 0).findFirst().orElse(null);
                if ( result == null ) {
                    dealTagQueryResult.setHasTag(false);
                    return dealTagQueryResult;
                }
                dealTagQueryResult.setHasTag(result.getJudgeResult());
                return dealTagQueryResult;
            });
        } catch (Exception e) {
            log.error("TagQueryFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }
}
