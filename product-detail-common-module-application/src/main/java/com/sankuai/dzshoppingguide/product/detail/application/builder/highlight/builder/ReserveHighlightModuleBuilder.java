package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.BodyPartDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HighlightsModuleAttrVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.highlights.vo.HighlightsModuleVO;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-06
 * @desc 预订高亮模块
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.HIGHLIGHTS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ReserveTagQueryFetcher.class
        }
)
public class ReserveHighlightModuleBuilder extends BaseVariableBuilder<HighlightsModuleVO> {
    private ProductAttr productAttr;
    private ReserveTagQueryResult reserveTagQueryResult;

    @Override
    public HighlightsModuleVO doBuild() {
        productAttr = getDependencyResult(ProductAttrFetcher.class);
        reserveTagQueryResult = getDependencyResult(ReserveTagQueryFetcher.class);
        return buildReserveHighlightsModuleVO();
    }

    private HighlightsModuleVO buildReserveHighlightsModuleVO() {
        HighlightsModuleVO highlightsModuleVO = new HighlightsModuleVO();
        highlightsModuleVO.setStyle("struct");
        highlightsModuleVO.setDelimiter("line");

        if (productAttr == null) {
            return highlightsModuleVO;
        }

        // 服务时长 > 服务部位 > 服务手法 > 服务方式
        List<HighlightsModuleAttrVO> attrVOList = Lists.newArrayList();
        String duration = productAttr.getSkuAttrFirstValue("duration");
        if (StringUtils.isNotBlank(duration)) {
            HighlightsModuleAttrVO durationAttr = new HighlightsModuleAttrVO();
            durationAttr.setName("服务时长");
            durationAttr.setValue(String.format("%s分钟", duration));
            attrVOList.add(durationAttr);
        }

        String bodyPart = getBodyPart(productAttr.getSkuAttrFirstValue("bodyPart"));
        if (StringUtils.isNotBlank(bodyPart)) {
            HighlightsModuleAttrVO partAttr = new HighlightsModuleAttrVO();
            partAttr.setName("服务部位");
            partAttr.setValue(bodyPart);
            attrVOList.add(partAttr);
        }

        String manipulation = productAttr.getSkuAttrFirstValue("massageManipulation");
        if (StringUtils.isNotBlank(manipulation)) {
            HighlightsModuleAttrVO manipulationAttr = new HighlightsModuleAttrVO();
            manipulationAttr.setName("服务手法");
            manipulationAttr.setValue(manipulation);
            attrVOList.add(manipulationAttr);
        }

        String serviceType = Optional.ofNullable(reserveTagQueryResult).map(ReserveTagQueryResult::getMetaTagDTO).map(MetaTagDTO::getValue).orElse(StringUtils.EMPTY);
        if (StringUtils.isNotBlank(serviceType)) {
            HighlightsModuleAttrVO serviceAttr = new HighlightsModuleAttrVO();
            serviceAttr.setName("服务方式");
            serviceAttr.setValue(serviceType);
            attrVOList.add(serviceAttr);
        }

        highlightsModuleVO.setAttrs(attrVOList);
        return highlightsModuleVO;
    }

    private String getBodyPart(String bodyPartStr){
        if ( org.apache.commons.lang3.StringUtils.isBlank(bodyPartStr)){
            return StringUtils.EMPTY;
        }
        List<BodyPartDTO> bodyPartDTOS = JSONObject.parseArray(bodyPartStr, BodyPartDTO.class);
        Set<String> bodyParts = bodyPartDTOS.stream().map(BodyPartDTO::getName).collect(Collectors.toSet());
        return DealAttrHelper.joinListByDelimiter(bodyParts, "、");
    }
}
