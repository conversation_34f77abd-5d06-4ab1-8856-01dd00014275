package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/14 15:37
 */
@Data
public class SnackTimeDTO {

    @FieldDoc(description = "用餐起始时间")
    @JsonProperty("snackStartTime")
    private String startTime;

    @FieldDoc(description = "用餐起始时间")
    @JsonProperty("snackEndTime")
    private String endTime;

    @FieldDoc(description = "用餐起始时间")
    @JsonProperty("timeframe")
    private String timeFrame;
}
