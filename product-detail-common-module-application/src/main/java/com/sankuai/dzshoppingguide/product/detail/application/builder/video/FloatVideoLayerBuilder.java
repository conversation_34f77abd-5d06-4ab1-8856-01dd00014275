package com.sankuai.dzshoppingguide.product.detail.application.builder.video;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.video.vo.FloatVideoLayerVO;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/20
 */
@Builder(
        moduleKey = ModuleKeyConstants.FLOAT_VIDEO_LAYER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class
        }
)
public class FloatVideoLayerBuilder extends BaseBuilder<FloatVideoLayerVO> {
    @Override
    public FloatVideoLayerVO doBuild() {
        return null;
    }
}
