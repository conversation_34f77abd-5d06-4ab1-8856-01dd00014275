package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 20:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdditionInfoResult extends FetcherReturnValueDTO {
    private String additionInfo;
}
