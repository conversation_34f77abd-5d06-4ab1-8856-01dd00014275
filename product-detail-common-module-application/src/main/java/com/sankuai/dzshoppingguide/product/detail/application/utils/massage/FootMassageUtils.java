package com.sankuai.dzshoppingguide.product.detail.application.utils.massage;

import com.dianping.lion.common.util.JsonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructContentModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageServiceTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/6 15:18
 */
@Slf4j
public class FootMassageUtils {

    private static final String RICH_TEXT = "richtext";

    public static <T> List<T> parseProcess(List<String> servicesProcessStr, Class<T> clazz) {
        try {
            if (CollectionUtils.isEmpty(servicesProcessStr)) {
                return Collections.emptyList();
            }
            return servicesProcessStr.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(process -> JsonUtils.fromJson(process, clazz))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("parseProcess error,process:{}", JsonCodec.encode(servicesProcessStr), e);
        }
        return Collections.emptyList();
    }

    public static ServiceProjectDTO getFirstMustSkuModel(ProductServiceProject serviceProject) {
        return Optional.ofNullable(serviceProject)
                .map(ProductServiceProject::getServiceProject)
                .map(DealAttrHelper::getFirstMustSkuFromServiceProject)
                .orElse(null);
    }

    /**
     * 获取各三级分类的服务项目名称
     */
    public static String getServiceFlowSkuName2(ProductAttr productAttr, String serviceType) {
        MassageServiceTypeEnum serviceTypeEnum = MassageServiceTypeEnum.getEnumByServiceType(serviceType);
        if (serviceTypeEnum == null) {
            return null;
        }
        switch (serviceTypeEnum) {
            case FOOT_MASSAGE:
            case SCRAPING:
            case PEDIATRIC_MASSAGE:
                // 足疗、刮痧、小儿推拿：三级类目名称，如刮痧
                return serviceType;
            case EAR_PICKING:
                // 服务手法
                return productAttr.getSkuAttrFirstValue("serviceTechnique");
            case MASSAGE:
            case ESSENTIAL_OIL_SPA:
            case ULNA:
                // 推拿/按摩、精油SOA、推拿正骨：只有当服务部位范围为全身时拼接，为局部部位时直接展示服务手法
                String bodyRegion = productAttr.getSkuAttrFirstValue("bodyRegion");
                String serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                if ("全身".equals(bodyRegion)) {
                    return "全身" + (StringUtils.isNotBlank(serviceTechnique) ? serviceTechnique : "");
                }
                return serviceTechnique;
            case MOXIBUSTION:
                // 艾灸：三级分类+服务手法+特色灸法， 用 "|"分割。 如 艾灸｜盒灸｜三伏灸
                serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                String moxibustionMethod = productAttr.getSkuAttrFirstValue("moxibustionMethod");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "｜" + serviceTechnique) + (StringUtils.isBlank(moxibustionMethod) ? "" : "｜" + moxibustionMethod);
            case CUP:
                // 拔罐：三级类目+服务手法，如拔罐（走罐）
                serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "（" + serviceTechnique + "）");
            case HEAD:
                // 头疗：三级类目+ "|"+服务部位+服务手法（若服务部位有多个，只取优先级最高的第一个）
                String headServiceBodyRange = productAttr.getSkuAttrFirstValue("serviceBodyRange");
                if (StringUtils.isNotBlank(headServiceBodyRange) && headServiceBodyRange.contains("、")) {
                    headServiceBodyRange = headServiceBodyRange.split("、")[0];
                }
                String headServiceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                return serviceType + "｜" + (StringUtils.isNotBlank(headServiceBodyRange) ? headServiceBodyRange : "") + (StringUtils.isNotBlank(headServiceTechnique) ? headServiceTechnique : "");
            case SPORTS_REHABILITATION:
                // 运动复健：服务项目
                return productAttr.getSkuAttrFirstValue("ServiceItems10");
            default:
                return null;
        }
    }

    public static String buildServiceItemPrice(ServiceProjectDTO firstMustSkuModel) {
        if (Objects.isNull(firstMustSkuModel) || Objects.isNull(firstMustSkuModel.getMarketPrice())) {
            return null;
        }
        String itemPrice = firstMustSkuModel.getMarketPrice();
        return String.format("¥%s", itemPrice);
    }

    public static String buildServiceItemDuration(ServiceProjectDTO firstMustSkuModel) {
        if (Objects.isNull(firstMustSkuModel) || CollectionUtils.isEmpty(firstMustSkuModel.getAttrs())) {
            return null;
        }
        String serviceDuration = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), MassageCPVConstant.SERVICE_DURATION);
        return StringUtils.isBlank(serviceDuration) ? null : serviceDuration + "分钟";
    }


    public static String getDesc(String additionInfoStr) {
        if (org.apache.commons.lang.StringUtils.isEmpty(additionInfoStr)) {
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        UniformStructModel uniformStruct = JsonCodec.decode(additionInfoStr, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private static String getRichText(List<UniformStructContentModel> structContentModels) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        UniformStructContentModel struct = structContentModels.stream().filter(model -> org.apache.commons.lang.StringUtils.isNotEmpty(model.getType()) && model.getType().equals(RICH_TEXT)).findFirst().orElse(null);
        if (struct == null || struct.getData() == null) {
            return null;
        }
        return struct.getData().toString();
    }
}
