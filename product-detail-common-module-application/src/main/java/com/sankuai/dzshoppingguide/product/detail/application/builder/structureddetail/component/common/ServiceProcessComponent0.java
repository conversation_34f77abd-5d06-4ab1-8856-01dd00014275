package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common;

import com.dianping.pigeon.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.ServiceFlowParseModel;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant.SERVICE_PROCESS;

/**
 * 足疗服务流程组件
 *
 * <AUTHOR>
 * @date 2025/5/6 16:06
 */
@Component
public class ServiceProcessComponent0<T> implements CommonComponent0<T> {

    public Optional<List<DealDetailStructuredDetailVO>> buildServiceItems(ProductAttr productAttr) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        if (Objects.isNull(productAttr)) {
            return Optional.empty();
        }
        List<String> serviceFlowStr = productAttr.getSkuAttrValues(SERVICE_PROCESS);
        if (CollectionUtils.isEmpty(serviceFlowStr)) {
            return Optional.empty();
        }

        List<ServiceFlowParseModel> serviceFlowParseModels = FootMassageUtils.parseProcess(serviceFlowStr, ServiceFlowParseModel.class);
        for (int i = 0; i < serviceFlowParseModels.size(); i++) {
            ServiceFlowParseModel serviceFlow = serviceFlowParseModels.get(i);
            String stepDuration = StringUtils.isNotBlank(serviceFlow.getStepDuration()) ? String.format("%s分钟", serviceFlow.getStepDuration()) : StringUtils.EMPTY;
            String stepTime = StringUtils.isNotBlank(stepDuration) ? stepDuration : serviceFlow.getStepTime() > 0 ? String.format("%s分钟", serviceFlow.getStepTime()) : StringUtils.EMPTY;
            DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.DETAIL_TYPE_3.getType())
                    .content(serviceFlow.getServiceMethod())
                    .subContent(serviceFlow.getServiceProcessInstructions())
                    .detail(stepTime)
                    .order(i + 1);
            if (i == 0) {
                builder.title("服务流程");
            }
            result.add(builder.build());
        }
        return Optional.of(result);
    }

    @Override
    public List<T> build(T vo) {
        return Collections.emptyList();
    }
}
