package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: created by hang.yu on 2023/3/14 10:52
 */
@Getter
@AllArgsConstructor
public enum MassageServiceTypeEnum {

    /**
     * 足疗
     */
    FOOT_MASSAGE(303, "足疗"),

    /**
     * 推拿/按摩
     */
    MASSAGE(303, "推拿/按摩"),

    /**
     * 艾灸
     */
    MOXIBUSTION(303, "艾灸"),

    /**
     * 采耳
     */
    EAR_PICKING(303, "采耳"),

    /**
     * 拔罐
     */
    CUP(303, "拔罐"),

    /**
     * 刮痧
     */
    SCRAPING(303, "刮痧"),

    /**
     * 精油SPA
     */
    ESSENTIAL_OIL_SPA(303, "精油SPA"),

    /**
     * 推拿正骨
     */
    ULNA(303, "推拿正骨"),

    /**
     * 头疗
     */
    HEAD(303, "头疗"),

    /**
     * 小儿推拿
     */
    PEDIATRIC_MASSAGE(303, "小儿推拿"),

    /**
     * 运动复健
     */
    SPORTS_REHABILITATION(303, "运动复健"),

    /**
     * 组合及多选一套餐
     */
    COMBINATION(303, "组合及多选一套餐"),
    ;


    /**
     * 二级类目id
     */
    private final int dealCategoryId;

    /**
     * 三级类目名称
     */
    private final String serviceType;

    public static MassageServiceTypeEnum getEnumByServiceType(String serviceType) {
        for (MassageServiceTypeEnum value : MassageServiceTypeEnum.values()) {
            if (value.getServiceType().equals(serviceType)) {
                return value;
            }
        }
        return null;
    }

}
