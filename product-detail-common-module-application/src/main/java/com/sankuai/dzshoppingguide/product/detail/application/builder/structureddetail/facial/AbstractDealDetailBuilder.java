package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;

import java.util.List;

public abstract class AbstractDealDetailBuilder {

    public abstract List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context);

    public String getIdentifyKey() {
        return this.getClass().getSimpleName();
    }
}
