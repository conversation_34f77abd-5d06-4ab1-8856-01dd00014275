package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.reserveinfo;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.MassageBookProductConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.ShopScheduleTimeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.ShopScheduleTimeResult;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateAndTimeUtils;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 15:07
 */
@Fetcher(
        timeout = 50000L,
        previousLayerDependencies = {ShopIdMapperFetcher.class, ShopScheduleTimeFetcher.class, CityIdMapperFetcher.class}
)
@Slf4j
public class ReserveInfoFetcher extends NormalFetcherContext<ReserveQueryResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;


    @Override
    protected CompletableFuture<ReserveQueryResult> doFetch() {
        try {
            if (request.getProductTypeEnum() != ProductTypeEnum.RESERVE) {
                return CompletableFuture.completedFuture(null);
            }
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
            ShopScheduleTimeResult shopScheduleTimeResult = getDependencyResult(ShopScheduleTimeFetcher.class);
            if(request == null || request.getShepherdGatewayParam() == null){
                return CompletableFuture.completedFuture(null);
            }
           return compositeAtomService.queryReserveProduct(buildReserveQueryRequest(request,shopIdMapper,cityIdMapper,shopScheduleTimeResult)).thenApply(res -> {
               if (res == null || CollectionUtils.isEmpty(res.getProducts())) {
                   return null;
               }
               return new ReserveQueryResult(res.getProducts());
           });
        } catch (Exception e) {
            log.error("ReserveInfoFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private ReserveQueryRequest buildReserveQueryRequest(ProductDetailPageRequest request, ShopIdMapper shopIdMapper,CityIdMapper cityIdMapper,ShopScheduleTimeResult shopScheduleTimeResult) {
        List<Integer> productIds = Optional.ofNullable(request).map(ProductDetailPageRequest::getProductId).map(Long::intValue).map(Lists::newArrayList).orElse(Lists.newArrayList());
        ReserveQueryRequest reserveQueryRequest = new ReserveQueryRequest();
        reserveQueryRequest.setPlanId("11900008");
        reserveQueryRequest.setProductIds(productIds);
        reserveQueryRequest.setExtParams(buildParams(request,shopIdMapper,cityIdMapper,shopScheduleTimeResult));
        return reserveQueryRequest;
    }

    private Map<String, Object> buildParams( ProductDetailPageRequest request, ShopIdMapper shopIdMapper,CityIdMapper cityIdMapper,ShopScheduleTimeResult shopScheduleTimeResult) {
        int platform = request.getPlatformEnum().getCode();
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put(MassageBookProductConstants.Params.CITY_ID, isMt ? cityIdMapper.getMtCityId():cityIdMapper.getDpCityId());
        extParams.put(MassageBookProductConstants.Params.PLATFORM, platform);
        extParams.put(MassageBookProductConstants.Params.UA_CODE, isMt ? VCClientTypeEnum.MT_APP.getCode():VCClientTypeEnum.DP_APP.getCode());
        extParams.put(MassageBookProductConstants.Params.LAT, request.getUserLat());
        extParams.put(MassageBookProductConstants.Params.LNG, request.getUserLng());
        extParams.put(MassageBookProductConstants.Params.USER_ID, isMt ? request.getMtUserId() : request.getDpUserId());
        extParams.put(MassageBookProductConstants.Params.SHOP_IDL, isMt ? shopIdMapper.getMtBestShopId():shopIdMapper.getDpBestShopId());
        extParams.put(MassageBookProductConstants.Params.QUERY_PRICE_PROMO_SCENE, RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        extParams.put(MassageBookProductConstants.Params.QUERY_FUTURE_PRICE_PROMO_SCENE, RequestSceneEnum.MERGE_CARD_FUSION_WithDealPromo.getScene());
        extParams.put(MassageBookProductConstants.Params.SELECT_DATES, buildSelectDates(shopScheduleTimeResult));
        // extParams.put(MassageBookProductConstants.Params.SHOP_UUID, ParamsUtil.getStringSafely(ctx, PmfConstants.Params.shopUuid));
        // extParams.put(MassageBookProductConstants.Params.TECH_ID, MassageUtils.getTechId(ctx, PmfConstants.Params.techId));
        // extParams.put(MassageBookProductConstants.Params.DAYS, ParamsUtil.getIntSafely(groupParams, PmfConstants.Params.days));
        extParams.put(MassageBookProductConstants.Params.DAYS, 1);
        long shopTodayScheduleStartTime = Optional.ofNullable(shopScheduleTimeResult).map(ShopScheduleTimeResult::getTodayScheduleStartTime).orElse(0L);
        long shopTodayScheduleEndTime = Optional.ofNullable(shopScheduleTimeResult).map(ShopScheduleTimeResult::getTodayScheduleEndTime).orElse(0L);
        extParams.put(MassageBookProductConstants.Params.START_TIME, shopTodayScheduleStartTime);
        extParams.put(MassageBookProductConstants.Params.END_TIME, shopTodayScheduleEndTime);
        // extParams.put(MassageBookProductConstants.Params.SHOP_IDENTIFIER, CtxUtils.getShopIdentifier(ctx)); // 门店标识信息是什么东西
        extParams.put(MassageBookProductConstants.Params.SPU_TYPE, 521);
        extParams.put(MassageBookProductConstants.Params.URL_EXTRA_CODE,"MassageExtraParamsService");
        extParams.put(MassageBookProductConstants.Params.SUBMIT_PAGE_SOURCE,0);
        extParams.put(MassageBookProductConstants.Params.APP_VERSION, Optional.of(request).map(ProductDetailPageRequest::getShepherdGatewayParam).map(ShepherdGatewayParam::getAppVersion).orElse(StringUtils.EMPTY));
        // 足疗主题中用dpId接收deviceId
        extParams.put(MassageBookProductConstants.Params.DP_ID, Optional.of(request).map(ProductDetailPageRequest::getShepherdGatewayParam).map(ShepherdGatewayParam::getDeviceId).orElse(""));
        extParams.put(MassageBookProductConstants.Params.UNION_ID, Optional.of(request).map(ProductDetailPageRequest::getShepherdGatewayParam).map(ShepherdGatewayParam::getUnionid).orElse(""));
        // 底层报价需要区分ios和android
        extParams.put(MassageBookProductConstants.Params.CLIENT_TYPE, Optional.of(request).map(ProductDetailPageRequest::getMobileOSType).map(MobileOSTypeEnum::getCode).orElse(""));
        // extParams.put(GeneralProductPaddingConstants.Params.DOU_HU_INFO, paddingDouHuInfo(ctx));
        //神会员新增的参数(userId相关，cityId相关、pricePageSource、pricePosition)
        extParams.put(MassageBookProductConstants.Params.MT_USER_ID, request.getMtUserId());
        extParams.put(MassageBookProductConstants.Params.DP_USER_ID, request.getDpUserId());
        // Integer shopDpCityId = Optional.ofNullable(productBestShop).map(ProductBestShop::getDpPoiDTO).map(DpPoiDTO::getCityId).orElse(0);
        // todo
        // extParams.put(MassageBookProductConstants.Params.mtVirtualUserId, ParamsUtil.getLongSafely(cxt, PmfConstants.Params.mtVirtualUserId));
        // extParams.put(MassageBookProductConstants.Params.dpUserLocalCityId, ParamsUtil.getIntSafely(cxt, PmfConstants.Params.dpUserLocalCityId));
        // extParams.put(MassageBookProductConstants.Params.mtUserLocalCityId, ParamsUtil.getIntSafely(cxt, PmfConstants.Params.mtUserLocalCityId));
        // extParams.put(MassageBookProductConstants.Params.userLocalCityId, ParamsUtil.getIntSafely(cxt, PmfConstants.Params.userLocalCityId));
        // extParams.put(MassageBookProductConstants.Params.SHOP_MT_CITY_ID, ParamsUtil.getIntSafely(cxt, PmfConstants.Params.shopMtCityId));
        // extParams.put(MassageBookProductConstants.Params.SHOP_DP_CITY_ID, shopDpCityId);
        extParams.put(MassageBookProductConstants.Params.pricePosition, "2104");
        extParams.put(MassageBookProductConstants.Params.pricePageSource,"8");
        extParams.put(MassageBookProductConstants.Params.queryNewTuanDetail,true);
        return extParams;
    }

    private List<Long> buildSelectDates(ShopScheduleTimeResult shopScheduleTimeResult) {
        long shopTodayScheduleStartTime = Optional.ofNullable(shopScheduleTimeResult).map(ShopScheduleTimeResult::getTodayScheduleStartTime).orElse(0L);
        return Lists.newArrayList(shopTodayScheduleStartTime);
        // Object dayValue = ParamsUtil.getValue(groupParams, PmfConstants.Params.days, 1);
        // int days = Optional.ofNullable(dayValue).map(String::valueOf).map(NumberUtils::toInt).orElse(1);
        // if (days == 1) {
        //     return Lists.newArrayList(shopTodayScheduleStartTime);
        // }
        // return buildAllSelectDates(shopTodayScheduleStartTime, days);
    }

    private List<Long> buildAllSelectDates(long shopTodayScheduleStartTime, long calPriceDays) {
        List<Long> all = new ArrayList<>();
        if (shopTodayScheduleStartTime < DateAndTimeUtils.convertZeroLongTime(0)) {
            // 从昨天开始的话，需要多查1天
            calPriceDays ++;
        }
        for (long i = 0; i < calPriceDays; i++) {
            all.add(shopTodayScheduleStartTime + 24 * 3600 * 1000 * i);
        }
        return all;
    }
}
