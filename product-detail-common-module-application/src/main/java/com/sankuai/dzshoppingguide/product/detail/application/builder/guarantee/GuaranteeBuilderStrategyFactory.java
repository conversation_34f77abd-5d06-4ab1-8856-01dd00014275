package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee;

import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障模块构建策略工厂
 */
@Deprecated
public class GuaranteeBuilderStrategyFactory {
    private ApplicationContext applicationContext;

    private static final Map<GuaranteeStrategyEnum, GuaranteeBuilderStrategy> STRATEGY_MAP = Maps.newEnumMap(GuaranteeStrategyEnum.class);

    public GuaranteeBuilderStrategy getStrategy(GuaranteeStrategyEnum strategyEnum) {
        return STRATEGY_MAP.get(strategyEnum);
    }

//    @Override
//    public void afterPropertiesSet() throws Exception {
//        applicationContext.getBeansOfType(GuaranteeBuilderStrategy.class)
//                .values()
//                .forEach(strategy -> STRATEGY_MAP.put(strategy.getStrategyEnum(), strategy));
//    }
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        this.applicationContext = applicationContext;
//    }
}
