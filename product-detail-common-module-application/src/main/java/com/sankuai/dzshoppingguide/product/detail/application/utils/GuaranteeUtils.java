package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;

/**
 * <AUTHOR>
 * @date 2025-03-14
 * @desc
 */
public class GuaranteeUtils {
    public static boolean checkPriceProtectionValid(ObjectGuaranteeTagDTO objectGuaranteeTagDTO) {
        if (objectGuaranteeTagDTO == null) {
            return false;
        }
        PriceProtectionTagDTO priceProtectionTagDTO = objectGuaranteeTagDTO.getPriceProtectionTag();
        return priceProtectionTagDTO != null
                && priceProtectionTagDTO.getValid() != null
                && priceProtectionTagDTO.getValid()
                && priceProtectionTagDTO.getValidityDays() != null;
    }
}
