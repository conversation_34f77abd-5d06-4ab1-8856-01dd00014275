package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "必选Sku分组")
public class MustSkusGroupModel {
    @FieldDoc(name = "skus", description = "Sku服务项目列表")
    List<SkuModel> skus;
}
