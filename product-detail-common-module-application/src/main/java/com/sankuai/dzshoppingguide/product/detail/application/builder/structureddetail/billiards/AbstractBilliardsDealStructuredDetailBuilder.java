package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.billiards;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/28 16:54
 * @Description: 二级类目: 台球
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class
        }
)
abstract class AbstractBilliardsDealStructuredDetailBuilder extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> {

    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductServiceProject serviceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (Objects.isNull(serviceProject) || Objects.isNull(productAttr)) {
            return null;
        }

        // 构建服务详情模块
        List<DealDetailStructuredDetailVO> serviceDetailModule = buildServiceDetailModule(serviceProject, productAttr);
        ModuleDetailStructuredDetailVO result = new ModuleDetailStructuredDetailVO();
        result.setDealDetails(serviceDetailModule);
        return result;
    }

    private List<DealDetailStructuredDetailVO> buildServiceDetailModule(ProductServiceProject serviceProject, ProductAttr productAttr) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();

        // 1. 台费
        List<DealDetailStructuredDetailVO> feesDetailsModule = buildFeesModule(productAttr);
        safeAddAllWithNewLine(result, feesDetailsModule, true);

        // 2. 使用流程
        List<DealDetailStructuredDetailVO> usageProcessModule = buildUsageProcessModule(serviceProject);
        safeAddAllWithNewLine(result, usageProcessModule, true);

        // 过滤空值
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<DealDetailStructuredDetailVO> buildUsageProcessModule(ProductServiceProject serviceProject) {
        // TODO
        return Lists.newArrayList();
    }

    public void safeAddAllWithNewLine(List<DealDetailStructuredDetailVO> result, List<DealDetailStructuredDetailVO> addition, boolean newLine) {
        if (Objects.nonNull(result) && Objects.nonNull(addition)) {
            result.addAll(addition);
            if (newLine) {
                // 换行符
                result.add(buildStructuredDetailVO(null, null, null, ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType()));
            }
        }
    }

    private List<DealDetailStructuredDetailVO> buildFeesModule(ProductAttr productAttr) {
        List<DealDetailStructuredDetailVO> feesModule = Lists.newArrayList();
        // 构建台费标题
        DealDetailStructuredDetailVO feesTitle = buildFeesTitle(productAttr);
        feesModule.add(feesTitle);
        // 构造台费详情
        List<DealDetailStructuredDetailVO> feesDetails = buildFeesDetails(productAttr);
        safeAddAllWithNewLine(feesModule, feesDetails, false);
        return feesModule;
    }

    protected abstract List<DealDetailStructuredDetailVO> buildFeesDetails(ProductAttr productAttr);

    protected abstract DealDetailStructuredDetailVO buildFeesTitle(ProductAttr productAttr);

    protected DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type) {
        DealDetailStructuredDetailVO structuredDetailVO = DealDetailStructuredDetailVO.builder()
                .type(type)
                .build();
        if (title != null) {
            structuredDetailVO.setTitle(title);
        }
        if (content != null) {
            structuredDetailVO.setContent(content);
        }
        if (subcontent != null) {
            structuredDetailVO.setSubContent(subcontent);
        }
        return structuredDetailVO;
    }

}
