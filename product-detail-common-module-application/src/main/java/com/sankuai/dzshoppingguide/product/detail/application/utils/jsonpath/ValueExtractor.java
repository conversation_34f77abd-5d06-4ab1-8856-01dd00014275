package com.sankuai.dzshoppingguide.product.detail.application.utils.jsonpath;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;

public class ValueExtractor {
    private static final Configuration JACKSON_CONFIG = Configuration.builder()
            .jsonProvider(new JacksonJsonProvider())
            .mappingProvider(new JacksonMappingProvider())
            .build();

    public static Object extractValue(Object sourceData, String jsonPath) {
        if (sourceData == null || jsonPath == null) {
            throw new IllegalArgumentException("Source data and JSONPath cannot be null");
        }

        try {
            DocumentContext context = JsonPath.using(JACKSON_CONFIG).parse(sourceData);
            return context.read(jsonPath);
        } catch (Exception e) {
            throw new RuntimeException(
                    "Failed to extract value with JSONPath: " + jsonPath +
                            "\nSource data type: " + sourceData.getClass().getSimpleName(),
                    e
            );
        }
    }
}
