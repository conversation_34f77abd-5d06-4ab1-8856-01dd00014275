package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/14 16:26
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER, moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class, ProductAttrFetcher.class, SkuAttrFetcher.class,
            ProductCategoryFetcher.class, ProductServiceProjectFetcher.class, SkuDefaultSelectFetcher.class}
)
@Slf4j
public class MassageReminderInfoBuilder extends AbstractReminderInfoBuilder {

    // 定价方式：PricingMethod 1：全时段统一 2：分时段定价
    private static final String PRICING_METHOD = "PricingMethod";

    // segmentType 1:白天/夜间档 2:工作日/周末
    private static final String SEGMENT_TYPE = "segmentType";

    // 售卖价格：1.档位开始时间：gearStartTime 2.档位结束时间：gearEndTime
    private static final String GEAR_START_TIME = "gearStartTime";
    private static final String GEAR_END_TIME = "gearEndTime";

    // 售卖分档：SellDifferentGrades
    private static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";

    // 工作日/周末挡
    private static final String WEEKEND_TIME = "weekendTime";

    public static final String PARTIAL_DATE = "部分日期";

    private SkuAttr skuAttr;

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }

        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);

        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (productAttr == null) {
            return null;
        }
        // 区分闲忙时
        String priceMethod = productAttr.getSkuAttrFirstValue(PRICING_METHOD);
        // segmentType 1:白天/夜间档 2:工作日/周末
        String segmentType = productAttr.getSkuAttrFirstValue(SEGMENT_TYPE);

        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        long skuId = Optional.ofNullable(skuDefaultSelect).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);

        skuAttr = getDependencyResult(SkuAttrFetcher.class);

        ProductServiceProject productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        FetcherResultDTO fetcherResultDTO = FetcherResultDTO.builder().productBaseInfo(baseInfo)
                .productAttr(productAttr).productServiceProject(productServiceProject)
                .productSecondCategoryId(getCategoryId()).build();

        // 不区分闲忙时,走原有的逻辑. 1：全时段统一 2：分时段定价
        if (StringUtils.isBlank(priceMethod) || !StringUtils.equals("2", priceMethod)
                || StringUtils.isBlank(segmentType) || skuAttr == null || skuId <= 0) {
            return processBasicReminderInfo(fetcherResultDTO, baseReminderInfo);
        }

        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();

        // 预约时间·日期+时间段+可用·有效时间
        if (StringUtils.equals("1", segmentType)) {
            // 1:白天/夜间档
            int secondCategoryId = getCategoryId();
            processDayOrNightGear(baseInfo, secondCategoryId, skuId, contents);
        } else if (StringUtils.equals("2", segmentType)) {
            // 2:工作日/周末
            processWeekDay(fetcherResultDTO, skuId, contents);
        } else {
            // 兜底场景
            return processBasicReminderInfo(fetcherResultDTO, baseReminderInfo);
        }
        return baseReminderInfo;
    }

    private void processWeekDay(FetcherResultDTO fetcherResultDTO, long skuId,
            List<GuaranteeInstructionsContentVO> contents) {
        // 日期 有变动
        // 时间段
        String dateDoc;
        List<Integer> disableDays = AvailableTimeHelper.getDisableDays(fetcherResultDTO.getProductBaseInfo());
        if (CollectionUtils.isNotEmpty(disableDays)) {
            dateDoc = PARTIAL_DATE;
        } else {
            // [1,2,3,4] 周一至周四
            // [1,2,3,4,5] 周一至周五
            // [5,6,7] 周六至周日
            // [6,7] 周六周日
            String weekDay = skuAttr.getSkuAttrFirstValue(skuId, WEEKEND_TIME);
            switch (weekDay) {
                case "[1,2,3,4]":
                    dateDoc = "周一至周四";
                    break;
                case "[1,2,3,4,5]":
                    dateDoc = "周一至周五";
                    break;
                case "[5,6,7]":
                    dateDoc = "周五至周日";
                    break;
                case "[6,7]":
                    dateDoc = "周六周日";
                    break;
                default:
                    dateDoc = StringUtils.EMPTY;
            }
        }

        String timeOfDayDoc = AvailableTimeHelper.getTimeOfDayDoc(fetcherResultDTO);

        if (StringUtils.isNotBlank(dateDoc) || StringUtils.isNotBlank(timeOfDayDoc)) {
            ReminderInfoUtils.buildReminderInfo(dateDoc + timeOfDayDoc + "可用").ifPresent(contents::add);
        }

        // 有效期
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(fetcherResultDTO.getProductBaseInfo()))
                .ifPresent(contents::add);
    }

    private void processDayOrNightGear(ProductBaseInfo baseInfo, int secondCategoryId, long skuId,
            List<GuaranteeInstructionsContentVO> contents) {
        // 时间段 有变动
        // 日期
        String dateDoc = AvailableTimeHelper.getDateDoc(baseInfo, secondCategoryId);

        String timeOfDayDoc;
        String startTime = skuAttr.getSkuAttrFirstValue(skuId, GEAR_START_TIME);
        // endTime次日的时间会出现 31:00 表示 次日 7:00的情况,因此需要减掉24:00
        String endTime = AvailableTimeHelper.getTimeOfDayDoc(skuAttr.getSkuAttrFirstValue(skuId, GEAR_END_TIME));
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            timeOfDayDoc = StringUtils.EMPTY;
        } else {
            timeOfDayDoc = startTime + "-" + endTime;
        }

        // 可用时间
        if (StringUtils.isNotBlank(dateDoc) || StringUtils.isNotBlank(timeOfDayDoc)) {
            ReminderInfoUtils.buildReminderInfo(dateDoc + timeOfDayDoc + "可用").ifPresent(contents::add);
        }
        // 有效期
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(baseInfo)).ifPresent(contents::add);
    }

    private @NotNull Integer getCategoryId() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
    }

    private ProductDetailReminderVO processBasicReminderInfo(FetcherResultDTO fetcherResults,
            ProductDetailReminderVO baseReminderInfo) {
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }
        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();
        // 可用时间
        ReminderInfoUtils.buildReminderInfo(AvailableTimeHelper.getAvailableTime(fetcherResults))
                .ifPresent(contents::add);
        // 有效期
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(fetcherResults.getProductBaseInfo()))
                .ifPresent(contents::add);

        return baseReminderInfo;
    }
}
