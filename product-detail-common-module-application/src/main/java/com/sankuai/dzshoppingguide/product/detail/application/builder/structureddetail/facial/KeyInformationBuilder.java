package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 重点信息展示模块
 */
@Component
public class KeyInformationBuilder extends AbstractDealDetailBuilder {
    
    @Autowired
    private List<AbstractSubModuleBuildStrategy> strategies;

    @Override
    public List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        return strategies.stream()
                // 过滤出重点信息模块的构建策略执行器
                .filter(strategy -> Objects.equals(strategy.getSubModuleName(), SubModuleKey.KEY_INFO))
                .filter(strategy -> strategy.isHit(context))
                .findFirst()
                .map(strategy -> strategy.build(context))
                .orElse(Collections.emptyList());
    }
}
