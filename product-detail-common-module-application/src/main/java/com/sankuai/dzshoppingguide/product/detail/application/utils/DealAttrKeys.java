package com.sankuai.dzshoppingguide.product.detail.application.utils;

public class DealAttrKeys {

    public static final String TORT = "tort";
    public static final String TORT_VALUE = "1";
    public static final String HIDE_TYPE = "hide_type";
    /**
     * 普通侵权屏蔽
     */
    public static final String HIDE_TYPE_VALUE_COMMON = "1";
    /**
     * 二次处置屏蔽
     */
    public static final String HIDE_TYPE_VALUE_SECONDARY_TREATMENT = "2";
    /**
     * 一键下线屏蔽
     */
    public static final String HIDE_TYPE_VALUE_EMERGENT_BLOCK = "3";
    public static final String RESERVATION = "reservation_is_needed_or_not";
    public static final String RESERVATION_2 = "reservation_is_needed_or_not_2";
    public static final String RESERVATION_3 = "reservation_is_needed_or_not_3";
    public static final String VOUCHER = "sys_deal_universal_type";
    public static final String TIMES_AVAILABLE_ALL = "times_available_all"; // 是
    public static final String HOLIDAY_AVAILABLE = "calc_holiday_available";
    public static final String NO_TIMES_LIMIT = "token_time_use_limit_1"; //可
    public static final String ALL_CAN_USE = "voucher_limit_of_using_1"; //是
    public static final String AVAILABLE_TIME = "available_time";

    public static final String RESERVATION_VALUE_YES = "是";
    public static final String VOUCHER_VALUE = "2";
    public static final String PRE_SALE_TAG = "preSaleTag";
    public static final String PRE_SALE_DEAL_GROUP_PRICE = "rawDealGroupPrice";
    public static final String SUPPORT_SHOP_SERVICE = "support_shop_service";
    public static final String SUPPORT_HOME_SERVICE = "support_home_service";
    public static final String TOOTH_SUIT_PEOPLE = "tooth_suit_people";
    public static final String RESERVATION_NUMBER = "reservation_number";
    public static final String SERVICE_TYPE = "service_type";

    public static final String STANDARD_DEALGROUP = "standardDealGroup";

    public static final String DEAL_GROUP_TEMPLATE_KEY = "standardDealGroupKey";
    public static final String KEY_CATEGORY = "category";
    public static final String IS_SELECTED_DEAL = "isselecteddeal";
    public static final String VOUCHER_LIMIT_OF_USING_2 = "voucher_limit_of_using_2";

    public static final String EDU_SUITABLE_AGE = "eduSuitableAge";
    public static final String EDU_FEMALE_ONLY = "female_only";

    /**
     * 口腔齿科团详标签展示
     */
    public static final String ORAL_DENTISTRY_RULE= "oral_dentistry_tuanxiang_rule";

    /**
     * 预热单行业属性
     */
    public static final String WARM_UP_START_TIME = "warmUpStartTime";
    public static final String USING_STOCK_PLAN = "usingStockPlan";

    /**
     * 配镜-每次消费限用几张
     */
    public static final String  LIMIT_OF_USING_EACH_EYE = "limit_of_using_each_eye";

    /**
     * 团购属性配置：单次核销次数
     */
    public static final String SINGLE_VERIFICATION_QUANTITY_DESC = "single_verification_quantity_desc";

    /**
     * 套餐属性配置：可用次数
     */
    public static final String SYS_MULTI_SALE_NUMBER = "sys_multi_sale_number";
    /**
     * 酒吧行业可用时间属性
     */
    public static final String WINE_BAR_AVAILABLE_TIME = "winebar_available_time";

    /**
     * 月子-房型ID
     */
    public static final String CONFINEMENT_ROOM_ID = "room_id";

}
