package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.AggregateFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterResultCheckException;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.model.DealBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/9 22:31
 */
@AggregateFetcher
@Slf4j
public class QueryCenterAggregateFetcher extends AggregateFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue> {

    @Resource
    public QueryCenterAclService queryCenterAclService;

    @Override
    protected QueryByDealGroupIdRequestBuilder initRequest() {
        QueryByDealGroupIdRequestBuilder requestBuilder = QueryCenterAclService.getBaseRequestBuilder(this.request);
        requestBuilder
                .dealGroupId(DealGroupIdBuilder.builder().all())//Id转换
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())//基础信息
                .dealId(DealIdBuilder.builder().id().bizDealId())//skuId
                .dealBasicInfo(DealBasicInfoBuilder.builder().all())//sku基本信息
        ;
        return requestBuilder;
    }

    @Override
    protected CompletableFuture<FetcherResponse<QueryCenterAggregateReturnValue>> initFuture(final QueryByDealGroupIdRequestBuilder requestBuilder) {
        final QueryByDealGroupIdRequest queryCenterRequest = requestBuilder.build();
        try {
            CompletableFuture<QueryDealGroupListResponse> thriftFuture = queryCenterAclService.query(queryCenterRequest);
            return thriftFuture.thenApply(response -> {
                        if (response == null) {
                            throw new QueryCenterException("查询中心response为null");
                        }
                        DealGroupDTO dealGroupDTO = Optional.of(response)
                                .map(QueryDealGroupListResponse::getData)
                                .map(QueryDealGroupListResult::getList)
                                .orElse(new ArrayList<>()).stream()
                                .findFirst().orElse(null);
                        if (dealGroupDTO == null) {
                            Cat.logEvent("QueryCenterStatus", "Empty");
                            throw new QueryCenterException("查询中心未返回商品数据");
                        }
                        Cat.logEvent("QueryCenterStatus", "Normal");
                        checkIfPartlyFail(response);
                        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
                        returnValue.setDealGroupDTO(dealGroupDTO);
                        return FetcherResponse.succeed(returnValue);
                    }).exceptionally(throwable -> {
                        log.error("查询中心创建future失败，会导致商品详情页核心模块不展示", throwable);
                        return FetcherResponse.fail(new QueryCenterException(throwable));
                    });
        } catch (TException e) {
            log.error("查询中心创建future失败，会导致商品详情页核心模块不展示,", e);
            return CompletableFuture.completedFuture(FetcherResponse.fail(new QueryCenterException(e)));
        }
    }

    private void checkIfPartlyFail(final QueryDealGroupListResponse response) {
        try {
            ResponseCodeEnum responseCodeEnum = ResponseCodeEnum.parse(response.getCode());
            Cat.logEvent("QueryCenter", Optional.ofNullable(responseCodeEnum).map(Enum::name).orElse("UNKNOWN_CODE"));
            if (CollectionUtils.isNotEmpty(response.getExceptionDealGroupField())) {
                log.error("查询Product字段部分异常，productId={}，productType={}，Field={}",
                        this.request.getProductId(), this.request.getProductTypeEnum().name(),
                        JSON.toJSONString(response.getExceptionDealGroupField()),
                        new QueryCenterResultCheckException("查询Product字段部分异常"));
            }
            if (CollectionUtils.isNotEmpty(response.getExceptionDealField())) {
                log.error("查询Sku字段部分异常，productId={}，productType={}，Field={}",
                        this.request.getProductId(), this.request.getProductTypeEnum().name(),
                        JSON.toJSONString(response.getExceptionDealField()),
                        new QueryCenterResultCheckException("查询Sku字段部分异常"));
            }
            if (CollectionUtils.isNotEmpty(response.getExceptionSpuField())) {
                log.error("查询Spu字段部分异常，productId={}，productType={}，Field={}",
                        this.request.getProductId(), this.request.getProductTypeEnum().name(),
                        JSON.toJSONString(response.getExceptionSpuField()),
                        new QueryCenterResultCheckException("查询Spu字段部分异常"));
            }
        } catch (Throwable throwable) {
            log.error("检查是否部分失败代码异常", new QueryCenterResultCheckException(throwable));
        }
    }

}
