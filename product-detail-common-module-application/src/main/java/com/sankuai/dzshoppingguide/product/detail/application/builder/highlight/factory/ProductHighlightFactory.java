package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder.DefaultHighlightModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder.MassageDealHighlightsModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder.ReserveHighlightModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.highlights.vo.HighlightsModuleVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-06
 * @desc
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.HIGHLIGHTS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class
        }
)
public class ProductHighlightFactory extends BaseBuilderFactory<HighlightsModuleVO> {
    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        if (Objects.equals(request.getProductTypeEnum(), ProductTypeEnum.RESERVE)) {
            return ReserveHighlightModuleBuilder.class;
        } else if (productCategory.getProductSecondCategoryId() == 303) {
            return MassageDealHighlightsModuleBuilder.class;
        }
        return DefaultHighlightModuleBuilder.class;
    }
}
