package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-23
 * @desc 茶馆 体验项目 631
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductCategoryFetcher.class,
                ProductServiceProjectFetcher.class
        }
)
public class TeaHouseExperienceProjectDealStructuredDetailBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {


    @Override
    DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
        // 项目时长
        return DealDetailStructuredDetailVO.builder()
                .title("套餐详情")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_11.getType())
                .build();
    }

    @Override
    List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
        if (Objects.isNull(productAttr)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> packageDetails = Lists.newArrayList();
        // 项目类型
        String projectType = productAttr.getSkuAttrFirstValue("ProjectType");
        if (StringUtils.isNotBlank(projectType)) {
            packageDetails.add(buildStructuredDetailVO("项目类型", projectType,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 体验时长
        String duration = productAttr.getSkuAttrFirstValue("duration");
        String durationUnit = productAttr.getSafeString("DurationUnit", "");
        if (StringUtils.isNotBlank(duration)) {
            packageDetails.add(buildStructuredDetailVO("体验时长", String.format("%s%s", duration, durationUnit), null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 使用人数
        String applicableUsers = productAttr.getSkuAttrFirstValue("fixed_people_amount_gate");
        if (StringUtils.isNotBlank(applicableUsers)) {
            packageDetails.add(buildStructuredDetailVO("使用人数", String.format("%s人",applicableUsers),null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 免费餐食
        List<String> freeFood = productAttr.getSkuAttrValue("freeFood");
        if (CollectionUtils.isNotEmpty(freeFood)) {
            packageDetails.add(buildStructuredDetailVO("免费餐食", ProductAttr.joinListByDelimiter(freeFood, "、"),null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 附加服务
        String additionalServices = productAttr.getSkuAttrFirstValue("px_additional_service");
        if (StringUtils.isNotBlank(additionalServices)) {
            packageDetails.add(buildStructuredDetailVO("附加服务", additionalServices,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        return packageDetails;
    }
}
