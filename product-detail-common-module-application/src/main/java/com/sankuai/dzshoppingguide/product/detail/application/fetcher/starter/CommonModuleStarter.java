package com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.dto.StartFetcherReturnValue;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 13:35
 */
@Fetcher(
        isStartFetcher = true
)
public class CommonModuleStarter extends NormalFetcherContext<StartFetcherReturnValue> {

    @Override
    protected CompletableFuture<StartFetcherReturnValue> doFetch() {
        return CompletableFuture.completedFuture(new StartFetcherReturnValue());
    }

}
