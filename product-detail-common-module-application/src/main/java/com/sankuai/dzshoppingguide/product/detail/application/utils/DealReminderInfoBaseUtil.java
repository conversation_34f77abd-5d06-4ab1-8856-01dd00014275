package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FetcherResultEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealreserve.DealOnlineReserveResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/10 21:17
 */
public class DealReminderInfoBaseUtil {
    public static ProductDetailReminderVO getBaseReminderInfo(Map<FetcherResultEnum, FetcherReturnValueDTO> fetcherResultMap, ProductDetailPageRequest request) {
        if ( MapUtils.isEmpty(fetcherResultMap)) {
            return null;
        }
        // 获取对应的结果集
        ProductAttr productAttr = FetcherResultEnum.PRODUCT_ATTR.getResult(fetcherResultMap);
        ProductCategory productCategory = FetcherResultEnum.PRODUCT_CATEGORY.getResult(fetcherResultMap);
        DealOnlineReserveResult dealOnlineReserveResult = FetcherResultEnum.DEAL_ONLINE_RESERVE.getResult(fetcherResultMap);
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return null;
        }
        List<AttrDTO> attrList = productAttr.getSkuAttrList();
        ProductDetailReminderVO bookingDetailsReminderInfoVO = new ProductDetailReminderVO();

        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        bookingDetailsReminderInfoVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        List<String> reservationInfo = DealReminderInfoBaseUtil.getReservationInfo(attrList, request, productCategory, dealOnlineReserveResult);
        if (CollectionUtils.isNotEmpty(reservationInfo)) {
            List<String> reservations = reservationInfo.subList(0, 1);
            List<GuaranteeInstructionsContentVO> reminderInfoVOS = reservations.stream().filter(StringUtils::isNotBlank).map(item -> {
                Optional<GuaranteeInstructionsContentVO> reminderInfoVO = ReminderInfoUtils.buildReminderInfo(item);
                return reminderInfoVO.orElse(null);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            contents.addAll(reminderInfoVOS);
        }
        bookingDetailsReminderInfoVO.setContents(contents);
        return bookingDetailsReminderInfoVO;
    }

    private static List<String> getReservationInfo(List<AttrDTO> attrList, ProductDetailPageRequest request, ProductCategory categoryDTO, DealOnlineReserveResult dealOnlineReserveResult) {
        List<String> reservationInfo = new ArrayList<>();
        String reservation = null;
        //从上单信息（团单属性）中判断是否需预约、是否支持上门、是否支持到店
        boolean needReservation = DealAttrHelper.needReservation(attrList);
        boolean supportHome = DealAttrHelper.isSupportHomeService(attrList);
        boolean supportShop = DealAttrHelper.isSupportShopService(attrList);
        // 判断是否为预订单，仅支持可预约团单
        boolean needPreOrder = isPreOrderDeal(request,categoryDTO);
        int categoryId = Optional.ofNullable(categoryDTO).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        if ( LionConfigUtils.reserveAfterPurchase(categoryId)) {
            if (reserveOnline(dealOnlineReserveResult)) {
                reservation = needPreOrder ? "在线预订" : "在线预约";
            } else if (supportHome) {
                reservation = needPreOrder ? "预订上门" : "预约上门";
            } else if (supportShop && needReservation) {
                reservation = needPreOrder ? "预订到店" : "预约到店";
            } else if (needReservation) {
                reservation = needPreOrder ? "需预订" : "需预约";
            }
        } else if (needReservation) {
            reservation = needPreOrder ? "需预订" : "需预约";
        }
        if (reservation != null) {
            reservationInfo.add(reservation);
        }

        // 指定类目并且预约信息为不为"是"显示"免预约", 若指定类目预约信息为空时不显示"免预约"
        if ((!needReservation && !LionConfigUtils.forceReserve(categoryId)) &&
                (!isReservationEmpty(attrList, categoryId))) {
            reservationInfo.add("免预约");
        }
        return reservationInfo;
    }

    private static boolean reserveOnline(DealOnlineReserveResult dealOnlineReserveResult) {
        if (dealOnlineReserveResult == null) {
            return false;
        }
        return dealOnlineReserveResult.isCanReserve();
    }

    private static boolean isPreOrderDeal(ProductDetailPageRequest request,ProductCategory categoryDTO) {
        if ( Objects.isNull(request) || Objects.isNull(request.getShepherdGatewayParam())) {
            return false;
        }
        // 当前仅作用于美团APP、点评APP
        // 标识和二级类目决定是否为强预订团单
        return request.getClientTypeEnum().isInApp() &&  com.sankuai.dz.product.detail.RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(request.getPageSource()) && hitPreOrderDealInfo(categoryDTO);
    }

    private static boolean hitPreOrderDealInfo(ProductCategory categoryDTO) {
        String categoryId = Optional.ofNullable(categoryDTO).map(ProductCategory::getProductSecondCategoryId).map(String::valueOf).orElse(StringUtils.EMPTY);
        List<String> preOrderCategoryIds = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.PRE_ORDER_CATEGORY_IDS, String.class, Collections.emptyList());
        if ( org.apache.commons.collections4.CollectionUtils.isEmpty(preOrderCategoryIds)) {
            return false;
        }
        return preOrderCategoryIds.contains(categoryId);
    }


    // 指定类目的预约信息没有值, 则返回true
    private static boolean isReservationEmpty(List<AttrDTO> attrs, int categoryId) {
        // 指定的二级类目
        List<Long> filterCategory = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.RESERVATION_EMPTY_NOT_DISPLAY_TYPE, Long.class, new ArrayList<>());
        // 团单二级类目 不是 指定Lion配置的类目则返回false
        if (categoryId <= 0 || !filterCategory.contains((long)categoryId)) {
            return false;
        }
        // 预约信息为空
        return CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION)) &&
                CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION_2)) &&
                CollectionUtils.isEmpty(DealAttrHelper.getAttributeValues(attrs, DealAttrKeys.RESERVATION_3));
    }
}
