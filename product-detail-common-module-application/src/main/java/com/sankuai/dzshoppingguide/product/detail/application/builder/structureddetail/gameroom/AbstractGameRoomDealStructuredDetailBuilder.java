package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.gameroom;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/28 20:48
 * @Description: 二级类目: 游戏厅
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class
        }
)
abstract class AbstractGameRoomDealStructuredDetailBuilder extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> {
    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        return null;
    }
}
