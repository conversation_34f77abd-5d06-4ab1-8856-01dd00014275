package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 履约保障构造策略
 */
public class PerformanceGuaranteeBuilderStrategyImpl implements GuaranteeBuilderStrategy {
    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.PERFORMANCE_GUARANTEE;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        return null;
    }
}
