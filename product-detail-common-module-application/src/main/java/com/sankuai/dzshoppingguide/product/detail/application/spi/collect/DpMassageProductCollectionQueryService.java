package com.sankuai.dzshoppingguide.product.detail.application.spi.collect;

import com.dinping.mobile.usercenter.api.dto.MyCollectItemDto;
import com.dinping.mobile.usercenter.api.dto.MyFavorItemDto;
import com.dinping.mobile.usercenter.api.request.MyCollectData;
import com.dinping.mobile.usercenter.api.request.MyCollectRequest;
import com.dinping.mobile.usercenter.api.request.MyFavorRequest;
import com.dinping.mobile.usercenter.api.service.MyCollectService;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AtomServiceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.*;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-11
 * @desc 点评足疗/按摩收藏列表查询
 */
@Slf4j
@MdpPigeonServer(url = "com.sankuai.dzshoppingguide.product.detail.application.spi.collect.DpMassageProductCollectionQueryService")
public class DpMassageProductCollectionQueryService implements MyCollectService {

    private static final int MASSAGE_BIZ_TYPE = 100;

    private static final int BATCH_SIZE = 20;

    private static final String DP_RESERVE_KEY = "dp_reserve";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public List<MyCollectItemDto> getCollectList(MyCollectRequest myCollectRequest) {
        // 无需实现
        return Collections.emptyList();
    }

    @Override
    public List<MyFavorItemDto> getFavorList(MyFavorRequest myFavorRequest) {
        if (Objects.isNull(myFavorRequest) || CollectionUtils.isEmpty(myFavorRequest.getDatas())) {
            return null;
        }
        // 过滤出数据中的按摩业务类型的商品ID数据
        List<Long> productIdLs = myFavorRequest.getDatas().stream()
                .filter(Objects::nonNull)
                .filter(data -> Objects.equals(data.getBizType(), MASSAGE_BIZ_TYPE))
                .findFirst()
                .map(MyCollectData::getBizIds)
                .orElse(Collections.emptyList())
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIdLs)) {
            return null;
        }
        // 查询团单对应门店
        List<DealGroupDTO> dealGroups = compositeAtomService.getProductBaseInfo(productIdLs, false);
        if (CollectionUtils.isEmpty(dealGroups)) {
            return null;
        }
        Map<Long, Long> productId2ShopIdMap = AtomServiceUtils.getProductId2ShopIdMap(dealGroups, false);
        if (MapUtils.isEmpty(productId2ShopIdMap)) {
            return null;
        }
        // 提取出有对应门店的团单ID
        List<Integer> productIds = productId2ShopIdMap.keySet().stream().filter(Objects::nonNull).map(Long::intValue).collect(Collectors.toList());
        List<List<Integer>> allProductIds = buildAllProductIds(productIds);
        if (CollectionUtils.isEmpty(allProductIds)) {
            return null;
        }
        List<CompletableFuture<ReserveQueryResponse>> massageReserveQueryResponse = buildProductResultCfList(allProductIds, myFavorRequest, productId2ShopIdMap);
        List<ReserveProductDTO> reserveProducts = extractAllProductResult(massageReserveQueryResponse);
        if (CollectionUtils.isEmpty(reserveProducts)) {
            return null;
        }
        Map<Integer, ReserveProductDTO> productId2GeneralProduct = reserveProducts.stream().collect(Collectors.toMap(ReserveProductDTO::getProductId, e -> e, (a, b) -> a));
        List<MyFavorItemDto> result = Lists.newArrayList();
        for (Integer productId : productIds) {
            ReserveProductDTO reserveProductDTO = productId2GeneralProduct.get(productId);
            if (Objects.isNull(reserveProductDTO)) {
                continue;
            }
            result.add(buildMyFavorItemDto(reserveProductDTO, myFavorRequest.getVersion()));
        }
        return result;
    }

    private MyFavorItemDto buildMyFavorItemDto(ReserveProductDTO reserveProductDTO, String appVersion) {
        if (Objects.isNull(reserveProductDTO)) {
            return null;
        }
        MyFavorItemDto myFavorItemDto = new MyFavorItemDto();
        myFavorItemDto.setBizType(MASSAGE_BIZ_TYPE);
        myFavorItemDto.setBizId(String.valueOf(reserveProductDTO.getProductId()));
        myFavorItemDto.setTitle(reserveProductDTO.getName());
        
        myFavorItemDto.setImage(reserveProductDTO.getHeadPic());
        List<String> shopIds = buildPoiIds(reserveProductDTO.getShops());
        myFavorItemDto.setPoiIds(shopIds);
        // 商品详情页跳链
        String shopId = CollectionUtils.isEmpty(shopIds) ? StringUtils.EMPTY : shopIds.get(0);
        String productDetailUrl = UrlHelper.getCollectedProductDetailUrl(DP_RESERVE_KEY, String.valueOf(reserveProductDTO.getProductId()), shopId, appVersion);
        myFavorItemDto.setUrl(productDetailUrl);
        List<String> subTitles = Lists.newArrayList();
        subTitles.add("免等位、提前退");
        List<String> tags = Lists.newArrayList();
        // 售卖价
        String salePrice = buildSalePrice(reserveProductDTO.getReservePrice());
        if (Objects.nonNull(salePrice)) {
            tags.add("¥" + salePrice);
        }
        // 销量
        String saleTag = buildSaleCount(reserveProductDTO.getSale());
        if (StringUtils.isNotBlank(saleTag)) {
            tags.add(saleTag);
        }
        String tag = String.join(" | ", tags);
        subTitles.add(tag);
        myFavorItemDto.setSubTitles(subTitles);
        return myFavorItemDto;
    }

    private List<String> buildPoiIds(List<ReserveProductShopDTO> shops) {
        if (CollectionUtils.isEmpty(shops)) {
            return null;
        }
        return shops.stream()
                .map(ReserveProductShopDTO::getShopIdAsLong)
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    private List<List<Integer>> buildAllProductIds(List<Integer> allProducts) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(allProducts)) {
            return Lists.newArrayList();
        }
        return Lists.partition(allProducts, BATCH_SIZE);
    }

    private List<CompletableFuture<ReserveQueryResponse>> buildProductResultCfList(List<List<Integer>> productIdsList, MyFavorRequest myFavorRequest, Map<Long, Long> productIdL2ShopIdMap) {
        List<CompletableFuture<ReserveQueryResponse>> cfList = Lists.newArrayList();
        for (List<Integer> productIds : productIdsList) {
            ReserveQueryRequest reserveQueryRequest = AtomServiceUtils.buildReserveQueryRequest(productIds, 0.0D, 0.0D, myFavorRequest.getUserId(), productIdL2ShopIdMap, false);
            CompletableFuture<ReserveQueryResponse> resultCf = compositeAtomService.queryReserveProduct(reserveQueryRequest);
            cfList.add(resultCf);
        }
        return cfList;
    }

    private List<ReserveProductDTO> extractAllProductResult(List<CompletableFuture<ReserveQueryResponse>> generalProductResultCfList) {
        List<ReserveProductDTO> reserveProductList = Lists.newArrayList();
        for (CompletableFuture<ReserveQueryResponse> resultCf : generalProductResultCfList) {
            ReserveQueryResponse result;
            try {
                result = resultCf.get(5000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                continue;
            }
            if (result == null || CollectionUtils.isEmpty(result.getProducts())) {
                continue;
            }
            reserveProductList.addAll(result.getProducts());
        }
        return reserveProductList;
    }

    private String buildSalePrice(ReservePriceDTO reservePrice) {
        return Optional.ofNullable(reservePrice)
                .filter(r -> Objects.nonNull(r.getSalePrice()))
                .map(dto -> dto.getSalePrice().doubleValue() + dto.getSalePriceDesc())
                .orElse(null);
    }

    private String buildSaleCount(SaleDTO sale) {
        return Optional.ofNullable(sale)
                .map(SaleDTO::getSaleTag)
                .orElse(null);
    }

}
