package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.model.DealIdBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DefaultSelectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DefaultSelectDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/10
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class},
        timeout = 500
)
@Slf4j
public class SkuDefaultSelectFetcher extends NormalFetcherContext<SkuDefaultSelect> {

    @Resource
    public QueryCenterAclService queryCenterAclService;

    @Override
    protected CompletableFuture<SkuDefaultSelect> doFetch() {
        long selectedSkuIdFromRequest = request.getSkuId();
        // 如果入参携带skuId，则直接返回
        if (selectedSkuIdFromRequest > 0) {
            return CompletableFuture.completedFuture(new SkuDefaultSelect(selectedSkuIdFromRequest));
        }

        // 否则走查询中心，获取默认skuId
        try {
            QueryByDealGroupIdRequestBuilder baseRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(this.request);
            //查询关联的所有skuId，兜底使用
            baseRequestBuilder.dealId(DealIdBuilder.builder().id().bizDealId());
            // 团购商品类型才查默认skuid，预订的skuid无数据
            if (request.getProductTypeEnum() == ProductTypeEnum.DEAL) {
                //默认skuId
                baseRequestBuilder.defaultSelect(DefaultSelectBuilder.builder().dealId());
            }
            CompletableFuture<QueryDealGroupListResponse> thriftFuture = queryCenterAclService.query(baseRequestBuilder.build());
            return thriftFuture.thenApply(response -> {
                if (response == null) {
                    throw new QueryCenterException("查询中心response为null,团单ID=" + request.getProductId());
                }
                DealGroupDTO dealGroupDTO = Optional.of(response)
                        .map(QueryDealGroupListResponse::getData)
                        .map(QueryDealGroupListResult::getList)
                        .orElse(new ArrayList<>()).stream()
                        .findFirst().orElse(null);
                long selectedSkuId = getDefaultSkuId(request.getProductTypeEnum(), dealGroupDTO);
                return new SkuDefaultSelect(selectedSkuId);
            }).exceptionally(throwable -> {
                log.error("查询中心调用失败!!!未查到默认skuId,团单ID={}", request.getProductId(),throwable);
                return new SkuDefaultSelect(0);
            });
        } catch (TException e) {
            log.error("查询中心调用失败!!!未查到默认skuId,团单ID={}", request.getProductId(), e);
            return CompletableFuture.completedFuture(new SkuDefaultSelect(0));
        }
    }

    private long getDefaultSkuId(final ProductTypeEnum productType,
                                 final DealGroupDTO dealGroupDTO) {
        try {
            if (productType == null) {
                throw new ProductDetailFatalError("productType为null");
            }
            if (dealGroupDTO == null) {
                throw new ProductDetailFatalError("查询中心返回值为null!!!");
            }
            List<DealGroupDealDTO> dealGroupDealDTOS = getValidDealGroupDealDTOS(dealGroupDTO);
            long defaultSkuId = 0;
            if (CollectionUtils.isNotEmpty(dealGroupDealDTOS)) {
                defaultSkuId = SkuIdUtils.getSkuId(productType, dealGroupDealDTOS.get(0));
            }
            defaultSkuId = Optional.of(dealGroupDTO)
                    .map(DealGroupDTO::getDefaultSelectDTO)
                    .map(DefaultSelectDTO::getDealId)
                    .orElse(defaultSkuId);
            if (defaultSkuId <= 0) {
                throw new ProductDetailFatalError("获取不到默认SkuId");
            }
            return defaultSkuId;
        } catch (Throwable throwable) {
            log.error("DealSkuUtils.getDefaultSkuId,dealGroupDTO:{}", JSON.toJSONString(dealGroupDTO), throwable);
            return 0L;
        }
    }

    /**
     * 获取有效skuId信息列表，作为查询中心未返回defaultSkuId的兜底方案
     */
    private List<DealGroupDealDTO> getValidDealGroupDealDTOS(DealGroupDTO dealGroupDTO) {
        if (CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
            return null;
        }

        // 优先获取在线&有销售属性的skuId
        List<DealGroupDealDTO> dealGroupDealDTOS = dealGroupDTO.getDeals().stream()
                // 过滤掉没有销售属性的skuId
                .filter(d -> CollectionUtils.isNotEmpty(d.getAttrs()))
                // 过滤掉无效的skuId
                .filter(d -> d.getBasic().getStatus() == 1)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dealGroupDealDTOS)) {
            return dealGroupDealDTOS;
        }

        // 兜底中的兜底
        return dealGroupDTO.getDeals();
    }

}