package com.sankuai.dzshoppingguide.product.detail.application.builder.review;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.DpReserveShopReview;

/**
 * @Author: litengfei04
 * @Date: 2025/2/5 17:45
 */
@Builder(
        moduleKey = ModuleKeyConstants.DP_RESERVE_SHOP_REVIEW_MODULE,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class}
)
public class DpReserveReviewModuleBuilder extends BaseBuilder<DpReserveShopReview> {

    @Override
    public DpReserveShopReview doBuild() {
        // 美团侧直接返回
        if (this.request.getClientTypeEnum().isMtClientType()) {
            return null;
        }
        if (this.request.getPoiId() <= 0) {
            return null;
        }
        DpReserveShopReview dpReserveShopReview = new DpReserveShopReview();
        dpReserveShopReview.setModuleName("用户评价");
        dpReserveShopReview.setContent("店铺点评");
        dpReserveShopReview.setMoreText("查看全部点评");
        dpReserveShopReview.setMoreUrl(String.format("dianping://review?referid=%d", this.request.getPoiId()));
        return dpReserveShopReview;
    }
}
