package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule;

import com.dianping.technician.service.massage.MassageTechCustomizeService;
import com.dianping.technician.service.massage.query.MassageShopSwitchTechScheduleQuery;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 17:35
 */
@Fetcher(
        previousLayerDependencies = {ShopIdMapperFetcher.class}
)
@Slf4j
public class TechStockWhiteShopFetcher extends NormalFetcherContext<TechStockWhiteShopResult> {
    @RpcClient(url = "com.dianping.technician.service.massage.MassageTechCustomizeService")
    private MassageTechCustomizeService massageTechCustomizeService;

    @Override
    protected CompletableFuture<TechStockWhiteShopResult> doFetch() {
        try {
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            Long dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
            MassageShopSwitchTechScheduleQuery query = buildMassageShopSwitchTechScheduleQuery(dpShopId);

            return AthenaInf.getRpcCompletableFuture(massageTechCustomizeService.queryMassageShopSwitchTechSchedule(query)).exceptionally(e -> {
                log.error(XMDLogFormat.build().putTag("scene", "TechStockWhiteShopFetcher")
                        .putTag("method", "TechStockWhiteShopFetcher").message(String
                                .format("TechStockWhiteShopFetcher doFetch error, request : %s", JsonCodec.encodeWithUTF8(request))));
                return null;
            }).thenApply(result -> {
                TechStockWhiteShopResult techStockWhiteShopResult = new TechStockWhiteShopResult(false);
                if (org.apache.commons.collections4.MapUtils.isEmpty(result)) {
                    return techStockWhiteShopResult;
                }
                if (result.get(dpShopId) == null) {
                    return techStockWhiteShopResult;
                }
                techStockWhiteShopResult.setWhiteList(result.get(dpShopId).equals(Boolean.TRUE));
                return techStockWhiteShopResult;
            });
        } catch (Exception e) {
            log.error("TechStockWhiteShopFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);

    }

    private MassageShopSwitchTechScheduleQuery buildMassageShopSwitchTechScheduleQuery(long dpShopId) {
        return MassageShopSwitchTechScheduleQuery.builder().dpShopIds(Lists.newArrayList(dpShopId)).build();
    }
}
