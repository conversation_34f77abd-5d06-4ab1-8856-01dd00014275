package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.gameroom;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/28 20:53
 * @Description: 游戏厅 项目体验 155006
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                ProductCategoryFetcher.class
        }
)
public class GameRoomProjectExperienceDealStructuredDetailBuilder extends AbstractGameRoomDealStructuredDetailBuilder {
}
