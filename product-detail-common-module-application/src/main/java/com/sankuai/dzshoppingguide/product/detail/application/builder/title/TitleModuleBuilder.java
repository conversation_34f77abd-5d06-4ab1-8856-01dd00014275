package com.sankuai.dzshoppingguide.product.detail.application.builder.title;

import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.title.vo.TitleModuleVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:45
 */
@Builder(
        moduleKey = ModuleKeyConstants.TITLE,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class, ProductCategoryFetcher.class, SkuAttrFetcher.class, ProductAttrFetcher.class, SkuDefaultSelectFetcher.class}
)
public class TitleModuleBuilder extends BaseBuilder<TitleModuleVO> {

    private static final String PRICING_METHOD = "PricingMethod";
    private static final String SEGMENT_TYPE = "segmentType";
    private static final String GEAR_START_TIME = "gearStartTime";
    private static final String GEAR_END_TIME = "gearEndTime";
    private static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";
    private static final String WEEKEND_TIME = "weekendTime";

    @Override
    public TitleModuleVO doBuild() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        // 默认标题
        String basicTitle = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getBasic)
                .map(DealGroupBasicDTO::getTitle)
                .orElse(StringUtils.EMPTY);

        if (StringUtils.isEmpty(basicTitle)) {
            return null;
        }

        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);

        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        long skuId = Optional.ofNullable(skuDefaultSelect).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        // 按摩足疗 货架点击可用时段的品进入
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        basicTitle = processGearDealTitle(skuId,skuAttr,productAttr,basicTitle);

        // 商场内餐厅代金券
        Integer categoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        if ( Objects.equals(request.getProductTypeEnum(),ProductTypeEnum.DEAL) && isFromMallFoodPoiVoucher(request.getPageSource(),categoryId) && StringUtils.isNotEmpty(basicTitle)) {
            basicTitle = processMallFoodTitle(basicTitle);
        }

        TitleModuleVO titleModuleVO = new TitleModuleVO();
        titleModuleVO.setTitle(basicTitle);
        return titleModuleVO;
    }

    private String processMallFoodTitle(String basicTitle) {
        basicTitle = String.format("%s代金券", basicTitle);
        return basicTitle;
    }


    private String processGearDealTitle(long skuId,SkuAttr skuAttr, ProductAttr productAttr,String basicTitle) {
        if (Objects.isNull(skuAttr) || Objects.isNull(productAttr)) {
            return basicTitle;
        }
        // 区分闲忙时
        // String priceMethod = productAttr.getSkuAttrFirstValue(PRICING_METHOD);
        // segmentType 1:白天/夜间档 2:工作日/周末
        String segmentType = productAttr.getSkuAttrFirstValue(SEGMENT_TYPE);

        // 预约时间·日期+时间段+可用·有效时间
        if ( StringUtils.equals("1", segmentType)) {
            // 1:白天/夜间档
            return processDayOrNightTitle(skuId,skuAttr,basicTitle);
        } else if ( StringUtils.equals("2", segmentType)) {
            // 2:工作日/周末
            return processWeekDayTitle(skuId,skuAttr,basicTitle);
        } else {
            // 兜底场景
            return basicTitle;
        }
    }

    private String processWeekDayTitle(long skuId,SkuAttr skuAttr,String basicTitle) {
        // String grade = skuAttr.getSkuAttrFirstValue(skuId, SELL_DIFFERENT_GRADES);
        String weekDay = skuAttr.getSkuAttrFirstValue(skuId, WEEKEND_TIME);
        if (StringUtils.isEmpty(weekDay)) {
            return basicTitle;
        }
        String weekDayDoc = StringUtils.EMPTY;
        switch (weekDay) {
            case "[1,2,3,4]":
                weekDayDoc = "周一至周四";
                break;
            case "[1,2,3,4,5]":
                weekDayDoc = "周一至周五";
                break;
            case "[5,6,7]":
                weekDayDoc = "周五至周日";
                break;
            case "[6,7]":
                weekDayDoc = "周六周日";
                break;
        }
        return String.format("%s | %s可用", basicTitle,weekDayDoc);
    }

    private String processDayOrNightTitle(long skuId,SkuAttr skuAttr,String basicTitle) {
        String startTime = skuAttr.getSkuAttrFirstValue(skuId, GEAR_START_TIME);
        String endTime = AvailableTimeHelper.getTimeOfDayDoc(skuAttr.getSkuAttrFirstValue(skuId, GEAR_END_TIME));
        // String grade = skuAttr.getSkuAttrFirstValue(skuId, SELL_DIFFERENT_GRADES);
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return basicTitle;
        }
        return String.format("%s | %s-%s可用", basicTitle,startTime,endTime);
    }

    private static final int MALL_DEAL_CATEGORY_ID = 712;//商场团单类目

    private static final String MALL_FOOD_POI_SHELF_PAGE_SOURCE = "mallFoodPoiShelf";//商场内餐厅代金券

    /**
     * 判断是否来自商场场内餐厅代金券
     */
    public boolean isFromMallFoodPoiVoucher(String requestPageSource, Integer dealCategoryId) {
        if (StringUtils.isEmpty(requestPageSource) || Objects.isNull(dealCategoryId)) {
            return false;
        }
        if (!StringUtils.equals(requestPageSource, MALL_FOOD_POI_SHELF_PAGE_SOURCE)) {
            return false;
        }
        return isMallDealCategory(dealCategoryId);
    }

    public boolean isMallDealCategory(Integer dealCategoryId) {
        return Objects.nonNull(dealCategoryId) && MALL_DEAL_CATEGORY_ID == dealCategoryId;
    }
}
