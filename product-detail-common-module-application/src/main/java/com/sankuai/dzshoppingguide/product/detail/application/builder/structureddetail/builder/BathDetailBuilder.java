package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.factory.StyleFactory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/23 10:24
 * @Description: 洗浴行业主构建器
 */
@Builder(
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS, builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class BathDetailBuilder extends AbstractStructuredDetailBuilder {

    @Resource
    private StyleFactory styleFactory;

    @Override
    protected List<Style> getStyles() {
        return Lists.newArrayList(
                styleFactory.createBathStyle(),
                styleFactory.createAdditionalInfoStyle0(),
                styleFactory.createServiceFacilityStyle0()
        );
    }
}
