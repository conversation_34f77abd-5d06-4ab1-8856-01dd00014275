package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/13 11:21
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
@Slf4j
public class CityIdMapperFetcher extends NormalFetcherContext<CityIdMapper> {
    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    @Override
    protected CompletableFuture<CityIdMapper> doFetch() {
        int cityId = request.getCityId();
        boolean mt = request.getClientTypeEnum().isMtClientType();
        int mtCityId;
        int dpCityId;
        if (mt) {
            mtCityId = cityId;
            dpCityId = mapperCacheWrapper.fetchDpCityId(cityId);
        } else {
            dpCityId = cityId;
            mtCityId = mapperCacheWrapper.fetchMtCityId(cityId);
        }
        return CompletableFuture.completedFuture(new CityIdMapper(mtCityId, dpCityId));
    }
}
