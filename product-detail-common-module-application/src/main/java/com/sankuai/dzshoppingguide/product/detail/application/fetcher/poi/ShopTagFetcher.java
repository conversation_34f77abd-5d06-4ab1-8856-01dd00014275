package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 门店标签
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
public class ShopTagFetcher extends NormalFetcherContext<ShopTagReturnValue> {

    private static final String MT_BIZ_CODE = "MT_DAOZONG_DEAL_DETAIL";
    private static final String DP_BIZ_CODE = "DP_DAOZONG_DEAL_DETAIL";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ShopTagReturnValue> doFetch() {
        FindSceneDisplayTagRequest findSceneDisplayTagRequest = buildRequest(request);
        return compositeAtomService.queryShopTags(findSceneDisplayTagRequest).thenApply(result -> {
            if (MapUtils.isEmpty(result)) {
                return null;
            }
            return new ShopTagReturnValue(result.get(request.getPoiId()));
        });
    }

    private FindSceneDisplayTagRequest buildRequest(ProductDetailPageRequest pageRequest) {
        FindSceneDisplayTagRequest request = new FindSceneDisplayTagRequest();
        if (pageRequest.getClientTypeEnum().isMtClientType()) {
            request.setBizCode(MT_BIZ_CODE);
        } else {
            request.setBizCode(DP_BIZ_CODE);
        }
        request.setShopIds(Collections.singletonList(pageRequest.getPoiId()));
        request.setNeedStyle(false);
        return request;
    }
}
