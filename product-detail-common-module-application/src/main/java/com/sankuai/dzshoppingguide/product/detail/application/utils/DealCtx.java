package com.sankuai.dzshoppingguide.product.detail.application.utils;


import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.Data;


/**
 * 查询团单信息的环境变量
 */
@Data
public class DealCtx {
    // 上单客户ID
    private long customerId;
    //后台类目Id
    private int categoryId;
    // 是否是ap
    private Boolean isApp;
    /**
     * 渠道来源
     */
    private String requestSource;


    /**
     * 网关请求参数
     */
    ShepherdGatewayParam requestParam;

    /**
     * 团购商品信息
     */
    private DealGroupDTO dealGroupDTO;

    /**
     * 结构化退款购买须知表格控制开关
     */
    private boolean hasPurchaseNoteTable;

    /**
     * 是否是小程序
     */
    private boolean isMiniProgram;

    /**
     * 是否是团购次卡
     */
    private boolean isDealTimesCard;

}