package com.sankuai.dzshoppingguide.product.detail.application.builder.recommend;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.ModuleDetailRecommendVO;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/5 16:54
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_RECOMMEND,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class
        }
)
public class ModuleDetailRecommendBuilder extends BaseBuilder<ModuleDetailRecommendVO> {
    @Override
    public ModuleDetailRecommendVO doBuild() {
        return null;
    }
}
