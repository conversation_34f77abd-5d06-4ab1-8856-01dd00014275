// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.guarantee;
//
// import com.google.common.collect.Maps;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
// import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
// import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
// import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
// import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
// import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
// import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
// import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
// import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
// import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
// import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
// import com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum;
// import com.sankuai.nib.price.operation.common.guarantee.enums.QueryExtKeyEnum;
// import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
// import com.sankuai.nib.sp.common.enums.Owner;
// import com.sankuai.nib.sp.common.enums.TradeType;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.commons.collections.CollectionUtils;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.util.HashMap;
// import java.util.HashSet;
// import java.util.Map;
// import java.util.Set;
// import java.util.concurrent.CompletableFuture;
//
// import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.*;
// import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.PRODUCT;
// import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/3/11 08:45
//  */
// @Fetcher(
//         timeout = 50000L,
//         previousLayerDependencies = {ShopIdMapperFetcher.class, DealGroupIdMapperFetcher.class, ProductCategoryFetcher.class}
// )
// @Slf4j
// public class GuaranteeTagFetcher extends NormalFetcherContext<GuaranteeTagResult> {
//
//     @Autowired
//     private CompositeAtomService compositeAtomService;
//
//     private long mtDealGroupId;
//     private long dpDealGroupId;
//     private long mtShopId;
//     private long dpShopId;
//     private int categoryId;
//
//     @Override
//     protected CompletableFuture<GuaranteeTagResult> doFetch() {
//         DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
//         ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
//         ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
//         if  (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
//             return CompletableFuture.completedFuture(null);
//         }
//         mtDealGroupId = dealGroupIdMapper.getMtDealGroupId();
//         dpDealGroupId = dealGroupIdMapper.getDpDealGroupId();
//         mtShopId = shopIdMapper.getMtBestShopId();
//         dpShopId = shopIdMapper.getDpBestShopId();
//         categoryId = productCategory.getProductSecondCategoryId();
//
//         return compositeAtomService.batchQueryGuaranteeTag(buildSessionContext(),buildRequest()).thenApply(res -> {
//             if ( CollectionUtils.isEmpty(res)) {
//                 return null;
//             }
//             return new GuaranteeTagResult(res);
//         });
//     }
//
//     private SessionContextDTO buildSessionContext(){
//         SessionContextDTO sessionContext = new SessionContextDTO();
//         sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
//         // 判断是否是教育行业
//         if (LionConfigUtils.isAnXinXueCategoryIds(categoryId)){
//             sessionContext.setTradeType(TradeType.COUNT_CARD.getCode());
//         }else {
//             sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
//         }
//         return sessionContext;
//     }
//
//     private BatchQueryGuaranteeTagRequest buildRequest() {
//         BatchQueryGuaranteeTagRequest guaranteeTagRequest = new BatchQueryGuaranteeTagRequest();
//         guaranteeTagRequest.setObjects(getObjects());
//         guaranteeTagRequest.setGuaranteeTypes(getGuaranteeTypes());
//         guaranteeTagRequest.setUserInfo(getUserInfoDTO());
//         guaranteeTagRequest.setQueryTagOption(getQueryTagOption());
//         return guaranteeTagRequest;
//     }
//
//     public Set<GuaranteeObjectQueryDTO> getObjects() {
//         if (mtDealGroupId <= 0 || dpDealGroupId <= 0) {
//             return new HashSet<>();
//         }
//         Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
//         GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();
//         object.setObjectId(String.valueOf(request.getClientTypeEnum().isMtClientType() ? mtDealGroupId : dpDealGroupId));
//         object.setObjectType(PRODUCT.getCode());
//         objects.add(object);
//         object.setExt(getExt());
//         return objects;
//     }
//
//     private Map<String, String> getExt() {
//         //齿科
//         if (categoryId == 506){
//             Map<String, String> ext = new HashMap<>();
//             ext.put(QueryExtKeyEnum.POI_ID.getCode(), String.valueOf(mtShopId));
//             return ext;
//         }
//         return Maps.newHashMap();
//     }
//
//     private Set<Integer> getGuaranteeTypes() {
//         Set<Integer> guaranteeTypes = new HashSet<>();
//         guaranteeTypes.add(PRICE_PROTECTION.getCode());
//         guaranteeTypes.add(BEST_PRICE_GUARANTEE.getCode());
//         // 安心医、放心种植用的都是 ANXIN_MEDICAL_GUARANTEE
//         guaranteeTypes.add(ANXIN_MEDICAL_GUARANTEE.getCode());
//         guaranteeTypes.add(ANXIN_LEARNING_GUARANTEE.getCode());
//         return guaranteeTypes;
//     }
//
//     public UserInfoDTO getUserInfoDTO() {
//         UserInfoDTO userInfoDTO = new UserInfoDTO();
//         ChannelDTO channelDTO = new ChannelDTO();
//
//         PlatformEnum platformEnum;
//         ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
//         if ( clientTypeEnum.isMtClientType()) {
//             platformEnum = PlatformEnum.MT_PLATFORM;
//         } else {
//             platformEnum = PlatformEnum.DP_PLATFORM;
//         }
//         channelDTO.setPlatform(platformEnum.getCode());
//
//         TerminalTypeEnum terminalTypeEnum;
//         if (clientTypeEnum.isInApp()) {
//             terminalTypeEnum = TerminalTypeEnum.APP;
//         } else if (clientTypeEnum.isInWxXCX()) {
//             terminalTypeEnum = TerminalTypeEnum.APPLETS;
//         } else if (clientTypeEnum == ClientTypeEnum.DP_PC || clientTypeEnum == ClientTypeEnum.MT_PC) {
//             terminalTypeEnum = TerminalTypeEnum.PC;
//         } else {
//             terminalTypeEnum = TerminalTypeEnum.MOBILE;
//         }
//         channelDTO.setTerminalType(terminalTypeEnum.getCode());
//
//         ChannelNoEnum channelNoEnum;
//
//         if (com.sankuai.dz.product.detail.RequestSourceEnum.fromLive(request.getPageSource())) {
//             channelNoEnum = ChannelNoEnum.LIVE_STREAMING;
//         } else {
//             channelNoEnum = ChannelNoEnum.UNKNOWN;
//         }
//         channelDTO.setChannelNo(channelNoEnum.getCode());
//
//         userInfoDTO.setChannel(channelDTO);
//         return userInfoDTO;
//     }
//
//     public QueryTagOptionDTO getQueryTagOption(){
//         QueryTagOptionDTO queryTagOptionDTO=new QueryTagOptionDTO();
//         queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
//         return queryTagOptionDTO;
//     }
// }
