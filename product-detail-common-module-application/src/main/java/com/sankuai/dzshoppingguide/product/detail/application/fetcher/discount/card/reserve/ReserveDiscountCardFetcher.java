package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve;

import com.dianping.gm.marketing.member.card.api.dto.request.LoadShopDiscountCardRequest;
import com.dianping.gm.marketing.member.card.api.service.DiscountCardShelfService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ParamsUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PlatformUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:18
 */
@Fetcher(
        previousLayerDependencies = {ShopIdMapperFetcher.class, CityIdMapperFetcher.class}
)
@Slf4j
public class ReserveDiscountCardFetcher extends NormalFetcherContext<ReserveDiscountCard> {

    @RpcClient(url = "http://service.dianping.com/gmMemberCardService/DiscountCardShelfService_1.0.0")
    private DiscountCardShelfService discountCardShelfService;

    @Override
    protected CompletableFuture<ReserveDiscountCard> doFetch() {
        try {
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
            if(request == null || request.getShepherdGatewayParam() == null || shopIdMapper == null || cityIdMapper == null){
                return CompletableFuture.completedFuture(null);
            }
            LoadShopDiscountCardRequest discountCardRequest = buildLoadShopDiscountCardRequest(request, shopIdMapper,cityIdMapper);
            if (discountCardRequest == null) {
                return CompletableFuture.completedFuture(null);
            }
            return AthenaInf.getRpcCompletableFuture(discountCardShelfService.loadDiscountCardByShop(discountCardRequest)).exceptionally(e -> {
                log.error(XMDLogFormat.build()
                        .putTag("scene", "compositeAtomService")
                        .putTag("method", "loadShopDiscountCard")
                        .message(String.format("loadShopDiscountCard error, shopIdLong : %s", JsonCodec.encodeWithUTF8(request))));
                return null;
            }).thenApply(res -> {
                if (res == null) {
                    return null;
                }
                return new ReserveDiscountCard(res);
            });
        } catch (Exception e) {
            log.error("MassageShopConfigFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private LoadShopDiscountCardRequest buildLoadShopDiscountCardRequest(ProductDetailPageRequest request,ShopIdMapper shopIdMapper,CityIdMapper cityIdMapper) {
        LoadShopDiscountCardRequest cardRequest = new LoadShopDiscountCardRequest();
        if (request == null || request.getShepherdGatewayParam() == null) {
            return null;
        }
        ShepherdGatewayParam params = request.getShepherdGatewayParam();
        cardRequest.setPlatform(PlatformUtil.getPlatform(request.getClientTypeEnum()));
        cardRequest.setNewVersion(true);
        cardRequest.setDpId(Optional.of(params).map(ShepherdGatewayParam::getDeviceId).orElse(""));
        cardRequest.setClientType(ParamsUtils.getClientType(request.getClientTypeEnum(), request.getMobileOSType()));
        cardRequest.setClientVersion(params.getAppVersion());
        cardRequest.setLongDpShopId(shopIdMapper.getDpBestShopId());
        if (request.getClientTypeEnum().isMtClientType()) {
            cardRequest.setUserId(params.getMtUserId());
            cardRequest.setRequestLongShopId(shopIdMapper.getMtBestShopId());
            cardRequest.setCityId(cityIdMapper.getMtCityId());
        } else {
            cardRequest.setUserId(params.getDpUserId());
            cardRequest.setRequestLongShopId(shopIdMapper.getDpBestShopId());
            cardRequest.setCityId(cityIdMapper.getDpCityId());
        }
        return cardRequest;
    }

}
