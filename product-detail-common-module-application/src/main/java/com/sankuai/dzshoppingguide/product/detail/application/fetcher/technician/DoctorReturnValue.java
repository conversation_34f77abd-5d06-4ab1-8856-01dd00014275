package com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.technician.query.center.result.TechItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorReturnValue extends FetcherReturnValueDTO {

    private List<TechItem> techItems;

}
