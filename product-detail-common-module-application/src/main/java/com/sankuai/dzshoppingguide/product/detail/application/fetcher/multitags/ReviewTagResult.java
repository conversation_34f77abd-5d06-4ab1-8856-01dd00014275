package com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags;

import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/6 11:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReviewTagResult extends FetcherReturnValueDTO {
    private ReviewCount mtReviewInfo;
    private ReviewStarDistributionDTO dpReviewInfo;
}
