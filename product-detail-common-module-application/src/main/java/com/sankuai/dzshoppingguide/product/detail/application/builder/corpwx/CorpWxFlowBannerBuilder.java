// package com.sankuai.dzshoppingguide.product.detail.application.builder.corpwx;
//
// import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
// import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
// import com.sankuai.dz.srcm.flow.dto.CorpWxFlowMaterialDTO;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.corpwx.CorpWxFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.corpwx.CorpWxResult;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
// import com.sankuai.dzshoppingguide.product.detail.spi.corpwxflowbanner.vo.CorpWxFlowBannerVO;
// import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
//
// import java.util.Optional;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/2/26 16:50
//  */
// @Builder(
//         moduleKey = ModuleKeyConstants.ADDITIONAL_INFO,
//         startFetcher = CommonModuleStarter.class,
//         dependentFetchers = {CorpWxFetcher.class}
// )
// public class CorpWxFlowBannerBuilder extends BaseBuilder<CorpWxFlowBannerVO> {
//     @Override
//     protected CorpWxFlowBannerVO doBuild() {
//         CorpWxResult corpWxResult = getDependencyResult(CorpWxFetcher.class);
//         return Optional.ofNullable(corpWxResult).map(CorpWxResult::getCorpWxFlowMaterialDTO).map(this::convertToCorpWxFlowBanner).orElse(null);
//     }
//
//     private CorpWxFlowBannerVO convertToCorpWxFlowBanner(CorpWxFlowMaterialDTO dto) {
//         CorpWxFlowBannerVO corpWxFlowBanner = new CorpWxFlowBannerVO();
//         corpWxFlowBanner.setButtonText(dto.getButtonText());
//         corpWxFlowBanner.setJumpUrl(dto.getJumpUrl());
//         corpWxFlowBanner.setShowResource(dto.isShowResource());
//         corpWxFlowBanner.setPicUrl(dto.getPicUrl());
//         corpWxFlowBanner.setSubTitleText(dto.getSubTitleText());
//         corpWxFlowBanner.setShowText(dto.getShowText());
//         return corpWxFlowBanner;
//     }
// }
