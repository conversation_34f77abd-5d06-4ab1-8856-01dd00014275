package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 精油SPA
 *
 * @author: created by hang.yu on 2023/8/17 11:03
 */
@Component("spaStrategyImpl")
public class SpaStrategyImpl extends AbstractMassageStrategy {

    private static final List<String> DISPOSABLE_PRIORITY_LIST = Lists.newArrayList();

    static {
        DISPOSABLE_PRIORITY_LIST.add("一次性床单");
        DISPOSABLE_PRIORITY_LIST.add("一次性短裤");
        DISPOSABLE_PRIORITY_LIST.add("香薰");
        DISPOSABLE_PRIORITY_LIST.add("眼罩");
        DISPOSABLE_PRIORITY_LIST.add("电动按摩床");
        DISPOSABLE_PRIORITY_LIST.add("一次性拖鞋");
        DISPOSABLE_PRIORITY_LIST.add("一次性按摩巾");
        DISPOSABLE_PRIORITY_LIST.add("消毒按摩服");
    }

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 免费精油 > 热敷工具 > 一次性工具+其他工具
        // 免费精油
        String freeEssentialOil = getAttrValue(serviceProjectAttrs, FREE_ESSENTIAL_OIL);
        if (StringUtils.isNotBlank(freeEssentialOil)) {
            return getFreeEssentialOilValue(freeEssentialOil);
        }
        // 热敷工具
        String hotpackTool = getAttrValue(serviceProjectAttrs, HOTPACK_TOOL);
        if (StringUtils.isNotBlank(hotpackTool)) {
            return hotpackTool;
        }
        // 一次性工具+其他工具
        List<String> mixedTools = getMixedTools(serviceProjectAttrs);
        if (CollectionUtils.isEmpty(mixedTools)) {
            return null;
        }
        // 对一次性材料及工具、其他工具进行混排
        mixedTools.sort(Comparator.comparingInt(DISPOSABLE_PRIORITY_LIST::indexOf));
        return mixedTools.get(0);
    }

    private String getFreeEssentialOilValue(String freeEssentialOil) {
        List<String> freeEssentialOils = Arrays.asList(freeEssentialOil.split(SPERATOR));
        if (freeEssentialOils.size() == 1 || freeEssentialOils.size() == TWO) {
            return freeEssentialOil;
        }
        return String.format("%d种精油可选择", freeEssentialOils.size());
    }

}
