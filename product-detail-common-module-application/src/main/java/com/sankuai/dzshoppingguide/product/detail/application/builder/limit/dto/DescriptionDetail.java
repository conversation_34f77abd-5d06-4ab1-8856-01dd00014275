package com.sankuai.dzshoppingguide.product.detail.application.builder.limit.dto;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0xbfb1)
public class DescriptionDetail implements Serializable {
    /**
    * 浮层描述
    */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
    * 浮层标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}