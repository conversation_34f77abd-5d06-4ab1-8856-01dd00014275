package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.limit.dto.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FreeDealEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

public class FreeDealUtils {

    private static final String FREE_DEAL_INFO_CONFIG_LION_KEY = "free.deal.info.config";

    public static FreeDealConfig getFreeDealConfig(ProductBaseInfo baseInfo,ProductCategory productCategory) {
        FreeDealEnum freeDealEnum = getFreeDealEnum(baseInfo, productCategory);
        if (Objects.isNull(freeDealEnum)) {
            return null;
        }
        String config = Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb",  String.format("%s.%s", FREE_DEAL_INFO_CONFIG_LION_KEY, freeDealEnum.getType()));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        return JsonCodec.decode(config, FreeDealConfig.class);
    }
    
    public static boolean isFreeDeal(ProductBaseInfo baseInfo,ProductCategory productCategory) {
        FreeDealEnum freeDealType = getFreeDealEnum(baseInfo, productCategory);
        return freeDealType != null;
    }

    private static FreeDealEnum getFreeDealEnum(ProductBaseInfo baseInfo, ProductCategory productCategory) {
        int categoryId = getSecondCategoryId(productCategory);
        int tradeType = getTradeType(baseInfo);
        if (!LionConfigUtils.inCategoryList((long) categoryId)) {
            return null;
        }
        if (tradeType != TradeTypeEnum.RESERVATION.getCode()) {
            return null;
        }
        return FreeDealEnum.fromDealCategory(String.valueOf(categoryId));
    }

    /**
     * 交易类型
     * @param baseInfo
     * @return
     */
    public static int getTradeType(ProductBaseInfo baseInfo) {
        return Optional.ofNullable(baseInfo).map(ProductBaseInfo::getBasic).map(DealGroupBasicDTO::getTradeType).orElse(0);
    }

    /**
     * 获取团单的二级分类id
     * @param productCategory
     * @return
     */
    public static int getSecondCategoryId(ProductCategory productCategory) {
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
    }

    /**
     * 获取团单的三级分类id
     * @param productCategory
     * @return
     */
    public static int getServiceTypeId(ProductCategory productCategory) {
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
    }
}
