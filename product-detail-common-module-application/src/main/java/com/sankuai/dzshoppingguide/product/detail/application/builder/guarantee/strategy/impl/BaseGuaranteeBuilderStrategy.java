package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.detail.dto.DealGroupIdDTO;
import com.dianping.deal.detail.dto.ThirdPartyDTO;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.FeatureDetailDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.general.product.query.center.client.dto.combine.CombineDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;
import com.sankuai.technician.category.enums.TechCategoryEnum;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 基础保障构造策略
 */
@Slf4j
public class BaseGuaranteeBuilderStrategy {
    private static final long CLEANING_SELF_OPERATION_TAG_ID = 21345L;
    private static final int ORAL_TEETH_CATEGORY = 506;

    protected List<GuaranteeInstructionsContentVO> buildContents(GuaranteeParam param) {
        ProductCategory productCategory = param.getProductCategory();
        ProductGuaranteeTagInfo productGuaranteeTagInfo = param.getProductGuaranteeTagInfo();
        ProductAttr productAttr = param.getProductAttr();
        List<DisplayTagDto> shopDisplayTagList = param.getShopDisplayTagList();
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        // 买贵必赔
        if (checkBestPriceGuaranteeValid(productGuaranteeTagInfo.getBestPriceGuaranteeInfo())) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO("买贵必赔");
            content.setFontColor("#8E3C12");
            Icon icon = new Icon();
            icon.setIcon("https://p1.meituan.net/travelcube/409cdc9bf49ebac30967bf41d62665781754.png");
            content.setPrefixIcon(icon);
            contents.add(content);
        }
        // 价保
        if (LionConfigUtils.showPriceProtectionInfo(productCategory.getProductSecondCategoryId()) && checkPriceProtectionValid(productGuaranteeTagInfo.getPriceProtectionInfo())) {
            contents.add(new GuaranteeInstructionsContentVO("价保" + productGuaranteeTagInfo.getPriceProtectionInfo().getPriceProtectionTag().getValidityDays() + "天"));
        }
        // 履约保障标签
        List<FeatureDetailDTO> performanceGuaranteeTags = getPerformanceGuaranteeTags(productCategory, shopDisplayTagList);
        if (CollectionUtils.isNotEmpty(performanceGuaranteeTags)) {
            performanceGuaranteeTags.forEach(tag -> {
                if (Objects.nonNull(tag)) {
                    GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO(tag.getText());
                    content.setFontColor(tag.getStyle());
                    contents.add(content);
                }
            });
        }
        // 酒吧营销经理和保洁自营门店不展示
        if (!isJoyBarManager(param.getPageRequest()) && !isCleaningSelfOperationShop(shopDisplayTagList)) {
            if (checkRefundByProduct(productCategory, productAttr)) {
                contents.add(new GuaranteeInstructionsContentVO("未预约可退"));
            } else if (checkAutoRefundSwitch(param.getProductBaseInfo())) {
                contents.add(new GuaranteeInstructionsContentVO("随时退"));
            }
            if (checkOverdueAutoRefund(param.getProductBaseInfo())) {
                contents.add(new GuaranteeInstructionsContentVO("过期退"));
            }
        }
        // 适用时间
        String applicableTime = getApplicableTimeDesc(productAttr, productCategory);
        if (StringUtils.isNotBlank(applicableTime)) {
            contents.add(new GuaranteeInstructionsContentVO(applicableTime));
        }
        // 使用人群
        String applicablePeople = getApplicablePeopleDesc(productAttr, productCategory);
        if (StringUtils.isNotBlank(applicablePeople)) {
            contents.add(new GuaranteeInstructionsContentVO(applicablePeople));
        }
        if (!isJoyBarManager(param.getPageRequest())) {
            // 体检中心
            if (physicalExamReserveOnline(param.getPageRequest(), param.getDpDealGroupId2ThirdPartyMap())) {
                contents.add(new GuaranteeInstructionsContentVO("可在线预约"));
            }
            // todo 亲子游乐在线预约
        }
        // todo 快照类目是否设置在线预约
        // 可加项
        if (isForeSupportAdditional(param.getProductCategory()) && isAdditional(param.getCombines())) {
            contents.add(new GuaranteeInstructionsContentVO("可加项"));
        }
        return contents;
    }


    public List<FeatureDetailDTO> getPerformanceGuaranteeTags(ProductCategory productCategory, List<DisplayTagDto> shopDisplayTagList) {
        if (CollectionUtils.isEmpty(shopDisplayTagList) || Objects.isNull(productCategory)) {
            return Lists.newArrayList();
        }
        List<FeatureDetailDTO> featureDetailDTOS = LionConfigUtils.getPerformanceGuaranteeFeatureList(productCategory);
        if (CollectionUtils.isEmpty(featureDetailDTOS)) {
            return Lists.newArrayList();
        }
        Set<Long> dpShopTagsSet = shopDisplayTagList.stream()
                .map(DisplayTagDto::getTagId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return featureDetailDTOS.stream()
                .filter(Objects::nonNull)
                .filter(tag -> dpShopTagsSet.contains(tag.getId()))
                .collect(Collectors.toList());
    }

    public boolean checkPriceProtectionValid(ObjectGuaranteeTagDTO objectGuaranteeTagDTO) {
        if (objectGuaranteeTagDTO == null) {
            return false;
        }
        PriceProtectionTagDTO priceProtectionTagDTO = objectGuaranteeTagDTO.getPriceProtectionTag();
        return priceProtectionTagDTO != null
                && priceProtectionTagDTO.getValid() != null
                && priceProtectionTagDTO.getValid()
                && priceProtectionTagDTO.getValidityDays() != null;
    }

    public boolean checkBestPriceGuaranteeValid(ObjectGuaranteeTagDTO bestPriceGuaranteeTag) {
        return Objects.nonNull(bestPriceGuaranteeTag) && Objects.nonNull(bestPriceGuaranteeTag.getBestPriceGuaranteeTagDTO())
                && Objects.nonNull(bestPriceGuaranteeTag.getBestPriceGuaranteeTagDTO().getValid())
                && bestPriceGuaranteeTag.getBestPriceGuaranteeTagDTO().getValid();
    }

    public boolean isForeSupportAdditional(ProductCategory productCategory) {
        return LionConfigUtils.supportAdditionalCategory().contains(productCategory.getProductSecondCategoryId());
    }

    private boolean isAdditional(List<CombineDTO> combines) {
        if (CollectionUtils.isEmpty(combines)) {
            return false;
        }
        return combines.stream()
                .map(CombineDTO::getCombineItems).anyMatch(Objects::nonNull);
    }

    /**
     * 体检中心预约
     */
    public boolean physicalExamReserveOnline(ProductDetailPageRequest pageRequest, Map<DealGroupIdDTO, List<ThirdPartyDTO>> dpDealGroupId2ThirdPartyMap) {
        String config = pageRequest.getClientTypeEnum().isMtClientType() ? "mt" + pageRequest.getPoiId() : "dp" + pageRequest.getPoiId();
        List<String> configs = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb.physical.exam.third.party.shops", String.class, new ArrayList<>());

        boolean isCfgShop = CollectionUtils.isNotEmpty(configs) && configs.contains(config);
        boolean isThirdPartyDealGroup = isThirdPartyDealGroup(dpDealGroupId2ThirdPartyMap);
        return isThirdPartyDealGroup && isCfgShop;
    }

    public boolean isThirdPartyDealGroup(Map<DealGroupIdDTO, List<ThirdPartyDTO>> dpDealGroupId2ThirdPartyMap) {
        if (MapUtils.isEmpty(dpDealGroupId2ThirdPartyMap)) {
            return false;
        }

        for (Map.Entry<DealGroupIdDTO, List<ThirdPartyDTO>> entry : dpDealGroupId2ThirdPartyMap.entrySet()) {
            List<ThirdPartyDTO> dtoList = entry.getValue();
            for (ThirdPartyDTO dto : dtoList) {
                if (org.apache.commons.lang.StringUtils.isNotBlank( dto.getThirdPartyDealID())) {
                    return true;
                }
            }
        }
        return false;
    }

    public String getApplicablePeopleDesc(ProductAttr productAttr, ProductCategory productCategory) {
        if (ORAL_TEETH_CATEGORY == productCategory.getProductSecondCategoryId()) {
            return null;
        }
        List<String> peopleApplicable = productAttr.getSkuAttrValue("tooth_suit_people");
        if (CollectionUtils.isEmpty(peopleApplicable)) {
            return null;
        }
        if (peopleApplicable.contains("成人") && peopleApplicable.contains("儿童")) {
            return "成人/儿童通用";
        }
        if (peopleApplicable.contains("成人")) {
            return "限成人";
        }
        if (peopleApplicable.contains("儿童")) {
            return "限儿童";
        }
        return null;
    }

    public String getApplicableTimeDesc(ProductAttr productAttr, ProductCategory productCategory) {
        if (ORAL_TEETH_CATEGORY == productCategory.getProductSecondCategoryId()) {
            return null;
        }
        //口腔齿科定制化适用时间展示tag
        if (DealAttrHelper.workDayAvailable(productAttr.getSkuAttrList())) {
            return "仅工作日可用";
        }
        return null;
    }

    private boolean checkOverdueAutoRefund(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getRule())
                || Objects.isNull(productBaseInfo.getRule().getRefundRule())) {
            return false;
        }
        // 是否支持过期自动退
        return productBaseInfo.getRule().getRefundRule().isSupportOverdueAutoRefund();
    }

    private boolean checkAutoRefundSwitch(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getRule())
                || Objects.isNull(productBaseInfo.getRule().getRefundRule())) {
            return false;
        }
        // 支持退款类型，0-不支持退款 1-支持随时退 2-支持7天退换
        return productBaseInfo.getRule().getRefundRule().getSupportRefundType() > 0;
    }

    private boolean checkRefundByProduct(ProductCategory productCategory, ProductAttr productAttr) {
        if (!LionConfigUtils.hitCustomRefundCategoryConfig(productCategory.getProductSecondCategoryId())) {
            return false;
        }
        String refundDesc = productAttr.getSkuAttrFirstValue("reservation_policy");
        return Objects.equals("预约成功后不可退改", refundDesc);
    }

    /**
     * 是否是酒吧营销经理
     */
    private boolean isJoyBarManager(ProductDetailPageRequest pageRequest) {
        String passParam = pageRequest.getCustomParam(RequestCustomParamEnum.pass_param);
        if (StringUtils.isBlank(passParam)) {
            return false;
        }
        try {
            JSONObject passParamJsonObject = JSON.parseObject(passParam);
            String craftsmanExtParamStr = passParamJsonObject.getString("craftsmanExtParam");
            if (StringUtils.isBlank(craftsmanExtParamStr)) {
                return false;
            }
            JSONObject craftsmanExtParamJsonObject = JSON.parseObject(craftsmanExtParamStr);
            Integer techCategoryId = craftsmanExtParamJsonObject.getInteger("techCategoryId");
            if (techCategoryId == null){
                return false;
            }
            return techCategoryId == TechCategoryEnum.JOY_BAR_MANAGER.getCategoryId();
        }catch (Exception e){
            log.error("解析来源字段异常", e);
        }
        return false;
    }

    private boolean isCleaningSelfOperationShop(List<DisplayTagDto> shopDisplayTagList) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return false;
        }
        return shopDisplayTagList.stream()
                .anyMatch(tag -> Objects.equals(tag.getTagId(), CLEANING_SELF_OPERATION_TAG_ID));
    }
}
