package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Utility class for building glasses lens related attributes
 */
@Deprecated
public class GlassesLensAttrUtils {

    // 镜片相关

    /**
     * 品牌
     */
    public static Optional<DealDetailStructuredDetailVO> buildBrand(ProductAttr productAttr) {
        List<String> lensBrands = productAttr.getSkuAttrValues("lens_brand");
        if (CollectionUtils.isEmpty(lensBrands)) {
            return Optional.empty();
        }
        
        String content;
        if (lensBrands.size() == 1) {
            content = lensBrands.get(0);
        } else {
            content = String.join("、", lensBrands) + " " + lensBrands.size() + "选1";
        }
        
        return DealDetailStructuredUtils.buildTitleAndContent("品牌", content);
    }

    /**
     * 型号
     */
    public static Optional<DealDetailStructuredDetailVO> buildModel(ProductAttr productAttr) {
        List<String> lensModels = productAttr.getSkuAttrValues("lens_model");
        if (CollectionUtils.isEmpty(lensModels)) {
            return Optional.empty();
        }
        
        String content;
        if (lensModels.size() == 1) {
            content = lensModels.get(0);
        } else {
            content = String.join("、", lensModels) + " " + lensModels.size() + "选1";
        }
        
        return DealDetailStructuredUtils.buildTitleAndContent("型号", content);
    }

    /**
     * 折射率
     */
    public static Optional<DealDetailStructuredDetailVO> buildRefractiveIndex(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("refractivity", "折射率", productAttr);
    }

    /**
     * 功能
     */
    public static Optional<DealDetailStructuredDetailVO> buildFunction(ProductAttr productAttr) {
        return EyesAttrUtils.buildFunction(productAttr);
    }

    /**
     * 技术
     */
    public static Optional<DealDetailStructuredDetailVO> buildTechnology(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("lens_technology", "技术", productAttr);
    }

    /**
     * 适用度数
     */
    public static Optional<DealDetailStructuredDetailVO> buildApplicableDegrees(ProductAttr productAttr) {
        List<String> applicableDegrees = productAttr.getSkuAttrValues("applicable_degrees");
        if (CollectionUtils.isEmpty(applicableDegrees)) {
            return Optional.empty();
        }

        List<String> degreeContents = new ArrayList<>();
        
        if (applicableDegrees.contains("近视")) {
            String myopiaDegree = productAttr.getSkuAttrFirstValue("applicableMyopiaDegree");
            if (StringUtils.isNotBlank(myopiaDegree)) {
                degreeContents.add("近视" + myopiaDegree + "度");
            }
        }
        
        if (applicableDegrees.contains("散光")) {
            String astigmiaDegree = productAttr.getSkuAttrFirstValue("applyAstigmiaDegreeRight");
            if (StringUtils.isNotBlank(astigmiaDegree)) {
                degreeContents.add("散光" + astigmiaDegree + "度");
            }
        }
        
        if (applicableDegrees.contains("远视")) {
            String hyperopiaDegree = productAttr.getSkuAttrFirstValue("hyperopia_degree");
            if (StringUtils.isNotBlank(hyperopiaDegree)) {
                degreeContents.add("远视" + hyperopiaDegree + "度");
            }
        }

        if (degreeContents.isEmpty()) {
            return Optional.empty();
        }

        String content = String.join("、", degreeContents);
        return DealDetailStructuredUtils.buildTitleAndContent("适用度数", content);
    }

    /**
     * 类型
     */
    public static Optional<DealDetailStructuredDetailVO> buildType(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("lens_type", "类型", productAttr);
    }

    /**
     * 规格
     */
    public static Optional<DealDetailStructuredDetailVO> buildSpecification(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("lens_specification", "规格", productAttr);
    }

    /**
     * 材质
     */
    public static Optional<DealDetailStructuredDetailVO> buildMaterial(ProductAttr productAttr) {
        List<String> materials = productAttr.getSkuAttrValues("lens_material");
        if (CollectionUtils.isEmpty(materials)) {
            return Optional.empty();
        }

        String content;
        if (materials.size() == 1) {
            content = materials.get(0);
        } else {
            content = String.join(" 或 ", materials);
        }

        return DealDetailStructuredUtils.buildTitleAndContent("材质", content);
    }

    // 镜框相关
    /**
     * 品牌
     */
    public static Optional<DealDetailStructuredDetailVO> buildFrameBrand(ProductAttr productAttr) {
        String frameType = productAttr.getSkuAttrFirstValue("eyeglass_frame");
        if (StringUtils.isBlank(frameType)) {
            return Optional.empty();
        }

        if ("指定价格/款式范围内任选".equals(frameType)) {
            String priceRange = productAttr.getSkuAttrFirstValue("availablePriceRange");
            String styleCount = productAttr.getSkuAttrFirstValue("frame_available_style");
            if (StringUtils.isNotBlank(priceRange) && StringUtils.isNotBlank(styleCount)) {
                String content = priceRange + "元内" + styleCount + "款镜框任选";
                return DealDetailStructuredUtils.buildTitleAndContent("品牌", content);
            }
        } else if ("指定品牌".equals(frameType)) {
            List<String> brands = productAttr.getSkuAttrValues("eyeglass_brand");
            if (CollectionUtils.isEmpty(brands)) {
                return Optional.empty();
            }

            String content;
            if (brands.size() == 1) {
                content = brands.get(0);
            } else {
                content = String.join("、", brands) + " " + brands.size() + "选1";
            }
            return DealDetailStructuredUtils.buildTitleAndContent("品牌", content);
        }

        return Optional.empty();
    }

    /**
     * 材质 frame_material
     */
    public static Optional<DealDetailStructuredDetailVO> buildFrameMaterial(ProductAttr productAttr) {
        List<String> materials = productAttr.getSkuAttrValues("frame_material");
        if (CollectionUtils.isEmpty(materials)) {
            return Optional.empty();
        }

        String content;
        if (materials.size() == 1) {
            content = materials.get(0);
        } else {
            content = String.join(" / ", materials);
        }

        return DealDetailStructuredUtils.buildTitleAndContent("材质", content);
    }

    /**
     * 款式 frame_style
     */
    public static Optional<DealDetailStructuredDetailVO> buildFrameStyle(ProductAttr productAttr) {
        List<String> styles = productAttr.getSkuAttrValues("frame_style");
        if (CollectionUtils.isEmpty(styles)) {
            return Optional.empty();
        }

        String content;
        if (styles.size() == 1) {
            content = styles.get(0);
        } else {
            content = String.join(" / ", styles);
        }

        return DealDetailStructuredUtils.buildTitleAndContent("款式", content);
    }

    /**
     * 适用人群
     */
    public static Optional<DealDetailStructuredDetailVO> buildSuitablePeople(ProductAttr productAttr) {
        String people = productAttr.getSkuAttrFirstValue("applicable_population");
        if (StringUtils.isBlank(people)) {
            return Optional.empty();
        }

        if (StringUtils.equals("各年龄段均适用", people)) {
            return DealDetailStructuredUtils.buildTitleAndContentWithPrefix("适用人群", people);
        }

        if (StringUtils.equals("指定年龄范围", people)) {
            String minAge = productAttr.getSkuAttrFirstValue("applicable_age_min");
            String maxAge = productAttr.getSkuAttrFirstValue("applicable_age_max");
            
            if (StringUtils.isAnyBlank(minAge, maxAge)) {
                return Optional.empty();
            }
            
            return DealDetailStructuredUtils.buildTitleAndContentWithPrefix("适用人群", String.format("%s岁-%s岁", minAge, maxAge));
        }

        return Optional.empty();
    }

    /**
     * 防控保障
     */
    public static Optional<DealDetailStructuredDetailVO> buildPreventionAndControl(ProductAttr productAttr) {
        String duration = productAttr.getSkuAttrFirstValue("PreventionAndControlDuration");
        String measure = productAttr.getSkuAttrFirstValue("PreventionAndControlMeasure");
        
        if (StringUtils.isAnyBlank(duration, measure)) {
            return Optional.empty();
        }
        
        String content = String.format("%s个月内防控失效可%s", duration, measure);
        return DealDetailStructuredUtils.buildTitleAndContentWithPrefix("防控保障", content);
    }
} 