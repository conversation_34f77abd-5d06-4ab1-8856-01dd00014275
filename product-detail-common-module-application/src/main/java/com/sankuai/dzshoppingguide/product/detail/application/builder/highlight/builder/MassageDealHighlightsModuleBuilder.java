package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.MassageToolFactory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HighlightsModuleAttrVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.highlights.vo.HighlightsModuleVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.DealRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 14:56
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.HIGHLIGHTS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductServiceProjectFetcher.class,
                ProductBaseInfoFetcher.class,
                ProductAttrFetcher.class
        }
)
public class MassageDealHighlightsModuleBuilder extends BaseVariableBuilder<HighlightsModuleVO> {
    private static final List<String> ATTR_ITEMS = Lists.newArrayList("特色服务", "免费餐食", "增值服务", "材料工具", "玩乐设施");
    private static final List<String> FOOD_TYPES_ORDERED = Lists.newArrayList("自助餐", "小吃简餐", "饮料", "水果", "零食", "茶水");
    public static final Set<String> FOOD_NEW_DATA = new HashSet<>(Lists.newArrayList("自助餐畅吃", "小吃简餐畅吃", "茶点水果"));
    private static final List<String> SPECIAL_SERVICES = Lists.newArrayList();

    static {
        SPECIAL_SERVICES.add("沐足礼");
        SPECIAL_SERVICES.add("净足礼");
        SPECIAL_SERVICES.add("闻香礼");
        SPECIAL_SERVICES.add("头皮检测");
        SPECIAL_SERVICES.add("发质检测");
        SPECIAL_SERVICES.add("造型补妆");
        SPECIAL_SERVICES.add("踩背");
        SPECIAL_SERVICES.add("跪背");
        SPECIAL_SERVICES.add("耳浴");
        SPECIAL_SERVICES.add("瑶浴");
        SPECIAL_SERVICES.add("脚底走罐");
        SPECIAL_SERVICES.add("颂钵");
        SPECIAL_SERVICES.add("音疗");
        SPECIAL_SERVICES.add("戴森吹干");
        SPECIAL_SERVICES.add("头部熏蒸");
        SPECIAL_SERVICES.add("草药包热敷");
        SPECIAL_SERVICES.add("草本球热敷");
        SPECIAL_SERVICES.add("膝盖姜贴");
    }

    private static final Integer MIN_SHOW_ATTR = 2;

    private static final Integer MAX_SHOW_ATTR = 3;

    private ProductServiceProject productServiceProject;
    private ProductBaseInfo productBaseInfo;
    private ProductAttr productAttr;

    @Resource
    private MassageToolFactory massageToolFactory;

    @Override
    public HighlightsModuleVO doBuild() {
        productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productAttr = getDependencyResult(ProductAttrFetcher.class);
        return buildDealHighlightsModuleVO();
    }

    private HighlightsModuleVO buildDealHighlightsModuleVO() {
        if (productServiceProject == null || productServiceProject.getServiceProject() == null) {
            return null;
        }
        // 校验标准化足疗单品团单
        ServiceProjectDTO serviceProject = getServiceProject(productServiceProject.getServiceProject());
        if (serviceProject == null || !MassageToolFactory.SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.contains(serviceProject.getCategoryId())) {
            return null;
        }

        List<HighlightsModuleAttrVO> attrs = ATTR_ITEMS.stream()
                .map(this::buildCommonAttr)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (attrs.size() < MIN_SHOW_ATTR) {
            return null;
        }

        HighlightsModuleVO highlightsModuleVO = new HighlightsModuleVO();
        highlightsModuleVO.setStyle("struct");
        highlightsModuleVO.setDelimiter("line");

        // 最多取三个
        highlightsModuleVO.setAttrs(attrs.stream().limit(MAX_SHOW_ATTR).collect(Collectors.toList()));
        return highlightsModuleVO;
    }

    private HighlightsModuleAttrVO buildCommonAttr(String key) {
        String attrValue = getAttrValue(key);
        if ( org.apache.commons.lang3.StringUtils.isBlank(attrValue)) {
            return null;
        }
        HighlightsModuleAttrVO commonAttrVO = new HighlightsModuleAttrVO();
        commonAttrVO.setName(key);
        commonAttrVO.setValue(attrValue);
        return commonAttrVO;
    }

    private String getAttrValue(String key) {
        DealGroupBasicDTO dealGroupBasicDTO = Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getBasic).orElse(null);
        ServiceProjectDTO serviceProject = getServiceProject(productServiceProject.getServiceProject());
        switch (key) {
            case "特色服务":
                return getSpecialServiceValue(dealGroupBasicDTO, serviceProject.getAttrs());
            case "免费餐食":
                return getFreeFoodValue(serviceProject.getAttrs());
            case "增值服务":
                return getOverNightValue();
            case "材料工具":
                return massageToolFactory.getToolValue(serviceProject);
            case "玩乐设施":
                return getJoyFacilityValue(serviceProject.getAttrs());
            default:
                return null;
        }
    }

    /**
     * 获取特殊服务属性值
     */
    private String getSpecialServiceValue(DealGroupBasicDTO basicDTO, List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        List<String> destList = Lists.newArrayList();
        // 获取团购标题
        Optional.ofNullable(basicDTO).map(DealGroupBasicDTO::getTitle).ifPresent(destList::add);
        // 获取服务流程
        if (CollectionUtils.isNotEmpty(serviceProjectAttrs)) {
            String serviceProcessArray = serviceProjectAttrs.stream()
                    .filter(skuAttrItemDto -> "serviceProcessArrayNew".equals(skuAttrItemDto.getAttrName()))
                    .map(ServiceProjectAttrDTO::getAttrValue).findFirst().orElse(null);
            if ( org.apache.commons.lang3.StringUtils.isNotBlank(serviceProcessArray)) {
                JSONArray foodContentArray = JSONArray.parseArray(serviceProcessArray);
                for (int i = 0; i < foodContentArray.size(); i++) {
                    // 服务内容
                    String serviceMethod = foodContentArray.getJSONObject(i).getString("servicemethod");
                    destList.add(serviceMethod);
                }
            }
        }
        return SPECIAL_SERVICES.stream()
                .filter(specialService -> destList.stream().filter(Objects::nonNull).anyMatch(value -> value.contains(specialService)))
                .findFirst().orElse(null);
    }

    /**
     * 获取免费餐食属性值
     */
    private String getFreeFoodValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        if (CollectionUtils.isEmpty(serviceProjectAttrs)) {
            return null;
        }
        // 新餐食模型
        String freeFoodStr = getNewFoodDataByServiceProjectAttr(serviceProjectAttrs);
        if( org.apache.commons.lang3.StringUtils.isNotEmpty(freeFoodStr)){
            return freeFoodStr;
        }
        String foodContentArrayStr = serviceProjectAttrs.stream()
                .filter(skuAttrItemDto -> "foodContentArray".equals(skuAttrItemDto.getAttrName()))
                .map(ServiceProjectAttrDTO::getAttrValue).findFirst().orElse(null);
        if ( org.apache.commons.lang3.StringUtils.isBlank(foodContentArrayStr)) {
            return null;
        }
        JSONArray foodContentArray = JSONArray.parseArray(foodContentArrayStr);
        if (foodContentArray == null || foodContentArray.size() == 0) {
            return null;
        }
        // 按照食物类型排序
        foodContentArray.sort(Comparator.comparing(obj -> FOOD_TYPES_ORDERED.indexOf(((JSONObject) obj).getString("foodType"))));

        // 取第一个类型的食物
        JSONObject foodContent = foodContentArray.getJSONObject(0);
        String foodType = foodContent.getString("foodType");
        switch (foodType) {
            case "自助餐":
                return "自助餐畅吃";
            case "小吃简餐":
                return "小吃简餐畅吃";
            case "茶水":
                return "茶水";
            case "零食":
                return "零食";
            case "水果":
                return "水果";
            case "饮料":
                return "饮料";
            default:
                return null;
        }
    }

    public static String getNewFoodDataByServiceProjectAttr(List<ServiceProjectAttrDTO> serviceProjectAttrs){
        String freeFood = getAttrValueByServiceProjectAttr(serviceProjectAttrs, "freeFood");

        if (FOOD_NEW_DATA.contains(freeFood)) {
            // 勾选茶点水果时，若勾选水果，返回茶点水果；若未勾选水果，返回免费餐食
            if ("茶点水果".equals(freeFood) && org.apache.commons.lang3.StringUtils.isNotBlank(getAttrValueByServiceProjectAttr(serviceProjectAttrs, "Fruit"))) {
                return "茶点水果";
            } else if ("茶点水果".equals(freeFood)) {
                return "茶点";
            } else {
                // 默认
                return freeFood;
            }
        }
        return null;
    }

    private static String getAttrValueByServiceProjectAttr(List<ServiceProjectAttrDTO> serviceProjectAttrs, String key) {
        if ( org.apache.commons.lang3.StringUtils.isEmpty(key)) {
            return null;
        }
        return serviceProjectAttrs.stream()
                .filter(skuAttrItemDto -> key.equals(skuAttrItemDto.getAttrName()))
                .map(ServiceProjectAttrDTO::getAttrValue).findFirst().orElse(null);
    }

    /**
     * 获取玩乐设施属性值
     */
    private String getJoyFacilityValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        return serviceProjectAttrs.stream()
                .filter(serviceProjectAttr -> "serviceFacility".equals(serviceProjectAttr.getAttrName()))
                .filter(serviceProjectAttr -> org.apache.commons.lang3.StringUtils.isNotBlank(serviceProjectAttr.getAttrValue()) && !"无".equals(serviceProjectAttr.getAttrValue()))
                .map(ServiceProjectAttrDTO::getAttrValue).findFirst().orElse(null);
    }

    /**
     * 获取增值服务属性值 -> 可过夜
     */
    private String getOverNightValue() {
        if(isFreeOverNight(productAttr)) {
            return "可免费过夜";
        }
        return StringUtils.EMPTY;
    }

    private ServiceProjectDTO getServiceProject(DealGroupServiceProjectDTO dealGroupServiceProject) {
        if (dealGroupServiceProject == null || CollectionUtils.isEmpty(dealGroupServiceProject.getMustGroups())) {
            return null;
        }
        // 标准化团单只有一个商品，且是必选
        MustServiceProjectGroupDTO mustServiceProjectGroupDTO = dealGroupServiceProject.getMustGroups().get(0);
        List<ServiceProjectDTO> groups = mustServiceProjectGroupDTO.getGroups();
        if (CollectionUtils.isEmpty(groups)) {
            return null;
        }
        return groups.get(0);
    }

    private boolean isFreeOverNight(ProductAttr productAttr) {
        String overnightServiceType = productAttr.getSkuAttrFirstValue("OvernightServices");
        return Objects.equals(overnightServiceType, "可免费过夜");
    }
}
