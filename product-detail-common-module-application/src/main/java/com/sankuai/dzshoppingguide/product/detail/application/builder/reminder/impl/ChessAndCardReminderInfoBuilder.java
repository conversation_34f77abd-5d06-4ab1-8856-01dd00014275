package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 14:08
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class, ProductAttrFetcher.class, GuaranteeTagFetcher.class, ProductPurchaseNoteFetcher.class}
)
@Slf4j
public class ChessAndCardReminderInfoBuilder extends AbstractReminderInfoBuilder {
    private ProductAttr productAttr;
    private ProductBaseInfo baseInfo;
    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }

        baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productAttr = getDependencyResult(ProductAttrFetcher.class);

        if (productAttr == null) {
            return null;
        }

        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();

        // 【预约信息】·【日期】【时间段】可用·【有效时间】
        if (hasAvailableTimePeriod()) {
            // 如果选择了部分时间可用
            AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        } else {
            // 选择了营业时间内全部可用
            AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
        }

        // 有效期
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(baseInfo)).ifPresent(contents::add);

        return baseReminderInfo;
    }

    /**
     * 判断是否选择了部分时间可用
     * @return
     */
    private boolean hasAvailableTimePeriod() {
        // 【预约信息】·【日期】【时间段】可用·【有效时间】
        String availableTimePeriod3 = productAttr.getSkuAttrFirstValue("AvailableTimePeriod3");
        return StringUtils.equals("部分时间可用",availableTimePeriod3);
    }
}
