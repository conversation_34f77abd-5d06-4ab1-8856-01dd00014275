package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * @author: created by hang.yu on 2023/8/17 11:04
 */
@Component("earStrategyImpl")
public class EarStrategyImpl extends AbstractMassageStrategy {

    private static final String ONE_TIME_TOOL = "一次性工具";

    private static final String SPECIAL_ERA_TOOL = "特色采耳工具";

    private static final List<String> DISPOSABLE_PRIORITY_LIST = Lists.newArrayList();

    static {
        DISPOSABLE_PRIORITY_LIST.add("耳内镜");
        DISPOSABLE_PRIORITY_LIST.add("一次性床单");
        DISPOSABLE_PRIORITY_LIST.add("一次性短裤");
        DISPOSABLE_PRIORITY_LIST.add("香薰");
        DISPOSABLE_PRIORITY_LIST.add("眼罩");
        DISPOSABLE_PRIORITY_LIST.add("电动按摩床");
        DISPOSABLE_PRIORITY_LIST.add("一次性拖鞋");
        DISPOSABLE_PRIORITY_LIST.add("一次性按摩巾");
        DISPOSABLE_PRIORITY_LIST.add("消毒按摩服");
    }

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 一次性工具 > 特色采耳工具 > 热敷工具 > 按摩工具 > 一次性材料工具+其他工具
        // 一次性材料及工具
        String disposableMaterial = getAttrValue(serviceProjectAttrs, DISPOSABLE_MATERIAL);
        if (StringUtils.isNotBlank(disposableMaterial) && disposableMaterial.contains(ONE_TIME_TOOL)) {
            return ONE_TIME_TOOL;
        }
        // 特色采耳工具
        String earpickingTool = getAttrValue(serviceProjectAttrs, EAR_PICKING_TOOL);
        if (StringUtils.isNotBlank(earpickingTool)) {
            return SPECIAL_ERA_TOOL;
        }
        // 热敷工具
        String hotpackTool = getAttrValue(serviceProjectAttrs, HOTPACK_TOOL);
        if (StringUtils.isNotBlank(hotpackTool)) {
            return hotpackTool;
        }
        // 按摩工具
        String massageTool = getAttrValue(serviceProjectAttrs, MASSAGE_TOOL);
        if (StringUtils.isNotBlank(massageTool)) {
            return massageTool;
        }
        // 一次性材料工具+其他工具
        List<String> mixedTools = getMixedTools(serviceProjectAttrs);
        if (CollectionUtils.isEmpty(mixedTools)) {
            return null;
        }
        mixedTools.remove(ONE_TIME_TOOL);
        // 对一次性材料及工具、其他工具进行混排
        mixedTools.sort(Comparator.comparingInt(DISPOSABLE_PRIORITY_LIST::indexOf));

        return Optional.ofNullable(mixedTools).filter(CollectionUtils::isNotEmpty).map(list -> list.get(0)).orElse(null);
    }

}
