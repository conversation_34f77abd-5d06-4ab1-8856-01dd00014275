package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.RestaurantTimeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BuffetFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.MassageVO;
import com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/7 14:21
 */
@Component
@Slf4j
public class BuffetFootStyle implements Style<MassageVO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<MassageVO> build(DealDetailBuildContext context) {
        long productId = Optional.ofNullable(context).map(DealDetailBuildContext::getRequest).map(ProductDetailPageRequest::getProductId).orElse(0L);
        try {
            ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
            if (Objects.isNull(productAttr)) {
                return Collections.emptyList();
            }

            List<MassageVO> buffetItems = Lists.newArrayList();

            // 亮点食材
            List<String> buffetHighlights = productAttr.getSkuAttrValues(MassageCPVConstant.BUFFET_HIGHLIGHTS);
            if (CollectionUtils.isNotEmpty(buffetHighlights)) {
                buffetItems.add(
                        MassageVO.builder()
                                .title(BuffetFoodEnum.BUFFET_HIGHLIGHTS.getFoodName())
                                .content(buffetHighlights.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("、")))
                                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                                .icon(BuffetFoodEnum.BUFFET_HIGHLIGHTS.getNewFoodIcon())
                                .build()
                );
            }

            // 餐食内容
            List<String> buffetContents = productAttr.getSkuAttrValues(MassageCPVConstant.BUFFET_CONTENT);
            if (CollectionUtils.isNotEmpty(buffetContents)) {
                buffetItems.add(
                        MassageVO.builder()
                                .title(BuffetFoodEnum.BUFFET_CONTENT.getFoodName())
                                .content(buffetContents.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("、")))
                                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                                .icon(BuffetFoodEnum.BUFFET_CONTENT.getNewFoodIcon())
                                .build()
                );
            }

            // 用餐规则
            StringBuilder stringBuilder = new StringBuilder();
            String buffetDuration = productAttr.getSkuAttrFirstValue(MassageCPVConstant.BUFFET_DURATION);
            if (StringUtils.isNotBlank(buffetDuration)) {
                stringBuilder.append(buffetDuration.contains("不限时") ? "用餐时长（不限时）" : String.format("用餐时长（不超过%s）",buffetDuration));
            }

            List<String> buffetTimeListStr = productAttr.getSkuAttrValues(MassageCPVConstant.BUFFET_SERVICE_TIME);
            List<RestaurantTimeDTO> restaurantTimes = FootMassageUtils.parseProcess(buffetTimeListStr, RestaurantTimeDTO.class);
            String buffetTime = parseBuffetTime(restaurantTimes);
            if (StringUtils.isNotEmpty(buffetTime)) {
                if (stringBuilder.length() > 0) {
                    stringBuilder.append("，");
                }
                stringBuilder.append(String.format("供餐时间%s",buffetTime));
            }

            if (stringBuilder.length() > 0) {
                buffetItems.add(
                        MassageVO.builder()
                                .title(BuffetFoodEnum.BUFFET_RULES.getFoodName())
                                .content(stringBuilder.toString())
                                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                                .icon(BuffetFoodEnum.BUFFET_RULES.getNewFoodIcon())
                                .build()
                );
            }

            if (CollectionUtils.isNotEmpty(buffetItems)) {
                buffetItems.add(0,MassageVO.builder().title("自助餐畅吃").detail("免费").type(ViewComponentTypeEnum.DETAIL_TYPE_4.getType()).build());
            }

            return buffetItems;
        } catch (Exception e) {
            log.error("build buffet items error,productId:{}", productId, e);
            return Collections.emptyList();
        }
    }

    private String parseBuffetTime(List<RestaurantTimeDTO> restaurantTimes) {
        if (CollectionUtils.isEmpty(restaurantTimes)) {
            return StringUtils.EMPTY;
        }

        return restaurantTimes.stream().filter(Objects::nonNull).map(e -> e.getStartTime() + "-" + AvailableTimeHelper.getTimeOfDayDoc(e.getEndTime())).collect(Collectors.joining("，"));
    }
}
