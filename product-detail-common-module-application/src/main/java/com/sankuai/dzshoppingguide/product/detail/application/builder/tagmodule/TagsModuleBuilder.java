package com.sankuai.dzshoppingguide.product.detail.application.builder.tagmodule;

import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags.RankTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags.RankTagResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags.ReviewTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags.ReviewTagResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CommonRichTextDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.TagsEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.BaseTagDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.RankTagVO;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.ReviewTagVO;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.TagsModuleVO;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.AbInfo;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingLabelDTO;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import joptsimple.internal.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 14:17
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_TAGS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {RankTagFetcher.class, ReviewTagFetcher.class}
)
public class TagsModuleBuilder extends BaseBuilder<TagsModuleVO> {
    // rank
    private static final String RANK_TAG_BACKGROUND_COLOR = "#FFEDDE";
    private static final String RANK_TAG_FONT_COLOR = "#8E3C12";
    private static final String RANK_TAG_FONT_TYPE = "PingFangSC-Regular";


    private static final String FONT_SIZE = "10";

    // review
    private static final String REVIEW_TAG_BACKGROUND_COLOR = "#FFF1EC";
    private static final String REVIEW_TAG_FONT_COLOR = "#FF4B10";
    private static final String REVIEW_TAG_RATIO_FONT_TYPE = "MTfin3.0"; // 好评率字体类型
    private static final String REVIEW_TAG_FONT_TYPE = "PingFangSC-Regular"; // 评价数字体类型

    @Override
    public TagsModuleVO doBuild() {
        RankTagResult rankTagResult = getDependencyResult(RankTagFetcher.class);
        ReviewTagResult reviewTagResult = getDependencyResult(ReviewTagFetcher.class);

        List<BaseTagDTO> tags = Lists.newArrayList();
        buildRankTagVO(rankTagResult).ifPresent(tags::add);
        buildReviewTagVO(reviewTagResult).ifPresent(tags::add);

        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }

        TagsModuleVO tagsModuleVO = new TagsModuleVO();
        tagsModuleVO.setTags(tags);
        return tagsModuleVO;
    }

    private Optional<RankTagVO> buildRankTagVO(RankTagResult rankTagResult) {
        if (rankTagResult == null || rankTagResult.getRankingResult() == null) {
            return Optional.empty();
        }
        RankingResult rankingResult = rankTagResult.getRankingResult();

        Map<Long, RankingLabelDTO> firstRankMap = rankingResult.getFirstRankMap();
        List<AbInfo> abInfoList = rankingResult.getAbInfoList();
        if ( MapUtils.isEmpty(firstRankMap)) {
            return Optional.empty();
        }

        RankingLabelDTO rankLabelDTO = firstRankMap.getOrDefault(request.getProductId(), null);
        if (rankLabelDTO == null) {
            return Optional.empty();
        }
        RankTagVO rankTagVO = new RankTagVO();
        if ( CollectionUtils.isNotEmpty(abInfoList)) {
            List<com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.AbInfo> abInfos = abInfoList.stream().filter(Objects::nonNull).map(ab -> {
                com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.AbInfo abInfo = new com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.AbInfo();
                abInfo.setAbCode(ab.getAbCode());
                abInfo.setQueryId(ab.getQueryId());
                return abInfo;
            }).collect(Collectors.toList());
            rankTagVO.setAbInfo(abInfos);
        }
        rankTagVO.setType(TagsEnum.RANK.getCode());
        rankTagVO.setRankType(rankLabelDTO.getRankType());
        rankTagVO.setRanking(rankLabelDTO.getRanking());
        rankTagVO.setLink(rankLabelDTO.getRankLink());
        rankTagVO.setPrefixIcon(buildIcon(rankLabelDTO));
        rankTagVO.setRankScene(rankLabelDTO.getRankScene());
        rankTagVO.setContents(buildRankContents(rankLabelDTO));
        rankTagVO.setBackgroundColor(RANK_TAG_BACKGROUND_COLOR);
        return Optional.of(rankTagVO);
    }

    private boolean buildIcon(RankingLabelDTO rankLabelDTO) {
        String iconUrl = Optional.ofNullable(rankLabelDTO).map(RankingLabelDTO::getIcon).orElse(StringUtils.EMPTY);
        return StringUtils.isBlank(iconUrl);
    }

    private List<CommonRichTextDTO> buildRankContents(RankingLabelDTO rankLabelDTO) {
        String labelName = Optional.ofNullable(rankLabelDTO).map(RankingLabelDTO::getLabelName).orElse(StringUtils.EMPTY);
        if ( StringUtils.isBlank(labelName)) {
            return Lists.newArrayList();
        }
        CommonRichTextDTO richTextDTO = new CommonRichTextDTO();
        richTextDTO.setText(labelName);
        richTextDTO.setFontColor(RANK_TAG_FONT_COLOR);
        richTextDTO.setFontSize(FONT_SIZE);
        richTextDTO.setFontType(RANK_TAG_FONT_TYPE);
        return Lists.newArrayList(richTextDTO);
    }

    private Optional<ReviewTagVO> buildReviewTagVO(ReviewTagResult reviewTagResult) {
        if ( reviewTagResult == null) {
            return Optional.empty();
        }
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        return isMt ? buildMtGoodReview(reviewTagResult.getMtReviewInfo()) : buildDpGoodReview(reviewTagResult.getDpReviewInfo());
    }


    public Optional<ReviewTagVO> buildMtGoodReview(ReviewCount mtReviewCount) {
        ReviewTagVO result = new ReviewTagVO();
        if (mtReviewCount == null) {
            return Optional.empty();
        }
        result.setType(TagsEnum.REVIEW.getCode());
        // 统计好评数量 40、45、50分为好评
        int goodReviewCount = mtReviewCount.getStars().get(50) + mtReviewCount.getStars().get(40) + mtReviewCount.getStars().get(45) ;
        String reviewRatio = getReviewRatio(goodReviewCount, mtReviewCount.getAll());
        result.setPrefixIcon(StringUtils.isNotBlank(reviewRatio));
        result.setGoodReviewRatio(reviewRatio);

        if (mtReviewCount.getAll() == 0 && StringUtils.isBlank(reviewRatio)) {
            return Optional.empty();
        }

        List<CommonRichTextDTO> commonRichTextDTOS = buildRichContents(reviewRatio, mtReviewCount.getAll());
        if (CollectionUtils.isEmpty(commonRichTextDTOS)) {
            return Optional.empty();
        }
        result.setContents(commonRichTextDTOS);

        result.setLink(UrlHelper.getGoodReviewUrl(request.getProductId(), request.getClientTypeEnum()));
        result.setSuffixIcon(true);

        int avgRate = mtReviewCount.getAvgRate();
        if (avgRate > 0) {
            result.setReviewScore(String.valueOf(avgRate * 1.0 / 10));
        }
        result.setBackgroundColor(REVIEW_TAG_BACKGROUND_COLOR);
        return Optional.of(result);
    }

    public Optional<ReviewTagVO> buildDpGoodReview(ReviewStarDistributionDTO reviewStar) {
        ReviewTagVO result = new ReviewTagVO();
        if (reviewStar == null) {
            return Optional.empty();
        }
        result.setType(TagsEnum.REVIEW.getCode());
        int goodReview = reviewStar.getStar4Count() + reviewStar.getFourHalfStarCount() + reviewStar.getStar5Count();
        String reviewRatio = getReviewRatio(goodReview, reviewStar.getReviewCount());
        result.setPrefixIcon(StringUtils.isNotBlank(reviewRatio));
        result.setGoodReviewRatio(reviewRatio);
        if (reviewStar.getReviewCount() == 0 && StringUtils.isBlank(reviewRatio)) {
            return Optional.empty();
        }

        List<CommonRichTextDTO> commonRichTextDTOS = buildRichContents(reviewRatio, reviewStar.getReviewCount());
        if (CollectionUtils.isEmpty(commonRichTextDTOS)) {
            return Optional.empty();
        }
        result.setContents(commonRichTextDTOS);
        result.setLink(UrlHelper.getGoodReviewUrl(request.getProductId(), request.getClientTypeEnum()));
        result.setSuffixIcon(true);
        result.setBackgroundColor(REVIEW_TAG_BACKGROUND_COLOR);
        return Optional.of(result);
    }

    private List<CommonRichTextDTO> buildRichContents(String reviewRatio,int reviewCounts) {
        List<CommonRichTextDTO> result = Lists.newArrayList();

        // 无好评率
        if (StringUtils.isNotBlank(reviewRatio)) {
            // 98%
            CommonRichTextDTO richTextDTO1 = new CommonRichTextDTO();
            richTextDTO1.setText(reviewRatio);
            richTextDTO1.setFontSize(FONT_SIZE);
            richTextDTO1.setFontColor(REVIEW_TAG_FONT_COLOR);
            richTextDTO1.setFontType(REVIEW_TAG_RATIO_FONT_TYPE);
            richTextDTO1.setFontWeight("400");
            result.add(richTextDTO1);

            // 好评
            CommonRichTextDTO richTextDTO2 = new CommonRichTextDTO();
            richTextDTO2.setText("好评");
            richTextDTO2.setFontSize(FONT_SIZE);
            richTextDTO2.setFontColor(REVIEW_TAG_FONT_COLOR);
            richTextDTO2.setFontType(REVIEW_TAG_RATIO_FONT_TYPE);
            richTextDTO2.setFontWeight("500");
            result.add(richTextDTO2);
        }

        if (CollectionUtils.isNotEmpty(result)) {
            CommonRichTextDTO richTextDTO3 = new CommonRichTextDTO();
            richTextDTO3.setText("·");
            richTextDTO3.setDotFlag(true);
            richTextDTO3.setFontColor(REVIEW_TAG_FONT_COLOR);
            result.add(richTextDTO3);
        }

        // 是否有评价数
        if (reviewCounts > 0) {
            // 2013条评价
            CommonRichTextDTO richTextDTO4 = new CommonRichTextDTO();
            richTextDTO4.setText(String.format("%s条评价",reviewCounts));
            richTextDTO4.setFontSize(FONT_SIZE);
            richTextDTO4.setFontColor(REVIEW_TAG_FONT_COLOR);
            richTextDTO4.setFontType(REVIEW_TAG_FONT_TYPE);
            richTextDTO4.setFontWeight("400");
            result.add(richTextDTO4);
        } else {
            // 无多少条评价该信息,所以需要删除 dot
            result = result.stream().filter(item -> item != null && !item.isDotFlag()).collect(Collectors.toList());
        }

        return result;
    }

    private String getReviewRatio(int goodReviewCount, int totalCount) {
        if (totalCount <= 0) {
            return Strings.EMPTY;
        }
        double ret = goodReviewCount * 100.0 / totalCount;
        return Math.round(ret) + "%";
    }
}
