package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor.UserFavorStatusFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor.UserFavorStatusReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailNavBarStatusInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarShareModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.NavBarItemTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.navbar.vo.NavigationBarVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/8
 */
@Builder(
        moduleKey = ModuleKeyConstants.NAVIGATION_BAR,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {UserFavorStatusFetcher.class, ProductBaseInfoFetcher.class, ShopIdMapperFetcher.class}
)
public class NavigationBarModuleBuilder extends BaseBuilder<NavigationBarVO> {
    @Override
    public NavigationBarVO doBuild() {
        NavigationBarVO navigationBarVO = new NavigationBarVO();

        // 获取依赖数据
        UserFavorStatusReturnValue userFavorStatusReturnValue = getDependencyResult(UserFavorStatusFetcher.class);
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ShopIdMapper idMapper = getDependencyResult(ShopIdMapperFetcher.class);

        // 组装分享信息
        NavBarShareModuleVO navBarShareModuleVO = buildShareModuleVO(baseInfo, idMapper);
        // 组装导航栏静态信息
        List<NavBarItem> navBarItemList = buildNavBarItemList(idMapper);
        // 组装状态信息
        DetailNavBarStatusInfo statusInfo = buildStatusInfo(userFavorStatusReturnValue);

        navigationBarVO.setShare(navBarShareModuleVO);
        navigationBarVO.setNavbar(navBarItemList);
        navigationBarVO.setStatusInfo(statusInfo);
        return navigationBarVO;
    }

    private DetailNavBarStatusInfo buildStatusInfo(UserFavorStatusReturnValue userFavorStatusReturnValue) {
        DetailNavBarStatusInfo statusInfo = new DetailNavBarStatusInfo();
        if (Objects.isNull(userFavorStatusReturnValue))  return statusInfo;
        statusInfo.setFavorStatus(userFavorStatusReturnValue.isFavored() ? 1 : 0);
        return statusInfo;
    }

    private NavBarShareModuleVO buildShareModuleVO(ProductBaseInfo baseInfo, ShopIdMapper idMapper) {
        // 后续如果涉及不可分享场景，直接返回null即可
        if (Objects.isNull(baseInfo)) return null;
        NavBarShareModuleVO navBarShareModuleVO = new NavBarShareModuleVO();
        String title = Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getBasic)
                .filter(Objects::nonNull)
                .map(DealGroupBasicDTO::getTitle)
                .orElse(StringUtils.EMPTY);
        String image = Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getImage)
                .filter(Objects::nonNull)
                .map(DealGroupImageDTO::getDefaultPicPath)
                .orElse(StringUtils.EMPTY);
        navBarShareModuleVO.setTitle(title);
        navBarShareModuleVO.setImage(StringUtils.isBlank(image) ? "" : image + "@200w_200h_1e_1c_1l");
        navBarShareModuleVO.setUrl(UrlHelper.getShareUrl(request, idMapper));
//        navBarShareModuleVO.setMiniProgramConfig(buildMiniProgramConfig());   预订暂无小程序不下发
        return navBarShareModuleVO;
    }

    private ShareModuleMiniProgramConfig buildMiniProgramConfig() {
        ShareModuleMiniProgramConfig config = new ShareModuleMiniProgramConfig();
        config.setMiniProgramId("");
        config.setImage("");
        config.setPath("");
        return config;
    }

    private List<NavBarItem> buildNavBarItemList(ShopIdMapper idMapper) {
        List<NavBarItem> navBarItems = LionConfigUtils.getNavBarItemList();
        navBarItems.stream()
                .filter(item -> item.getType() == NavBarItemTypeEnum.NAV_BAR_MORE.getCode())
                .filter(Objects::nonNull)
                .flatMap(item -> item.getPopover().stream())
                .forEach(item -> {
                    if (item.getType() == NavBarItemTypeEnum.NAV_BAR_POI.getCode() && Objects.nonNull(idMapper)) {
                        item.setJumpUrl(UrlHelper.getShopUrl(request, idMapper.getDpBestShopId(), idMapper.getMtBestShopId()));
                    }
                    if (item.getType() == NavBarItemTypeEnum.NAV_BAR_HOME_PAGE.getCode()) {
                        item.setJumpUrl(UrlHelper.getHomeUrl(request));
                    }
                });
        return navBarItems;
    }
}
