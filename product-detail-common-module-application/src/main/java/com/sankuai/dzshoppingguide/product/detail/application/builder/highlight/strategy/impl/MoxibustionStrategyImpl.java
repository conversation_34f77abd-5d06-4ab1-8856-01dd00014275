package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;

/**
 * 艾灸
 *
 * @author: created by hang.yu on 2023/8/17 11:03
 */
@Component("moxibustionStrategyImpl")
public class MoxibustionStrategyImpl extends AbstractMassageStrategy {

    private static final String SMOKE_TOOL = "排烟设备";

    private static final List<String> DISPOSABLE_PRIORITY_LIST = Lists.newArrayList();

    static {
        DISPOSABLE_PRIORITY_LIST.add("生姜");
        DISPOSABLE_PRIORITY_LIST.add("电动按摩床");
        DISPOSABLE_PRIORITY_LIST.add("一次性床单");
        DISPOSABLE_PRIORITY_LIST.add("消毒按摩服");
        DISPOSABLE_PRIORITY_LIST.add("一次性拖鞋");
        DISPOSABLE_PRIORITY_LIST.add("一次性按摩巾");
        DISPOSABLE_PRIORITY_LIST.add("盐");
    }

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 排烟设备 > 艾灸工具/仪器 > 艾灸材料 > 一次性材料及工具
        // 其他工具
        String unclassifiedTools = getAttrValue(serviceProjectAttrs, UNCLASSIFIED_TOOLS);
        if (StringUtils.isNotBlank(unclassifiedTools) && unclassifiedTools.contains(SMOKE_TOOL)) {
            return SMOKE_TOOL;
        }
        // 艾灸工具/仪器
        String moxibustionTool = getAttrValue(serviceProjectAttrs, MOXIBUSTION_TOOL);
        if (StringUtils.isNotBlank(moxibustionTool)) {
            return moxibustionTool;
        }
        // 艾灸材料
        String moxibustionMaterial = getAttrValue(serviceProjectAttrs, MOXIBUSTION_MATERIAL);
        if (StringUtils.isNotBlank(moxibustionMaterial)) {
            return moxibustionMaterial;
        }
        List<String> mixedTools = getMixedTools(serviceProjectAttrs);
        if (CollectionUtils.isEmpty(mixedTools)) {
            return null;
        }
        // 对一次性材料及工具、其他工具进行混排
        mixedTools.sort(Comparator.comparingInt(DISPOSABLE_PRIORITY_LIST::indexOf));
        return mixedTools.get(0);
    }

}
