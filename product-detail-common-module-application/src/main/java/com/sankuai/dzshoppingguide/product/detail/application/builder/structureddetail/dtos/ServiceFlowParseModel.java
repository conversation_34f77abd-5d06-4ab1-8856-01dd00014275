package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @author: created by hang.yu on 2023/3/15 16:28
 * 服务流程解析实体
 */
@Data
@TypeDoc(description = "服务流程解析实体")
public class ServiceFlowParseModel {

    @FieldDoc(description = "服务部位")
    @JsonProperty("bodypart")
    private String bodyPart;

    @FieldDoc(description = "服务方式")
    @JsonProperty("servicemethod")
    private String serviceMethod;

    @FieldDoc(description = "服务时间")
    @JsonProperty("stepTime")
    private int stepTime;

    @FieldDoc(description = "服务流程说明")
    @JsonProperty("ServiceProcessInstructions")
    private String serviceProcessInstructions;

    @FieldDoc(description = "步骤时间")
    @JsonProperty("StepDuration")
    private String stepDuration;

}
