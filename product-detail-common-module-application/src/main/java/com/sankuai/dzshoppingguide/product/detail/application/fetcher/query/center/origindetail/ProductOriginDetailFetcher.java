package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail;

import com.alibaba.fastjson.JSONValidator;
import com.dianping.lion.common.util.JsonUtils;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ProductIntroductionDTO;
import com.sankuai.general.product.query.center.client.builder.model.OriginDetailBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-26
 */
@Slf4j
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class,
        fillRequestDependencies = {

        }
)
public class ProductOriginDetailFetcher extends ComponentFetcherContext<QueryByDealGroupIdRequestBuilder, QueryCenterAggregateReturnValue, ProductOriginDetail> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder.originDetail(OriginDetailBuilder.builder().all());
    }

    @Override
    protected FetcherResponse<ProductOriginDetail> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        ProductOriginDetail productOriginDetail = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getOriginDetails)
                .map(this::convert)
                .orElse(new ProductOriginDetail());
        return FetcherResponse.succeed(productOriginDetail);
    }

    private ProductOriginDetail convert(Map<String, String> originDetails) {
        if (MapUtils.isEmpty(originDetails)) {
            return new ProductOriginDetail();
        }
        // 取图文详情
        String productIntro = originDetails.get("productIntro");
        if (StringUtils.isBlank(productIntro)) {
            return new ProductOriginDetail();
        }
        try (JSONValidator validator = JSONValidator.from(productIntro)) {
            if (!validator.validate()) {
                return new ProductOriginDetail();
            }
            ProductIntroductionDTO productIntroductionDTO = JsonUtils.fromJson(productIntro, ProductIntroductionDTO.class);
            return new ProductOriginDetail(productIntroductionDTO);
        } catch (Exception e) {
            log.error("parse productIntro error, productIntro:{}", productIntro, e);
        }
        return new ProductOriginDetail();
    }
}
