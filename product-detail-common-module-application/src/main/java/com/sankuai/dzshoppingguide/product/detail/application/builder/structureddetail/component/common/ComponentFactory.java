package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 10:59
 * @Description: TODO
 */
@Component
public class ComponentFactory {

    @Resource
    private UniversalComponent0 universalComponent;

    /**
     * 创建适用于特定VO类型的组件
     *
     * @param <T> VO类型参数，必须是BaseStructuredDetailVO的子类
     * @return 适用于指定VO类型的组件
     */
    @SuppressWarnings("unchecked")
    public <T> CommonComponent0<T> createComponent() {
        // 由于UniversalComponent是泛型的，可以处理任意BaseStructuredDetailVO子类
        return (CommonComponent0<T>) universalComponent;
    }
}
