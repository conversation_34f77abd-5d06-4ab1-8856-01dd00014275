package com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/6 11:39
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class RankTagResult extends FetcherReturnValueDTO {
    private RankingResult rankingResult;
}
