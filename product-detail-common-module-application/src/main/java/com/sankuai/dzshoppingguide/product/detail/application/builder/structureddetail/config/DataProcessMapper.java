package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.config;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.StructuredDetailDataSourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-14
 * @desc
 */
@Data
public class DataProcessMapper implements Serializable {
    private StructuredDetailDataSourceEnum dataSource;
    private String originField;
    private String targetField;
    private String transform;
    // 是否是数组
    private boolean isArray;
    // 需要解析JSON字符串
    private boolean parseJSON;
    // 数组元素映射规则
    List<DataProcessMapper> itemMapping;
}
