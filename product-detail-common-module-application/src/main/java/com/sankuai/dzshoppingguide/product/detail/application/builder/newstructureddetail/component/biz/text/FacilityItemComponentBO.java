package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.DetailComponentKeyConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType2ViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType3ViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType5ViewComponent;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-30
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Facility_Item,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "服务设施详情",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class FacilityItemComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "服务设施项目标题")
    private String itemName;
    @DigitalArchField(desc = "服务设施项目标题图标", required = false)
    private String itemNameIcon;
    @DigitalArchField(desc = "服务设施项目副标题", required = false)
    private String itemSubName;
    @DigitalArchField(desc = "服务设施项目弹窗图标", required = false)
    private String itemPopupIcon;
    @DigitalArchField(desc = "服务设施项目弹窗数据", required = false)
    private String itemPopupData;
    @DigitalArchField(desc = "服务设施项目具体描述")
    private List<String> itemDesc;
    @DigitalArchField(desc = "服务设施项目具体描述带图标", required = false)
    private List<Icon> itemDescWithIcon;

    @Override
    protected ComponentVO doBuildVO(int recursionDepth) {
        BaseViewComponent itemNameViewComponent = FacilityType2ViewComponent.builder()
                .title(itemName)
                .build();
        BaseViewComponent itemDescViewComponent = null;
        if (CollectionUtils.isNotEmpty(itemDesc)) {
            itemDescViewComponent = FacilityType3ViewComponent.builder()
                    .contents(itemDesc)
                    .build();
        } else if (CollectionUtils.isNotEmpty(itemDescWithIcon)) {
            itemDescViewComponent = FacilityType5ViewComponent.builder()
                    .contentsWithIcon(itemDescWithIcon)
                    .build();
        }
        return new BizComponentVO(itemNameViewComponent, itemDescViewComponent);
    }
}