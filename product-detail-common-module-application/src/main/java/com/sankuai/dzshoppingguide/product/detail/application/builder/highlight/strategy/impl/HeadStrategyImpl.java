package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/26 14:04
 */
@Component("headStrategyImpl")
public class HeadStrategyImpl extends AbstractMassageStrategy {
    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        /**
         * 头疗暂无工具
         */
        return "";
    }
}
