package com.sankuai.dzshoppingguide.product.detail.application.fetcher.multitags;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/6 11:39
 */
@Fetcher(
        timeout = 50000L,
        previousLayerDependencies = {ShopIdMapperFetcher.class}
)
@Slf4j
public class RankTagFetcher extends NormalFetcherContext<RankTagResult> {
    private static long mtShopId;
    private static long dpShopId;

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<RankTagResult> doFetch() {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);

        return compositeAtomService.queryProductRankTag(buildRankLabelRequest()).thenApply(res -> {
            if (res == null) {
                return null;
            }
            return new RankTagResult(res);
        });
    }

    private RankLabelDataRequest buildRankLabelRequest() {
        RankLabelDataRequest req = new RankLabelDataRequest();
        req.setPlatform(request.getPlatformEnum().getCode());
        req.setBizIds(Lists.newArrayList(request.getProductId()));
        req.setSource("deal_detail_page");
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        req.setClientType(getClientType(clientTypeEnum));
        req.setNeedLink(true);
        req.setVersion(request.getShepherdGatewayParam().getAppVersion());
        req.setExtendInfo(buildExtendInfo());
        return req;
    }
    
    private Map<String, Object> buildExtendInfo() {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        Map<String, Object> extendInfo = Maps.newHashMap();
        Map<Long, Long> productId2ShopIdMap = Maps.newHashMap();
        productId2ShopIdMap.put(request.getProductId(), isMt ? mtShopId : dpShopId);
        extendInfo.put("productId2ShopIdMap",productId2ShopIdMap);
        // 实体类型2-密室，3-团购，4-泛商品，5-泛商品sku 必传
        extendInfo.put("bizType",3);
        extendInfo.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        return extendInfo;
    }

    private int getClientType(ClientTypeEnum clientType) {
        if (clientType.isInApp()) {
            return com.sankuai.mdp.dzshoplist.rank.api.enums.ClientTypeEnum.APP.getType();
        } else if (clientType.isInWxXCX()) {
            return com.sankuai.mdp.dzshoplist.rank.api.enums.ClientTypeEnum.WX_XCX.getType();
        } else {
            return com.sankuai.mdp.dzshoplist.rank.api.enums.ClientTypeEnum.H5.getType();
        }
    }
}
