package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos;

import com.dianping.deal.detail.dto.DealGroupIdDTO;
import com.dianping.deal.detail.dto.ThirdPartyDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.general.product.query.center.client.dto.combine.CombineDTO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 商品详情页保障条构造参数
 */
@Data
public class GuaranteeParam {
    private ProductDetailPageRequest pageRequest;

    private ProductAttr productAttr;

    private ProductBaseInfo productBaseInfo;

    private ProductCategory productCategory;

    private List<DisplayTagDto> shopDisplayTagList;

    private Map<DealGroupIdDTO, List<ThirdPartyDTO>> dpDealGroupId2ThirdPartyMap;

    private List<CombineDTO> combines;

    private ProductGuaranteeTagInfo productGuaranteeTagInfo;
}
