package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SkuModel {

    private long skuId;

    private long productCategory;

    private String name;

    private int copies;

    private BigDecimal marketPrice;

    private List<DealSkuAttrModel> skuAttrs;

    public String getAttrValue(String attrName) {
        if (StringUtils.isBlank(attrName) || CollectionUtils.isEmpty(skuAttrs)) {
            return null;
        }
        return skuAttrs.stream()
                .filter(attr -> attrName.equals(attr.getAttrName()))
                .findFirst()
                .map(DealSkuAttrModel::getAttrValue)
                .orElse(null);
    }
}
