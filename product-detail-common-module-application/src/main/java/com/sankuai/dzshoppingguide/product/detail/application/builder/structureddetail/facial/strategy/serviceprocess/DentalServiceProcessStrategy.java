package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.dto.DentalProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.ServiceProcessUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/18 16:48
 */
@Component
@Slf4j
public class DentalServiceProcessStrategy extends AbstractSubModuleBuildStrategy {
    @Override
    public String getSubModuleName() {
        return SubModuleKey.SERVICE_PROCESS;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        return categoryId == SecondCategoryEnum.ORAL_DENTAL.getSecondCategoryId();
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = doBuild(context);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        // 增加分隔符
        DealDetailStructuredDetailVO separator = DealDetailStructuredUtils.buildLimiter();
        result.add(separator);
        return result;
    }

    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return Collections.emptyList();
        }
        // 服务流程信息
        List<String> servicesProcessStr = productAttr.getSkuAttrValues("serviceProcess");
        List<DentalProcessDTO> processDTOS = ServiceProcessUtils.parseDentalProcess(servicesProcessStr);
        if (CollectionUtils.isEmpty(processDTOS)) {
            return Collections.emptyList();
        }

        // 元素构建
        List<DealDetailStructuredDetailVO> result = processDTOS.stream().filter(Objects::nonNull).map(this::buildElement).filter(Objects::nonNull).collect(Collectors.toList());

        // 标题构建
        String totalDuration = getTotalDuration(processDTOS);
        if (CollectionUtils.isNotEmpty(result)) {
            DealDetailStructuredUtils.buildServiceProcessTitle(totalDuration).ifPresent(item -> result.add(0,item));
        }

        // 设置order
        ServiceProcessUtils.setOrder(result);

        // 设置底部的注释
        DealDetailStructuredUtils.buildAnnotation().ifPresent(result::add);

        // popupdata设置
        getPopupData(processDTOS).ifPresent(result::add);

        return result;
    }

    private String getTotalDuration(List<DentalProcessDTO> processDTOS) {
        if (CollectionUtils.isEmpty(processDTOS)) {
            return StringUtils.EMPTY;
        }
        // 要判定ServiceDurationUnits2的取值是否一致
        String serviceDurationUnits2 = processDTOS.get(0).getServiceDurationUnits2();
        if (processDTOS.stream().anyMatch(item -> !Objects.equals(item.getServiceDurationUnits2(), serviceDurationUnits2))) {
            return StringUtils.EMPTY;
        }
        int total = 0;
        for (DentalProcessDTO processDTO : processDTOS) {
            if (processDTO == null) {
                continue;
            }
            float count = 0f;
            int singleDuration = 0;
            if (StringUtils.isNotBlank(processDTO.getDuration()) && NumberUtils.isCreatable(processDTO.getDuration())) {
                singleDuration += Integer.parseInt(processDTO.getDuration());
                count++;
            }
            if (StringUtils.isNotBlank(processDTO.getDurationRangeMax()) && NumberUtils.isCreatable(processDTO.getDurationRangeMax())) {
                singleDuration += Integer.parseInt(processDTO.getDurationRangeMax());
                count++;
            }
            if (count > 0) {
                singleDuration = Math.round(singleDuration / count);
            }
            total += singleDuration;
        }
        return total + serviceDurationUnits2;
    }

    private DealDetailStructuredDetailVO buildElement(DentalProcessDTO processDTO) {
        if (processDTO == null || StringUtils.isBlank(processDTO.getProcessName())) {
            return null;
        }
        if (StringUtils.isBlank(processDTO.getServiceDurationUnits2())) {
            processDTO.setServiceDurationUnits2("分钟");
        }
        String durationValue = "";
        if (StringUtils.isNotBlank(processDTO.getDuration()) && StringUtils.isNotBlank(processDTO.getDurationRangeMax())) {
            if (Objects.equals(processDTO.getDuration(), processDTO.getDurationRangeMax())) {
                durationValue = processDTO.getDuration();
            } else {
                durationValue = processDTO.getDuration() + "-" + processDTO.getDurationRangeMax();
            }
        } else if (StringUtils.isNotBlank(processDTO.getDuration())) {
            durationValue = processDTO.getDuration();
        } else if (StringUtils.isNotBlank(processDTO.getDurationRangeMax())) {
            durationValue = processDTO.getDurationRangeMax();
        }
        if (StringUtils.isNotBlank(durationValue)) {
            durationValue = durationValue + processDTO.getServiceDurationUnits2();
        }

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder elementBuilder = DealDetailStructuredDetailVO.builder();
        return elementBuilder.type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .title(processDTO.getProcessName()).detail(durationValue).build();
    }

    private Optional<DealDetailStructuredDetailVO> getPopupData(List<DentalProcessDTO> processDTOS) {
        if (CollectionUtils.isEmpty(processDTOS)) {
            return Optional.empty();
        }
        List<DealDetailStructuredDetailVO> popupDataContents = processDTOS.stream().filter(Objects::nonNull).map(this::buildPopupData).flatMap(Collection::stream).collect(Collectors.toList());
        String popupDataContent = JSON.toJSONString(popupDataContents);
        DealDetailStructuredDetailVO popupData = DealDetailStructuredDetailVO.builder().title("服务流程").content(popupDataContent).type(ViewComponentTypeEnum.POPUP_STRUCTURED.getType()).build();

        return Optional.of(DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.SERVICE_PROCESS.getType()).content("查看流程详情").popupData(JSON.toJSONString(popupData)).build());
    }

    private List<DealDetailStructuredDetailVO> buildPopupData(DentalProcessDTO processDTO) {
        String processName = processDTO.getProcessName();
        String serviceDescription = processDTO.getService_description();

        if (StringUtils.isBlank(processName)) {
            return Collections.emptyList();
        }

        DealDetailStructuredDetailVO titleVO = DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.GUARANTEE_TITLE.getType()).title(processName).backgroundColor("#f6f6f6").endBackgroundColor("#fdfdfd").build();

        if (StringUtils.isBlank(serviceDescription)) {
            return Lists.newArrayList(titleVO);
        }
        DealDetailStructuredDetailVO content = DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .content(serviceDescription).contentColor("#888").build();

        return Lists.newArrayList(titleVO,content);
    }
}
