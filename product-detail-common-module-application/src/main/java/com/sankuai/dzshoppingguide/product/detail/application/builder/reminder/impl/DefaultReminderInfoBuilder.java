package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 13:05
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductCategoryFetcher.class,ProductBaseInfoFetcher.class, ProductAttrFetcher.class}
)
@Slf4j
public class DefaultReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }
        List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        Integer secondCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        FetcherResultDTO fetcherResultDTO = FetcherResultDTO.builder().productSecondCategoryId(secondCategoryId).productBaseInfo(baseInfo).productAttr(productAttr).build();
        ReminderInfoUtils.buildReminderInfo(AvailableTimeHelper.getAvailableTime(fetcherResultDTO), ReminderInfoUtils.fromOldDetail(request)).ifPresent(contents::add);
        ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(baseInfo), ReminderInfoUtils.fromOldDetail(request)).ifPresent(contents::add);
        return baseReminderInfo;
    }
}
