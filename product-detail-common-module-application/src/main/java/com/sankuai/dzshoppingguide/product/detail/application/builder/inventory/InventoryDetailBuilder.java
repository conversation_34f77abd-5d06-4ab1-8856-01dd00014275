package com.sankuai.dzshoppingguide.product.detail.application.builder.inventory;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructContentModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructModel;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand.BrandInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand.BrandInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash.CpvAttrConfigService;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryModuleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@Builder(
        moduleKey = ModuleKeyConstants.INVENTORY_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductCategoryFetcher.class, ProductAttrFetcher.class, BrandInfoFetcher.class,AdditionInfoFetcher.class}
)
@Slf4j
public class InventoryDetailBuilder extends BaseBuilder<InventoryModuleVO> {
    private static final String RICH_TEXT = "richtext";

    @Autowired
    private CpvAttrConfigService cpvExtraAttrConfigService;

    private String brandName = "";
    private int secondCategoryId;
    private String additionInfo;

    @Override
    public InventoryModuleVO doBuild() {
        Optional<ProductAttr> productAttrOpt = getDependencyResult(ProductAttrFetcher.class, ProductAttr.class);
        Optional<Integer> categoryIdOpt = getDependencyResult(ProductCategoryFetcher.class, ProductCategory.class)
                .map(ProductCategory::getProductSecondCategoryId);
        Optional<BrandInfo> brandInfoOpt = getDependencyResult(BrandInfoFetcher.class, BrandInfo.class);
        brandInfoOpt.ifPresent(brandInfo -> brandName = brandInfo.getBrandName());

        Optional<AdditionInfoResult> additionInfoResultOpt = getDependencyResult(AdditionInfoFetcher.class,AdditionInfoResult.class);
        additionInfoResultOpt.ifPresent(additionInfoResult -> additionInfo = getDesc(additionInfoResult.getAdditionInfo()));

        if (!productAttrOpt.isPresent() || !categoryIdOpt.isPresent() || categoryIdOpt.get() <= 0) {
            return null;
        }
        secondCategoryId = categoryIdOpt.get();
        Set<InventoryInfoDTO> productExtraAttrs = cpvExtraAttrConfigService.getProductExtraAttrDTOs(secondCategoryId);
        if (CollectionUtils.isEmpty(productExtraAttrs)) {
            return null;
        }
        
        ProductAttr productAttr = productAttrOpt.get();
        List<InventoryDetail> inventoryDetails = buildInventoryDetails(productAttr, productExtraAttrs);
        if (CollectionUtils.isEmpty(inventoryDetails)) {
            return null;
        }

        InventoryModuleVO vo = new InventoryModuleVO();
        vo.setInventoryDetails(inventoryDetails);
        vo.setRichText(buildRichText(inventoryDetails));
        return vo;
    
    }

    private String getDesc(String additionInfoStr) {
        if (StringUtils.isEmpty(additionInfoStr)) {
            return StringUtils.EMPTY;
        }
        UniformStructModel uniformStruct = JsonCodec.decode(additionInfoStr, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        if ( CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        UniformStructContentModel struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals(RICH_TEXT)).findFirst().orElse(null);
        if (struct == null || struct.getData() == null) {
            return null;
        }
        return struct.getData().toString();
    }

    private String buildRichText(List<InventoryDetail> inventoryDetails) {
        if (CollectionUtils.isEmpty(inventoryDetails)) {
            return "";
        }

        StringBuilder html = new StringBuilder();
        html.append("<div>");
        html.append("<div class=\"detail-tit\"></div>");
        html.append("<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" class=\"detail-table\">");
        html.append("<thead>");
        html.append("<tr>");
        html.append("<th width=\"50%\">名称</th>");
        html.append("<th width=\"25%\">值</th>");
        html.append("</tr>");
        html.append("</thead>");
        html.append("<tbody>");

        boolean hasAddedElements = false;
        
        for (InventoryDetail detail : inventoryDetails) {
            if (detail == null || StringUtils.isBlank(detail.getSubTitle()) || StringUtils.isBlank(detail.getTitle())) {
                continue;
            }
            html.append("<tr>");
            html.append("<td>").append(detail.getTitle()).append("</td>");
            html.append("<td class=\"tc\">").append(detail.getSubTitle()).append("</td>");
            html.append("</tr>");
            hasAddedElements = true;
        }

        if (!hasAddedElements) {
            return "";
        }

        html.append("</tbody>");
        html.append("</table>");
        if (StringUtils.isNotBlank(additionInfo)) {
            html.append("<div>").append(additionInfo).append("</div>");
        }
        html.append("</div>");

        return html.toString();
    }

    private List<InventoryDetail> buildInventoryDetails(ProductAttr productAttr, Set<InventoryInfoDTO> productExtraAttrs) {
        List<InventoryDetail> details = productExtraAttrs.stream()
                .filter(Objects::nonNull)
                .map(item -> buildInventoryDetail(productAttr, item))
                .filter(detail -> detail != null && StringUtils.isNotBlank(detail.getSubTitle()))
                .collect(Collectors.toList());

        // 排序优先级参考prd: https://km.sankuai.com/collabpage/2702523454
        Map<String, Integer> priorityMap = cpvExtraAttrConfigService.getCategoryPriority(secondCategoryId);

        details.sort((a, b) -> {
            int priorityA = priorityMap.getOrDefault(a.getTitle(), Integer.MAX_VALUE);
            int priorityB = priorityMap.getOrDefault(b.getTitle(), Integer.MAX_VALUE);
            return Integer.compare(priorityA, priorityB);
        });

        return details;
    }

    private InventoryDetail buildInventoryDetail(ProductAttr productAttr, InventoryInfoDTO item) {
        if (item == null || StringUtils.isBlank(item.getKey())) {
            return null;
        }

        String value = productAttr.getSkuAttrFirstValue(item.getKey());
        if (StringUtils.isBlank(value)) {
            return null;
        }

        InventoryDetail detail = new InventoryDetail();
        if (StringUtils.equals(Constants.BRAND_ID,item.getKey())) {
            // 替换品牌名称
            detail.setSubTitle(brandName);
        } else if (StringUtils.equals(Constants.ADDRESS,item.getKey())) {
            // 替换产地信息 ["中国","山东"]
            detail.setSubTitle(getAddress(productAttr.getSkuAttrValues(item.getKey())));
        } else {
            detail.setSubTitle(value);
        }
        detail.setTitle(item.getName());
        return detail;
    }
    
    public String getAddress(List<String> values) {
        try {
            if (CollectionUtils.isEmpty(values)) {
                // 后面有校验,空值的元素会被过滤掉
                return StringUtils.EMPTY;
            }

            return values.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("-"));
        } catch (Exception e) {
            log.error("getAddress error,values:{}",JsonCodec.encode(values),e);
        }
        return StringUtils.EMPTY;
    }
}
