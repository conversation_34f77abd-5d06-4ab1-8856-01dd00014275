package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.BathCPVConstant.*;

/**
 * <AUTHOR>
 * @date 2025/5/6 17:22
 */
@Component
@Slf4j
public class ServiceFacilityStyle0 implements Style<DealDetailStructuredDetailVO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        if (Objects.isNull(productAttr)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> serviceFacilities = Lists.newArrayList();

        // 桑拿汗蒸
        List<DealDetailStructuredDetailVO> teaAndFruitItems = buildCommonFacility(productAttr, SAUNA, "桑拿汗蒸");
        serviceFacilities.addAll(teaAndFruitItems);

        // 汤池
        List<DealDetailStructuredDetailVO> footSoakItems = buildCommonFacility(productAttr, BATHING_POOL, "汤池");
        serviceFacilities.addAll(footSoakItems);

        // 玩乐设施
        List<DealDetailStructuredDetailVO> freeOil = buildCommonFacility(productAttr, LEISURE, "玩乐设施");
        serviceFacilities.addAll(freeOil);

        // 休息区域
        List<DealDetailStructuredDetailVO> hotCompressItems = buildCommonFacility(productAttr, REST_AREA, "休息区域");
        serviceFacilities.addAll(hotCompressItems);

        // 免费洗浴用品
        List<DealDetailStructuredDetailVO> specialTools = buildCommonFacility(productAttr, FREE_BATHING_SUPPLIES, "免费洗浴用品");
        serviceFacilities.addAll(specialTools);

        // 一次性卫生用品
        List<DealDetailStructuredDetailVO> massageItems = buildCommonFacility(productAttr, DISPOSABLE_MATERIALS, "一次性卫生用品");
        serviceFacilities.addAll(massageItems);

        // 洗护用品
        List<DealDetailStructuredDetailVO> specialMaterial = buildCommonFacility(productAttr, BATHING_SUPPLIES, "洗护用品");
        serviceFacilities.addAll(specialMaterial);


        // 过滤空值
        List<DealDetailStructuredDetailVO> facilities = serviceFacilities.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(facilities)) {
            DealDetailStructuredDetailVO moduleName = DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_16.getType())
                    .title("服务设施")
                    .build();
            facilities.add(0, moduleName);
        }
        return facilities;
    }

    private List<DealDetailStructuredDetailVO> buildCommonFacility(ProductAttr productAttr, String attrKey, String title) {
        if (StringUtils.isBlank(attrKey)) {
            return Lists.newArrayList();
        }
        List<String> skuAttrValues = productAttr.getSkuAttrValues(attrKey);
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Lists.newArrayList();
        }

        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(skuAttrValues)) {
            result.add(DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_18.getType())
                    .content(JSON.toJSONString(skuAttrValues))
                    .build());
        }

        if (CollectionUtils.isNotEmpty(result)) {
            result.add(0, DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_17.getType())
                    .title(title)
                    .build());
        }
        return result;
    }

}
