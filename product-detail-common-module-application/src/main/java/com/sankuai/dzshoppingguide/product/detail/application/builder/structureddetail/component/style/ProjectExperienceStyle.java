package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.GameRoomVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 20:04
 * @Description: 二级类目: 游戏厅 三级类目: 项目体验 155006
 */
@Component
public class ProjectExperienceStyle implements Style<GameRoomVO> {

    private static final String GAME_PROJECT = "GameProject";
    private static final String GAME_DURATION = "GameDuration";
    private static final String SPACE_TYPE = "SpaceType4";
    private static final String SPACE_MOST_CAPACITY = "SpaceMostCapacity";
    private static final String USER_COUNT = "UserCount3";
    private static final String ADULT_COUNT = "AdultPopulation";
    private static final String CHILD_COUNT = "ChildCount";
    private static final String OVERCOUNT_EXPLANATION = "OverpopulationExplanation";

    @Override
    public List<GameRoomVO> build(DealDetailBuildContext context) {
        return Collections.emptyList();
    }
}
