package com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/2/5 13:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StartFetcherReturnValue extends FetcherReturnValueDTO {
}
