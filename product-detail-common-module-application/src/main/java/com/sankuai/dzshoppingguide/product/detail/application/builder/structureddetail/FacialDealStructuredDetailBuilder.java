package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.AbstractDealDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.CategoryInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.CategoryInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShop;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.DoctorFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.DoctorReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.TechnicianUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.TechnicianUrlReturnValue;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                ProductCategoryFetcher.class,
                CategoryInfoFetcher.class,
                DoctorFetcher.class,
                TechnicianUrlFetcher.class,
                ProductBestShopFetcher.class
        }
)
/**
 * 双眼 + 口腔 结构化详情页团单模块
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/4/14 16:26
 */
public class FacialDealStructuredDetailBuilder extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> implements InitializingBean, ApplicationContextAware {

    private static final Map<String, AbstractDealDetailBuilder> BUILDER_MAP = Maps.newHashMap();

    private ApplicationContext appContext;

    private static final String FACIAL_BUILDER_MODULE = "facial.builder.module.config";

    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductServiceProject productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        CategoryInfo categoryInfo = getDependencyResult(CategoryInfoFetcher.class);
        DoctorReturnValue doctorReturnValue = getDependencyResult(DoctorFetcher.class);
        TechnicianUrlReturnValue technicianUrlReturnValue = getDependencyResult(TechnicianUrlFetcher.class);
        Long availableShopCount = getDependencyResult(ProductBestShopFetcher.class, ProductBestShop.class)
                .map(ProductBestShop::getTotalCount)
                .orElse(0L);
        DealDetailBuildContext context = DealDetailBuildContext.builder()
                .productAttr(productAttr)
                .productServiceProject(productServiceProject)
                .productCategory(productCategory)
                .categoryInfo(categoryInfo)
                .doctorReturnValue(doctorReturnValue)
                .technicianUrlReturnValue(technicianUrlReturnValue)
                .request(request)
                .availableShopCount(availableShopCount)
                .build();

        List<AbstractDealDetailBuilder> necessaryBuilders = getNecessaryBuilders(productCategory);
        if (CollectionUtils.isEmpty(necessaryBuilders)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = necessaryBuilders.stream()
                .map(builder -> executeBuilder(builder, context))
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        ModuleDetailStructuredDetailVO moduleDetailStructuredDetailVO = new ModuleDetailStructuredDetailVO();
        moduleDetailStructuredDetailVO.setDealDetails(result);
        return moduleDetailStructuredDetailVO;
    }

    private List<DealDetailStructuredDetailVO> executeBuilder(AbstractDealDetailBuilder detailBuilder, DealDetailBuildContext context) {
        try {
            return detailBuilder.doBuild(context);
        } catch (Exception e) {
            log.error("builder failed", e);
        }
        return Lists.newArrayList();
    }

    private List<AbstractDealDetailBuilder> getNecessaryBuilders(ProductCategory productCategory) {
        if (productCategory == null) {
            return Collections.emptyList();
        }
        BuilderConfig builderConfig = Lion.getBean(MdpContextUtils.getAppKey(), FACIAL_BUILDER_MODULE, BuilderConfig.class);
        if (builderConfig == null || MapUtils.isEmpty(builderConfig.getBuilder())) {
            return Collections.emptyList();
        }
        List<String> identifyKeys = builderConfig.getBuilder().get(productCategory.getProductSecondCategoryId());
        return identifyKeys.stream().filter(BUILDER_MAP::containsKey).map(BUILDER_MAP::get).collect(Collectors.toList());
    }

    @Override
    public void afterPropertiesSet() {
        appContext.getBeansOfType(AbstractDealDetailBuilder.class)
                .values()
                .forEach(builder -> BUILDER_MAP.put(builder.getIdentifyKey(), builder));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.appContext = applicationContext;
    }

    @Data
    private static class BuilderConfig {
        private Map<Integer, List<String>> builder;
    }
}
