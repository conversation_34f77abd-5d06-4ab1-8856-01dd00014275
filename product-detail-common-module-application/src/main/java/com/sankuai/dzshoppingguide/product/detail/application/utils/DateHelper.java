package com.sankuai.dzshoppingguide.product.detail.application.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class DateHelper {
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 判断是否在时间范围内
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static boolean isCurrentDateInRange(String startDateStr, String endDateStr) {
        try {
            //使用ISO日期格式, 解析输入的日期字符串
            LocalDate startDate = convertToLocalDate(parseDate(startDateStr));
            LocalDate endDate = convertToLocalDate(parseDate(endDateStr));

            // 获取当前日期
            LocalDate currentDate = LocalDate.now();

            // 检查当前日期是否在输入的日期区间内
            return !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);
        } catch (Exception e) {
            // 如果输入的日期格式不正确，抛出异常或返回false
            log.error("Invalid date format. Please use ISO format (yyyy-MM-dd), startDateStr={}, endDateStr={}", startDateStr, endDateStr, e);
            return false;
        }
    }

    /**
     * 将字符串转换为时间，兼容 yyyy-MM-dd 和 yyyy-MM-dd HH:mm:ss 格式
     *
     * @param dateStr 日期字符串
     * @return 转换后的 Date 对象
     */
    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 先尝试完整日期时间格式
        Date date = parseDateWithPattern(dateStr, DATE_TIME_PATTERN);
        if (date != null) {
            return date;
        }

        // 再尝试纯日期格式
        return parseDateWithPattern(dateStr, DATE_PATTERN);
    }

    /**
     * 使用指定格式解析日期字符串
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return 解析后的Date对象，解析失败返回null
     */
    private static Date parseDateWithPattern(String dateStr, String pattern) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("日期格式解析失败, dateStr={}, pattern={}, error={}",
                    dateStr, pattern, e.getMessage());
            return null;
        }
    }

    /**
     * 将 java.util.Date 转换为 java.time.LocalDate
     *
     * @param date 需要转换的 Date 对象
     * @return 转换后的 LocalDate 对象
     */
    public static LocalDate convertToLocalDate(Date date) {
        // 1. 将 Date 转换为 Instant
        Instant instant = date.toInstant();
        // 2. 使用默认时区将 Instant 转换为 ZonedDateTime
        // 3. 从 ZonedDateTime 中提取 LocalDate
        return instant.atZone(ZoneId.systemDefault()).toLocalDate();
    }
}
