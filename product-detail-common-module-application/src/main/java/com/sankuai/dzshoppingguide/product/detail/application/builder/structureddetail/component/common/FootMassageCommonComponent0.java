package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum.*;
import static com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant.*;

/**
 * <AUTHOR>
 * @date 2025/5/9 15:37
 */
@Component
public class FootMassageCommonComponent0<T> implements CommonComponent0<T> {

    /**
     * 根据不同的按摩类型构建足疗标题
     *
     * @param productCategory 产品分类对象
     * @param productAttr     产品属性对象
     * @return 标题详情对象
     */
    public Optional<DealDetailStructuredDetailVO> buildTitleForFootMassage(ProductCategory productCategory, ProductAttr productAttr) {
        int thirdCategoryId = getThirdCategoryId(productCategory);
        MassageThirdCategoryEnum thirdCategoryEnum = MassageThirdCategoryEnum.getByCategoryId(thirdCategoryId);

        // 根据不同类型构建标题
        String serviceTitle = buildServiceTitleByCategory(thirdCategoryEnum, productAttr);
        // 服务时长
        String serviceDuration = buildServiceDuration(productAttr);
        if (StringUtils.isBlank(serviceTitle)) {
            return Optional.empty();
        }

        return Optional.of(DealDetailStructuredDetailVO.builder()
                .title(serviceTitle)
                .content(serviceDuration)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .build());
    }

    private String buildServiceDuration(ProductAttr productAttr) {
        String duration = getProductAttrByName(productAttr, SERVICE_DURATION);
        return StringUtils.isNotBlank(duration) ? duration + "分钟" : StringUtils.EMPTY;
    }

    /**
     * 根据按摩类型构建服务标题
     *
     * @param categoryEnum 按摩类型枚举
     * @param productAttr  产品属性对象
     * @return 服务标题
     */
    private String buildServiceTitleByCategory(MassageThirdCategoryEnum categoryEnum, ProductAttr productAttr) {
        String serviceTechnique = getProductAttrByName(productAttr, SERVICE_TECHNIQUE);
        if (categoryEnum == null) {
            return StringUtils.EMPTY;
        }
        switch (categoryEnum) {
            case GUA_SHA:
                return GUA_SHA.getCategoryName();
            case SCALP_MASSAGE:
                return String.format("%s %s", SCALP_MASSAGE.getCategoryName(), serviceTechnique);
            case FOOT_MASSAGE:
                return FOOT_MASSAGE.getCategoryName();
            case FOOT_MANAGEMENT:
                return getProductAttrByName(productAttr, SERVICE_ITEMS_5);
            case WOMEN_CARE:
                return WOMEN_CARE.getCategoryName();
            case POSTURE_CORRECTION:
                return getProductAttrByName(productAttr, SERVICE_ITEMS_8);
            case MASSAGE:
            case OIL_SPA:
            case EAR_PICKING:
            case MOXIBUSTION:
            case MASSAGE_BONE_SETTING:
                return serviceTechnique;
            case CUPPING:
                return String.format("%s(%s)", CUPPING.getCategoryName(), serviceTechnique);
            case PEDIATRIC_MASSAGE:
                return PEDIATRIC_MASSAGE.getCategoryName();
            case SPORTS_REHABILITATION:
                return getProductAttrByName(productAttr, SPORTS_REHABILITATION_SERVICE_ITEMS);
            default:
                return StringUtils.EMPTY;
        }
    }

    /**
     * 构建运动复健标题
     *
     * @param duration     服务时长
     * @param serviceItems 服务项目
     * @return 运动复健标题
     */
    private String buildSportsRehabilitationTitle(String serviceItems, String duration) {
        StringBuilder builder = new StringBuilder();
        if (StringUtils.isNotBlank(serviceItems)) {
            builder.append(serviceItems);
        }
        if (StringUtils.isNotBlank(duration)) {
            if (builder.length() > 0) {
                builder.append(" ");
            }
            builder.append(duration);
        }
        return builder.toString();
    }

    /**
     * 构建小儿推拿标题
     *
     * @param duration 服务时长
     * @return 小儿推拿标题
     */
    private String buildPediatricMassageTitle(String duration) {
        StringBuilder builder = new StringBuilder("小儿推拿");
        if (StringUtils.isNotBlank(duration)) {
            if (builder.length() > 0) {
                builder.append(" ");
            }
            builder.append(duration);
        }
        return builder.toString();
    }

    /**
     * 构建足疗标题
     *
     * @param duration 服务时长
     * @return 足疗标题
     */
    private String buildFootMassageTitle(String duration) {
        StringBuilder builder = new StringBuilder("足疗");
        if (StringUtils.isNotBlank(duration)) {
            builder.append(" ").append(duration).append("分钟");
        }
        return builder.toString();
    }

    private String buildFootMassageTitle(String title, String duration) {
        StringBuilder builder = new StringBuilder(title);
        if (StringUtils.isNotBlank(duration)) {
            builder.append(" ").append(duration).append("分钟");
        }
        return builder.toString();
    }


    /**
     * 构建拔罐标题
     *
     * @param serviceTechnique 服务手法
     * @param duration         服务时长
     * @return 拔罐标题
     */
    private String buildCuppingTitle(String serviceTechnique, String duration) {
        StringBuilder builder = new StringBuilder("拔罐");
        if (StringUtils.isNotBlank(serviceTechnique)) {
            builder.append(String.format("（%s）", serviceTechnique));
        }

        if (StringUtils.isNotBlank(duration)) {
            if (builder.length() > 0) {
                builder.append(" ");
            }
            builder.append(duration).append("分钟");
        }

        return builder.toString();
    }

    /**
     * 根据属性名获取产品属性值
     *
     * @param productAttr 产品属性对象
     * @param attrName    属性名
     * @return 属性值，如果属性名为空或产品属性为空则返回空字符串
     */
    private String getProductAttrByName(ProductAttr productAttr, String attrName) {
        if (StringUtils.isBlank(attrName)) {
            return StringUtils.EMPTY;
        }

        return Optional.ofNullable(productAttr)
                .map(item -> item.getSkuAttrFirstValue(attrName))
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 根据属性名获取产品属性值列表
     *
     * @param productAttr 产品属性对象
     * @param attrName    属性名
     * @return 属性值列表，如果属性名为空则返回包含空字符串的列表，如果产品属性为空则返回空字符串
     */
    private List<String> getProductAttrsByName(ProductAttr productAttr, String attrName) {
        if (StringUtils.isBlank(attrName)) {
            return Lists.newArrayList();
        }

        return Optional.ofNullable(productAttr)
                .map(item -> item.getSkuAttrValues(attrName))
                .orElse(Lists.newArrayList());
    }

    /**
     * 获取产品的三级分类 ID
     *
     * @param productCategory 产品分类对象
     * @return 三级分类 ID，如果产品分类为空则返回 0
     */
    private int getThirdCategoryId(ProductCategory productCategory) {
        return Optional.ofNullable(productCategory)
                .map(ProductCategory::getProductThirdCategoryId)
                .orElse(0);
    }

    /**
     * 构建服务部位信息
     *
     * @param productCategory 产品分类对象
     * @param productAttr     产品属性对象
     * @return 服务部位详情对象
     */
    public Optional<DealDetailStructuredDetailVO> buildServicePosition(ProductCategory productCategory, ProductAttr productAttr) {
        MassageThirdCategoryEnum thirdCategoryEnum = MassageThirdCategoryEnum.getByCategoryId(getThirdCategoryId(productCategory));
        // 先处理特殊场景
        switch (thirdCategoryEnum) {
            case SCALP_MASSAGE:
            case MASSAGE_BONE_SETTING:
            case PEDIATRIC_MASSAGE:
            case WOMEN_CARE:
            case POSTURE_CORRECTION:
            case SPORTS_REHABILITATION:
                return handleBasicServicePosition(productAttr);
        }
        // 获取基础信息
        String bodyRange = getProductAttrByName(productAttr, BODY_REGION);
        // if (StringUtils.isBlank(bodyRange)) {
        //     return Optional.empty();
        // }

        // 处理全身服务的情况
        if (StringUtils.equals("全身", bodyRange)) {
            return buildServicePositionDetail("服务部位", "全身");
        }

        // 根据服务类型处理不同场景
        return handleServicePositionByType(thirdCategoryEnum, productAttr, bodyRange);
    }

    /**
     * 根据按摩类型处理服务部位
     *
     * @param thirdCategoryEnum 按摩类型枚举
     * @param productAttr       产品属性对象
     * @param bodyRange         身体部位范围
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> handleServicePositionByType(MassageThirdCategoryEnum thirdCategoryEnum,
                                                                               ProductAttr productAttr, String bodyRange) {
        if (thirdCategoryEnum == null) {
            return Optional.empty();
        }

        switch (thirdCategoryEnum) {
            case FOOT_MASSAGE:
            case MASSAGE:
            case OIL_SPA:
            case EAR_PICKING:
                return handleBasicServicePosition(productAttr);
            case MOXIBUSTION:
            case CUPPING:
            case GUA_SHA:
                return handleMultiSiteServicePosition(productAttr, bodyRange);
            default:
                return Optional.empty();
        }
    }

    /**
     * 处理基础服务部位
     *
     * @param productAttr 产品属性对象
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> handleBasicServicePosition(ProductAttr productAttr) {
        List<String> serviceBodyRange = getProductAttrsByName(productAttr, SERVICE_BODY_RANGE);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(serviceBodyRange)) {
            return Optional.empty();
        }

        // 如果有全身 只展示全身
        if (CollectionUtils.containsAny(serviceBodyRange, Arrays.asList("全身"))) {
            return buildServicePositionDetail("服务部位", "全身");
        }

        String joinedServiceBodyRange = serviceBodyRange.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("服务部位", joinedServiceBodyRange);
    }

    /**
     * 处理多部位服务部位
     *
     * @param productAttr 产品属性对象
     * @param bodyRange   身体部位范围
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> handleMultiSiteServicePosition(ProductAttr productAttr, String bodyRange) {
        if (StringUtils.equals("多个部位任选", bodyRange)) {
            return buildMultiSiteSelectionContent(productAttr);
        } else {
            return buildSingleSiteContent(productAttr);
        }
    }

    /**
     * 构建多部位选择内容
     *
     * @param productAttr 产品属性对象
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> buildMultiSiteSelectionContent(ProductAttr productAttr) {
        String multiSiteSelection = getProductAttrByName(productAttr, MULTI_SITE_SELECTION_QUANTITY);
        List<String> serviceBodyRange = getProductAttrsByName(productAttr, SERVICE_BODY_RANGE);

        StringBuilder contentBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(multiSiteSelection)) {
            contentBuilder.append(String.format("以下部位可选%s个", multiSiteSelection));
        }

        if (CollectionUtils.isNotEmpty(serviceBodyRange)) {
            if (contentBuilder.length() > 0) {
                contentBuilder.append("，");
            }
            String joinedServiceBodyRange = serviceBodyRange.stream()
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("、"));
            contentBuilder.append(joinedServiceBodyRange);
        }

        return buildServicePositionDetail("服务部位", contentBuilder.toString());
    }

    /**
     * 构建单部位内容
     *
     * @param productAttr 产品属性对象
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> buildSingleSiteContent(ProductAttr productAttr) {
        List<String> serviceBodyRange = getProductAttrsByName(productAttr, SERVICE_BODY_RANGE);

        if (CollectionUtils.isEmpty(serviceBodyRange)) {
            return Optional.empty();
        }

        String content = serviceBodyRange.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("服务部位", content);
    }

    /**
     * 构建服务部位详情对象
     *
     * @param content 内容
     * @return 服务部位详情对象
     */
    private Optional<DealDetailStructuredDetailVO> buildServicePositionDetail(String title, String content) {
        if (StringUtils.isBlank(content)) {
            return Optional.empty();
        }

        return Optional.of(DealDetailStructuredDetailVO.builder()
                .title(title)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_2.getType())
                .content(content)
                .build());
    }

    /**
     * 构建项目功效详情对象
     *
     * @param productAttr 产品属性对象
     * @return 项目功效详情对象
     */
    public Optional<DealDetailStructuredDetailVO> buildProjectEfficacy(ProductAttr productAttr) {
        List<String> projectEfficacy = getProductAttrsByName(productAttr, PROJECT_EFFICACY);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(projectEfficacy)) {
            return Optional.empty();
        }

        String projectEfficacyStr = projectEfficacy.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));

        return buildServicePositionDetail("适用人群", projectEfficacyStr);
    }

    public Optional<DealDetailStructuredDetailVO> buildAdditionalService(ProductCategory productCategory, ProductAttr productAttr) {
        MassageThirdCategoryEnum thirdCategoryEnum = MassageThirdCategoryEnum.getByCategoryId(getThirdCategoryId(productCategory));
        // 先处理特殊场景
        List<String> additionalService = null;
        switch (thirdCategoryEnum) {
            case SCALP_MASSAGE:
                additionalService = getProductAttrsByName(productAttr, SCALP_MASSAGE_ADDITIONAL_SERVICE);
                break;
            case OIL_SPA:
                additionalService = getProductAttrsByName(productAttr, ADDITIONAL_SERVICES);
                break;
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(additionalService)) {
            return Optional.empty();
        }

        String additionalServiceStr = additionalService.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("附加服务", additionalServiceStr);
    }

    public Optional<DealDetailStructuredDetailVO> buildCorrespondingSymptoms(ProductAttr productAttr) {
        List<String> correspondingSymptoms = getProductAttrsByName(productAttr, CORRESPONDING_SYMPTOMS);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(correspondingSymptoms)) {
            return Optional.empty();
        }

        String correspondingSymptomsStr = correspondingSymptoms.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("对应症状", correspondingSymptomsStr);
    }

    /**
     * 适用人群
     *
     * @param productAttr
     * @return
     */
    public Optional<DealDetailStructuredDetailVO> buildApplicablePopulation(ProductCategory productCategory, ProductAttr productAttr) {
        MassageThirdCategoryEnum thirdCategoryEnum = MassageThirdCategoryEnum.getByCategoryId(getThirdCategoryId(productCategory));
        // 先处理特殊场景
        List<String> applicablePopulation = null;
        switch (thirdCategoryEnum) {
            case WOMEN_CARE:
            case POSTURE_CORRECTION:
            case GUA_SHA:
            case SCALP_MASSAGE:
            case MOXIBUSTION:
            case CUPPING:
                applicablePopulation = getProductAttrsByName(productAttr, APPLICABLE_POPULATION_8);
                break;
            case MASSAGE_BONE_SETTING:
                applicablePopulation = getProductAttrsByName(productAttr, APPLICABLE_POPULATION_10);
                break;
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(applicablePopulation)) {
            return Optional.empty();
        }

        String inapplicablePopulationStr = applicablePopulation.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("适用人群", inapplicablePopulationStr);
    }

    public Optional<DealDetailStructuredDetailVO> buildInapplicablePopulation(ProductAttr productAttr) {
        List<String> inapplicablePopulation = getProductAttrsByName(productAttr, INAPPLICABLE_POPULATION);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(inapplicablePopulation)) {
            return Optional.empty();
        }

        String inapplicablePopulationStr = inapplicablePopulation.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("不适用人群", inapplicablePopulationStr);
    }

    /**
     * 构造服务项目
     *
     * @param productCategory
     * @param productAttr
     * @return
     */
    public Optional<DealDetailStructuredDetailVO> buildServiceItems(ProductCategory productCategory, ProductAttr productAttr) {
        // 获取基础信息
        List<String> serviceItems = getProductAttrsByName(productAttr, ServiceItems7);
        if (CollectionUtils.isEmpty(serviceItems)) {
            return Optional.empty();
        }

        // 根据服务类型处理不同场景
        String joinedServiceBodyRange = serviceItems.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("、"));
        return buildServicePositionDetail("服务项目", joinedServiceBodyRange);
    }

    /**
     * 实现 CommonComponent 接口的 build 方法
     *
     * @param vo 输入对象
     * @return 空列表，因为该组件不直接通过此方法构建对象
     */
    @Override
    public List<T> build(T vo) {
        return Collections.emptyList();
    }
}
