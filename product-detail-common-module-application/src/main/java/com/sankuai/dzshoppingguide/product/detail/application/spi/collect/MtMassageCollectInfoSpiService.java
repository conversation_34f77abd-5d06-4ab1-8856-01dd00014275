package com.sankuai.dzshoppingguide.product.detail.application.spi.collect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.util.JsonUtils;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.meituan.service.mobile.collection.detail.*;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AtomServiceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReservePriceDTO;
import com.sankuai.dztheme.massagebook.theme.res.ReserveProductDTO;
import com.sankuai.dztheme.massagebook.theme.res.ReserveProductShopDTO;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/12
 */
@Slf4j
@MdpPigeonServer(url = "com.sankuai.dzshoppingguide.product.detail.application.spi.collect.MtMassageCollectInfoSpiService")
public class MtMassageCollectInfoSpiService implements CollectionDetailService.Iface {
    private static final int COLL_TYPE = 1;

    private static final int UNIQUE_TYPE = 45;

    private static final String MT_RESERVE_KEY = "mt_reserve";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CollectionDtlInfoResponse getCollectionDtlInfos(CollectionDtlInfoRequest request) {
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getCollIds())) {
            return null;
        }

        List<DealGroupDTO> dealGroupDTOList = compositeAtomService.getProductBaseInfo(request.getCollIds(), true);
        if (CollectionUtils.isEmpty(dealGroupDTOList)) {
            return null;
        }
        Map<Long, Long> productIdShopIdMap = AtomServiceUtils.getProductId2ShopIdMap(dealGroupDTOList, true);
        if (MapUtils.isEmpty(productIdShopIdMap)) {
            return null;
        }

        List<Integer> productIds = productIdShopIdMap.keySet().stream().filter(Objects::nonNull).map(Long::intValue).collect(Collectors.toList());
        List<List<Integer>> allProductIds = buildAllProductIds(productIds);
        if (CollectionUtils.isEmpty(allProductIds)) {
            return null;
        }

        List<ReserveProductDTO> reserveProductList = Lists.newArrayList();
        if (Environment.isOfflineEnv()) {
            reserveProductList = JSON.parseObject("[{\"attrs\":[],\"futurePromos\":[],\"headPic\":\"http://p0.meituan.net/dpmerchantpic/ffd079e16f475723df441201264f4c974542.jpg\",\"name\":\"足疗预订2\",\"productId\":423627757,\"productItems\":[],\"productTags\":[],\"promos\":[],\"relatedTechs\":[],\"reservePrice\":{\"discount\":0.84,\"marketPrice\":120.00,\"originalSalePrice\":100.00,\"salePrice\":90.00,\"salePriceDesc\":\"\"},\"sale\":{\"sale\":0},\"shops\":[{\"address\":\"蒲松北路10号\",\"categoryId\":0,\"distanceNum\":0,\"lat\":31.218157,\"lng\":121.375264,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=*********&latitude=31.218157&longitude=121.375264\",\"score\":0.0,\"shopIdAsLong\":*********,\"shopName\":\"足疗按摩\",\"shopPic\":\"https://p0.inf.test.sankuai.com/poifeedback/ba56c5b689750c5629dd4270511aac9e195970.jpg%401000w_1000h_0e_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\",\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********\",\"star\":0}]},{\"attrs\":[],\"futurePromos\":[],\"headPic\":\"http://p0.meituan.net/travelcube/8d83dec7b62564881adf7340ae06b29a3416372.jpg\",\"name\":\"商品展示优惠感知足浴1号\",\"productId\":*********,\"productItems\":[],\"productTags\":[],\"promos\":[],\"relatedTechs\":[],\"reservePrice\":{\"discount\":0.81,\"marketPrice\":99.00,\"originalSalePrice\":80.00,\"salePrice\":80.00,\"salePriceDesc\":\"\"},\"sale\":{\"sale\":0},\"shops\":[{\"address\":\"光大安石中心\",\"categoryId\":0,\"distanceNum\":0,\"lat\":31.257338,\"lng\":121.515529,\"mapUrl\":\"imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=*********&latitude=31.257338&longitude=121.515529\",\"score\":0.0,\"shopIdAsLong\":*********,\"shopName\":\"【商品展示】足疗优惠感知测试门店（不要绑定三方）\",\"shopPic\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/76bf6531ae4bf5e2ec43bcff6fdced6f2974897.png%401000w_1000h_0e_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20%7Cformat%3Djpeg\",\"shopUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=*********\",\"star\":0}]}]", new TypeReference<List<ReserveProductDTO>>(){});
        } else {
            List<CompletableFuture<ReserveQueryResponse>> massageBookResponseResult = buildProductResultCfList(allProductIds, request, productIdShopIdMap);
            reserveProductList = extractAllProductResult(massageBookResponseResult);
        }
        if (CollectionUtils.isEmpty(reserveProductList)) {
            return null;
        }
        Map<Integer, ReserveProductDTO> productId2GeneralProduct = reserveProductList.stream().collect(Collectors.toMap(ReserveProductDTO::getProductId, e -> e, (a, b) -> a));
        List<DealDetailInfo> dealDetailInfos = Lists.newArrayList();
        for (Integer productId : productIds) {
            ReserveProductDTO reserveProductDTO = productId2GeneralProduct.get(productId);
            if (reserveProductDTO == null) {
                continue;
            }
            dealDetailInfos.add(buildDealDetailInfo(reserveProductDTO, request.getVersion()));
        }

        CollectionDtlInfoResponse response = new CollectionDtlInfoResponse();
        CollectionDtlData data = new CollectionDtlData();
        data.setCollType(COLL_TYPE);
        data.setDealDetailInfos(dealDetailInfos);
        response.setData(data);
        response.setCode(1);
        try {
            log.info("MtMassageCollectInfoSpiService getCollectionDtlInfos response:{}", JsonUtils.toJson(response));
        } catch (IOException e) {
            log.error("MtMassageCollectInfoSpiService getCollectionDtlInfos json error", e);
            throw new RuntimeException(e);
        }
        return response;
    }

    private DealDetailInfo buildDealDetailInfo(ReserveProductDTO reserveProductDTO, String appVersion) {
        if (Objects.isNull(reserveProductDTO)) {
            return null;
        }
        DealDetailInfo dealDetailInfo = new DealDetailInfo();
        dealDetailInfo.setEndTime(3155731200000L);
        dealDetailInfo.setDealId(reserveProductDTO.getProductId());
        dealDetailInfo.setUniqueType(UNIQUE_TYPE);
        dealDetailInfo.setImgUrl(reserveProductDTO.getHeadPic());  //商品头图
        dealDetailInfo.setTitle(reserveProductDTO.getName());   //商品名称
        dealDetailInfo.setPrice(buildBasePrice(reserveProductDTO.getReservePrice()));
        dealDetailInfo.setValue(NumberUtils.toInt(buildMarketPrice(reserveProductDTO.getReservePrice())));  // todo 含义
        dealDetailInfo.setHistorySaleCount(reserveProductDTO.getSale() != null ? reserveProductDTO.getSale().getSaleTag() : "");   //销量
        dealDetailInfo.setSubTitle("免等位、提前退");

        if (CollectionUtils.isNotEmpty(reserveProductDTO.getShops())) {
            ReserveProductShopDTO reserveProductShopDTO = reserveProductDTO.getShops().get(0);
            dealDetailInfo.setMlls(buildMalls(reserveProductShopDTO));
            dealDetailInfo.setPoiName(reserveProductShopDTO.getShopName());  //门店
            // 门店详情页跳转链接
            String shopIdUrl = UrlHelper.getCollectedShopUrl(MT_RESERVE_KEY, String.valueOf(reserveProductShopDTO.getShopIdAsLong()));
            dealDetailInfo.setPoiUrl(shopIdUrl);
            dealDetailInfo.setBrandName(buildBrandName(reserveProductShopDTO));
            // 商品详情页跳转链接
            String productDetailUrl = UrlHelper.getCollectedProductDetailUrl(MT_RESERVE_KEY, String.valueOf(reserveProductDTO.getProductId()),
                    String.valueOf(reserveProductShopDTO.getShopIdAsLong()), appVersion);
            dealDetailInfo.setIUrl(productDetailUrl);

        } else {
            dealDetailInfo.setBrandName("");
            dealDetailInfo.setMlls("0,0");
            dealDetailInfo.setIUrl("");
        }

        return dealDetailInfo;
    }

    private double buildBasePrice(ReservePriceDTO reservePrice) {
        return Optional.ofNullable(reservePrice).filter(r -> Objects.nonNull(r.getSalePrice())).map(dto -> dto.getSalePrice().doubleValue()).orElse(0.0D);
    }

    private String buildMarketPrice(ReservePriceDTO reservePrice) {
        BigDecimal marketPrice = Optional.ofNullable(reservePrice)
                .map(ReservePriceDTO::getMarketPrice).orElse(null);
        if (Objects.isNull(marketPrice)) {
            return null;
        }
        return marketPrice.stripTrailingZeros().toPlainString();
    }

    private List<ReserveProductDTO> extractAllProductResult(List<CompletableFuture<ReserveQueryResponse>> generalProductResultCfList) {
        List<ReserveProductDTO> reserveProductList = Lists.newArrayList();
        for (CompletableFuture<ReserveQueryResponse> resultCf : generalProductResultCfList) {
            ReserveQueryResponse result = resultCf.join();
            if (result == null || CollectionUtils.isEmpty(result.getProducts())) {
                continue;
            }
            reserveProductList.addAll(result.getProducts());
        }
        return reserveProductList;
    }

    public static <U> CompletableFuture<List<U>> each(List<CompletableFuture<U>> futures) {
        CompletableFuture<List<U>> overallResult = new CompletableFuture<>();

        CompletableFuture
                .allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((noUsed, exception) -> {
                    if (exception != null) {
                        overallResult.completeExceptionally(exception);
                        return;
                    }
                    List<U> results = new ArrayList<>();
                    for (CompletableFuture<U> future : futures) {
                        results.add(future.join());
                    }
                    overallResult.complete(results);
                });
        return overallResult;
    }

    private List<List<Integer>> buildAllProductIds(List<Integer> allProducts) {
        if (CollectionUtils.isEmpty(allProducts)) {
            return Lists.newArrayList();
        }
        return Lists.partition(allProducts, 50);
    }

    private String buildBrandName(ReserveProductShopDTO reserveProductShopDTO) {
        if (StringUtils.isNotEmpty(reserveProductShopDTO.getBranchName())) {
            return reserveProductShopDTO.getBranchName();
        }

        if (StringUtils.isNotEmpty(reserveProductShopDTO.getAddress())) {
            return reserveProductShopDTO.getAddress();
        }

        return "";
    }

    private String buildMalls(ReserveProductShopDTO shop) {
        return String.format("%s,%s", shop.getLat(), shop.getLng());
    }

    private List<CompletableFuture<ReserveQueryResponse>> buildProductResultCfList(List<List<Integer>> productIdsList, CollectionDtlInfoRequest collectionDtlInfoRequest, Map<Long, Long> productIdShopIdMap) {
        List<CompletableFuture<ReserveQueryResponse>> cfList = Lists.newArrayList();
        for (List<Integer> productIds : productIdsList) {
            ReserveQueryRequest reserveQueryRequest = AtomServiceUtils.buildReserveQueryRequest(productIds, collectionDtlInfoRequest.getLat(), collectionDtlInfoRequest.getLng(), collectionDtlInfoRequest.getUserId(), productIdShopIdMap, true);
            CompletableFuture<ReserveQueryResponse> resultCf = compositeAtomService.queryReserveProduct(reserveQueryRequest);
            cfList.add(resultCf);
        }
        return cfList;
    }
}
