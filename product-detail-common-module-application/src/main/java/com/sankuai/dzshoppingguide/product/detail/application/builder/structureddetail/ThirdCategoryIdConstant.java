package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

public class ThirdCategoryIdConstant {
    // 眼镜类
    public static final int OPTICAL_EXAMINATION = 147001; // 验光
    public static final int CHILD_OPTICAL_EXAMINATION = 148000; // 儿童验光
    public static final int MYOPIA_GLASSES = 149000; // 近视配镜
    public static final int CHILD_GLASSES = 131013; // 儿童配镜
    public static final int LENS_ONLY = 148001; // 仅镜片
    public static final int PRESBYOPIA_GLASSES = 150000; // 老花眼镜
    public static final int CONTACT_LENS = 110011; // 隐形眼镜
    public static final int SUNGLASSES = 147002; // 太阳眼镜
    public static final int FRAME_ONLY = 150001; // 仅镜框
    public static final int MAINTENANCE = 148002; // 维修保养
    public static final int SMART_GLASSES = 149001; // 智能眼镜
    public static final int OTHER_GLASSES = 152000; // 眼镜（其他）

    // 眼科类
    public static final int CHILD_MEDICAL_OPTICAL = 147003; // 儿童医学验光
    public static final int CONTACT_LENS_EXAMINATION = 111000; // 角膜接触镜配镜检查
    public static final int ADULT_MEDICAL_OPTICAL = 154000; // 成人医学验光∂ç
    public static final int CHILD_REGULAR_GLASSES = 155000; // 儿童普通眼镜
    public static final int MEDICAL_GLASSES = 854; // 医学配镜
    public static final int DEFOCUS_GLASSES = 146001; // 离焦镜
    public static final int OK_GLASSES = 201; // OK镜
    public static final int DEFOCUS_SOFT_LENS = 117000; // 离焦软镜
    public static final int RGP_LENS = 114002; // RGP镜
    public static final int OTHER_CONTACT_LENS = 145001; // 其他接触镜
    public static final int VISION_TRAINING = 113001; // 视觉训练
    public static final int MYOPIA_PRE_SURGERY_EXAM = 198; // 近视术前检查
    public static final int SMILE_SURGERY = 856; // 全飞秒
    public static final int FEMTOSECOND_SURGERY = 855; // 飞秒
    public static final int EXCIMER_SURGERY = 111002; // 准分子
    public static final int CRYSTAL_SURGERY = 857; // 晶体手术
    public static final int DRY_EYE_EXAM = 112006; // 干眼检查
    public static final int DRY_EYE_LASER = 117001; // 干眼激光
    public static final int DRY_EYE_THERAPY = 146002; // 干眼理疗
    
    // 口腔类
    public static final int TEETH_CLEANING = 27; // 洗牙
    public static final int CHILD_TEETH_CLEANING = 157000; // 儿童洗牙
    public static final int TEETH_FILLING = 29; // 补牙
    public static final int CHILD_TEETH_FILLING = 845; // 儿童补牙
    public static final int TOOTH_EXTRACTION = 28; // 拔牙
    public static final int MILK_TOOTH_EXTRACTION = 843; // 乳牙拔除
    public static final int ORTHODONTICS = 207; // 矫正
    public static final int CROWN_RESTORATION = 149002; // 牙冠修复
    public static final int CHILD_CROWN_RESTORATION = 158001; // 儿童牙冠修复
    public static final int DENTURE = 151001; // 义齿/假牙
    public static final int VENEER_RESTORATION = 161000; // 牙贴面修复
    public static final int TEETH_WHITENING = 25; // 美白
    public static final int EARLY_ORTHODONTICS = 263; // 早期矫治
    public static final int FLUORIDE_TREATMENT = 841; // 涂氟
    public static final int PIT_AND_FISSURE_SEALANT = 842; // 窝沟封闭
    public static final int PERIODONTAL_TREATMENT = 677; // 牙周治疗
    public static final int ROOT_CANAL_TREATMENT = 159000; // 根管/根尖治疗
    public static final int CHILD_ROOT_CANAL_TREATMENT = 147004; // 儿童根管治疗
    public static final int LOOSE_TOOTH_FIXATION = 153001; // 松动牙固定
    public static final int TOOTH_DESENSITIZATION = 157001; // 牙齿脱敏治疗
    public static final int ORAL_CARE_PRODUCTS = 145003; // 口腔护理用品
    public static final int OTHER_ORAL_TREATMENT = 154002; // 其他口腔治疗
}
