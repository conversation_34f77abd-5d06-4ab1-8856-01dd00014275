package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FetcherResultEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/6 15:14
 */
@Slf4j
@Service
public class KTVAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(FetcherResultDTO fetcherResults) {
        try {
            ProductServiceProject productServiceProject = fetcherResults.getProductServiceProject();

            List<ServiceProjectAttrDTO> attrs = Optional.ofNullable(productServiceProject).map(ProductServiceProject::getServiceProject)
                    .map(DealGroupServiceProjectDTO::getMustGroups)
                    .orElse(Collections.emptyList()).stream().filter(Objects::nonNull)
                    .flatMap(group -> Optional.ofNullable(group.getGroups()).orElse(Collections.emptyList()).stream())
                    .filter(Objects::nonNull)
                    .flatMap(
                            project -> Optional.ofNullable(project.getAttrs()).orElse(Collections.emptyList()).stream())
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(attrs)) {
                return StringUtils.EMPTY;
            }
            String startTime = attrs.stream().filter(attr -> attr.getAttrName().equals("start_time")).findFirst()
                    .map(ServiceProjectAttrDTO::getAttrValue).orElse(null);
            String endTime = attrs.stream().filter(attr -> attr.getAttrName().equals("end_time")).findFirst()
                    .map(ServiceProjectAttrDTO::getAttrValue).orElse(null);
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return StringUtils.EMPTY;
            }
            // 这个24:00的时间非常地奇怪,对应的上单里面的次日00:00
            if ("00:00".equals(startTime) && "23:59".equals(endTime)
                    || "00:00".equals(startTime) && "24:00".equals(endTime)) {
                return AvailableTimeHelper.ALL_DAY;
            } else {
                return AvailableTimeHelper.PARTIAL_TIME;
            }
        } catch (Exception e) {
            log.error("KTVAvailableTimeStrategy error", e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.KTV_STRATEGY;
    }
}
