package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.DetailComponentKeyConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType1ViewComponent;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-05-30
 * @desc 复用自服务设施的标题
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Title,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "标题组件",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class TitleComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "标题")
    private String title;

    @Override
    protected ComponentVO doBuildVO(int recursionDepth) {
        BaseViewComponent viewComponent = FacilityType1ViewComponent.builder()
                .title(title)
                .build();
        return new BizComponentVO(viewComponent);
    }

}