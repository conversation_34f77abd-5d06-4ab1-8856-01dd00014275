// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.corpwx;
//
// import com.dianping.lion.client.Lion;
// import com.google.common.collect.Lists;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
// import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
// import com.sankuai.dz.srcm.flow.dto.FlowEntryWxMaterialRequest;
// import com.sankuai.dz.srcm.flow.dto.GeoDTO;
// import com.sankuai.dz.srcm.flow.enums.PageLocationType;
// import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
// import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
// import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
// import com.sankuai.sinai.data.api.dto.DpPoiDTO;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.util.List;
// import java.util.Optional;
// import java.util.concurrent.CompletableFuture;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/2/26 17:22
//  */
// @Fetcher(
//         previousLayerDependencies = {
//                 CommonModuleStarter.class,
//                 ShopIdMapperFetcher.class,
//                 ShopInfoFetcher.class,
//                 DealGroupIdMapperFetcher.class,
//                 CityIdMapperFetcher.class
//         }
// )
// @Slf4j
// public class CorpWxFetcher extends NormalFetcherContext<CorpWxResult> {
//     @Autowired
//     private CompositeAtomService compositeAtomService;
//     @Override
//     protected CompletableFuture<CorpWxResult> doFetch() {
//         if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
//             return CompletableFuture.completedFuture(null);
//         }
//         ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
//         ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
//         CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
//
//         int backPoiSecondCateGoryId = Optional.ofNullable(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getBackMainCategoryPath).orElse(Lists.newArrayList()).stream().filter(dpPoiBackCategoryDTO -> dpPoiBackCategoryDTO.getCategoryLevel() == 2)
//                 .findFirst().map(DpPoiBackCategoryDTO::getCategoryId).orElse(0);
//
//         FlowEntryWxMaterialRequest wxRequest = new FlowEntryWxMaterialRequest();
//         wxRequest.setShopId(Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L));
//         List<Integer> list = Lion.getList(LionConstants.DZTGDETAIL_APPKEY, LionConstants.FLOW_ENTRY_WX_MATERIAL_CONFIG, Integer.class, Lists.newArrayList());
//         if (!list.contains(backPoiSecondCateGoryId)) {
//             return CompletableFuture.completedFuture(null);
//         }
//         wxRequest.setCategoryId((long) backPoiSecondCateGoryId);
//         wxRequest.setDpCityId(Optional.ofNullable(cityIdMapper).map(CityIdMapper::getDpCityId).orElse(0));
//         wxRequest.setMtCityId(Optional.ofNullable(cityIdMapper).map(CityIdMapper::getMtCityId).orElse(0));
//         wxRequest.setUserId(request.getUserId());
//         wxRequest.setPlatform(request.getPlatformEnum().getCode());
//         wxRequest.setPageLocation(PageLocationType.SPECIAL_PRICE_DETAIL_PAGE.getCode());
//         wxRequest.setOrderId(String.valueOf(request.getProductId()));
//         GeoDTO geoDTO = new GeoDTO();
//         geoDTO.setLat(request.getUserLat());
//         geoDTO.setLng(request.getUserLng());
//         wxRequest.setGeoDTO(geoDTO);
//
//         return compositeAtomService.queryFlowEntryWxMaterial(wxRequest).thenApply(res -> {
//             if (res == null) {
//                 return null;
//             }
//             return new CorpWxResult(res);
//         });
//     }
// }
