package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 茶点水果
 *
 * @author: created by hang.yu on 2023/9/12 21:22
 */
@Getter
@AllArgsConstructor
public enum TeaFoodEnum {

    /**
     * 水果
     */
    FRUIT("Fruit", "水果", "https://p0.meituan.net/ingee/4335100d146f0a6810f56da126c8d22c1938.png", "https://p0.meituan.net/ingee/2c6456c553f0b46f18cdc1bc0ecd9b087311.png"),

    /**
     * 茶水
     */
    TEA("TeaWater", "茶水", "https://p0.meituan.net/ingee/8db0bf2b944241778e710d79481ccdda1162.png", "https://p0.meituan.net/ingee/489891a1f949a33163db1cc464d5ad845570.png"),

    /**
     * 零食
     */
    SNACK("Snack", "零食", "https://p0.meituan.net/ingee/2afa0d8973672b9dcdd3fcb59ae9ac921536.png", "https://p0.meituan.net/ingee/7ecedda14335d0a35fd3139f32596cbf6295.png"),
    ;

    /**
     * 食物code
     */
    private final String foodCode;

    /**
     * 食物名称
     */
    private final String foodName;

    /**
     * 食物icon
     */
    private final String foodIcon;


    /**
     * 新的食物icon
     */
    private final String newFoodIcon;
}
