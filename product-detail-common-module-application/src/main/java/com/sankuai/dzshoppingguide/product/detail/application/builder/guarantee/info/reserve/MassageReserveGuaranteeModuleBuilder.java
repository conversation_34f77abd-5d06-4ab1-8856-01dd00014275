package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.reserve;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PurchaseNotesConvertUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 足疗预订保障模块构造器
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductPurchaseNoteFetcher.class
        }
)
public class MassageReserveGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {
    @Override
    public ProductDetailGuaranteeVO doBuild() {
        List<String> reminderInfo = new ArrayList<>();
        ProductPurchaseNote productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
        PurchaseNotesConvertUtils.buildReminderInfo(productPurchaseNote,
                PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_GUARANTEE, reminderInfo);
        // 构建展示模块
        ProductDetailGuaranteeVO reminderInfoVO = ReminderInfoUtils.buildModel0("保障", reminderInfo);
        // 保障条不需要跳转特意移除
        if (CollectionUtils.isNotEmpty(reminderInfoVO.getContents())) {
            reminderInfoVO.getContents().get(reminderInfoVO.getContents().size() - 1).setSuffixIcon(new Icon());
        }
        return reminderInfoVO;
    }
}
