package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee;

import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.LEInsuranceAgreementEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.ShopTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.ShopTagReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.combine.ProductCombineInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.combine.ProductCombineInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PurchaseNotesConvertUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.tag.ShopTagUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_ORDER_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_XCX;

//@Builder(
//        moduleKey = ModuleKeyConstants.GUARANTEE_INFO, startFetcher = CommonModuleStarter.class,
//        dependentFetchers = {ProductPurchaseNoteFetcher.class, ProductAttrFetcher.class, GuaranteeTagFetcher.class}
//)
@Deprecated
public class GuaranteeInfoModuleBuilder extends BaseBuilder<ProductDetailGuaranteeVO> {

    private static final Set<ClientTypeEnum> MT_LIVE_MINI_APP = Sets.newHashSet(MT_LIVE_XCX, MT_LIVE_ORDER_XCX);

    @Resource(name = "guaranteeBuilderStrategyFactory")
    private GuaranteeBuilderStrategyFactory factory;

    @Override
    public ProductDetailGuaranteeVO doBuild() {
        if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return buildReserveGuaranteeInfo(request);
        }
        return buildDealGuaranteeInfo(request);
    }

    private ProductDetailGuaranteeVO buildReserveGuaranteeInfo(ProductDetailPageRequest pageRequest) {
        List<String> reminderInfo = new ArrayList<>();
        ProductPurchaseNote productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
        PurchaseNotesConvertUtils.buildReminderInfo(productPurchaseNote,
                PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_GUARANTEE, reminderInfo);
        // 构建展示模块
        ProductDetailGuaranteeVO reminderInfoVO = ReminderInfoUtils.buildModel0("保障", reminderInfo);
        // 保障条不需要跳转特意移除
        if (CollectionUtils.isNotEmpty(reminderInfoVO.getContents())) {
            reminderInfoVO.getContents().get(reminderInfoVO.getContents().size() - 1).setSuffixIcon(new Icon());
        }
        return reminderInfoVO;
    }

    private ProductDetailGuaranteeVO buildDealGuaranteeInfo(ProductDetailPageRequest pageRequest) {
        // 选择合适的保障条展示策略
        GuaranteeBuilderStrategy strategy = selectStrategy(pageRequest);
        GuaranteeParam guaranteeParam = new GuaranteeParam();
        guaranteeParam.setPageRequest(pageRequest);
        // 团单属性
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        guaranteeParam.setProductAttr(productAttr);
        // 保障标签
        ProductGuaranteeTagInfo productGuaranteeTagInfo = getDependencyResult(GuaranteeTagFetcher.class);
        guaranteeParam.setProductGuaranteeTagInfo(productGuaranteeTagInfo);
        // 附加项
        ProductCombineInfo productCombineInfo  = getDependencyResult(ProductCombineInfoFetcher.class);
        guaranteeParam.setCombines(productCombineInfo.getCombines());
        // 门店标签
        ShopTagReturnValue shopTagReturnValue = getDependencyResult(ShopTagFetcher.class);
        guaranteeParam.setShopDisplayTagList(shopTagReturnValue.getShopDisplayTagDtoList());
        // 团单类目
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        guaranteeParam.setProductCategory(productCategory);
        // 团单基础信息
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        guaranteeParam.setProductBaseInfo(productBaseInfo);
        // 三方团单信息
//        ThirdPartyDealReturnValue thirdPartyDealReturnValue = getDependencyResult(ThirdPartyDealFetcher.class);
//        guaranteeParam.setDpDealGroupId2ThirdPartyMap(thirdPartyDealReturnValue.getDpDealGroupId2ThirdPartyMap());

        // 构建保障条
//        List<GuaranteeInstructionsContentVO> contents = strategy.build(guaranteeParam);
        ProductDetailGuaranteeVO guaranteeVO = new ProductDetailGuaranteeVO();
        guaranteeVO.setTitle("保障");
//        guaranteeVO.setContents(contents);
        return guaranteeVO;
    }

    private GuaranteeBuilderStrategy selectStrategy(ProductDetailPageRequest pageRequest) {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        // 不展示保障条
        if (hitNoneStrategy(pageRequest, productAttr)) {
            return factory.getStrategy(GuaranteeStrategyEnum.NONE);
        }
        // 安心学
        if (hitSafeLearnStrategy()) {
            return factory.getStrategy(GuaranteeStrategyEnum.SAFE_LEARN);
        }
        // 交易保障
        if (isTradeAssuranceDeal(productAttr)) {
            return factory.getStrategy(GuaranteeStrategyEnum.TRADE_ASSURANCE);
        }
        // todo 局改装修
        // 在线教育
        if (isEduOnlineDeal(productCategory.getProductThirdCategoryId())) {
            String refundRule = productAttr.getSkuAttrFirstValue("refund_rule");
            if (StringUtils.isNotBlank(refundRule)) {
                return factory.getStrategy(GuaranteeStrategyEnum.EDU_ONLINE);
            }
        }
        // 眼镜
        if (productCategory.getProductSecondCategoryId() == 406) {
            return factory.getStrategy(GuaranteeStrategyEnum.GLASSES);
        }
        // 口腔齿科
        if (productCategory.getProductSecondCategoryId() == 506) {
            return factory.getStrategy(GuaranteeStrategyEnum.ORAL_DENTISTRY);
        }
        // 保洁自营
        ShopTagReturnValue shopTagReturnValue = getDependencyResult(ShopTagFetcher.class);
        LEInsuranceAgreementEnum leInsuranceAgreementEnum = ShopTagUtils.getLEInsuranceAgreementEnum(shopTagReturnValue.getShopDisplayTagDtoList());
        if (leInsuranceAgreementEnum != null) {
            return factory.getStrategy(GuaranteeStrategyEnum.CLEANING_SELF_OPERATION);
        }
        return factory.getStrategy(GuaranteeStrategyEnum.DEFAULT);
    }

    private boolean hitNoneStrategy(ProductDetailPageRequest pageRequest, ProductAttr productAttr) {
        return MT_LIVE_MINI_APP.contains(pageRequest.getClientTypeEnum()) || isWuYouTongDeal(productAttr);
    }

    private boolean hitSafeLearnStrategy() {
        ProductGuaranteeTagInfo productGuaranteeTag = getDependencyResult(GuaranteeTagFetcher.class);
        return Objects.nonNull(productGuaranteeTag) && Objects.nonNull(productGuaranteeTag.getSafeLearnGuaranteeInfo());
    }

    /**
     * 判断是否为无忧通团单
     */
    private boolean isWuYouTongDeal(ProductAttr productAttr) {
        AttrDTO standardDealGroupKeyAttr = productAttr.safeGetSkuAttr("standardDealGroupKey");
        return DealAttrHelper.isWuYouTongAttribute(standardDealGroupKeyAttr);
    }

    /**
     * 判断是否为交易保障团单
     * @param productAttr 团单属性
     * @return 是否为交易保障团单
     */
    private static boolean isTradeAssuranceDeal(ProductAttr productAttr) {
        String tradeAssurance = productAttr.getSkuAttrFirstValue("is_trade_assurance");
        return Objects.equals(tradeAssurance, "1");
    }

    private static boolean isEduOnlineDeal(int thirdCategoryId) {
        List<Long> eduOnlineDealServiceLeafIds = LionConfigUtils.getEduOnlineDealServiceLeafIds();
        return eduOnlineDealServiceLeafIds.contains((long) thirdCategoryId);
    }
}
