package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.unavailable.UnavailabelDate;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class UnavailableReminderInfoUtils {

    private static final String UNAVAILABLE_TEXT = "该团购今日无法在门店使用，具体情况请联系门店确认";

    /**
     * 判断是否命中可用时间,true 表示当前可用, false 表示当前不可用
     */
    public static boolean hitAvailableTime(AvailableDateDTO availableDateDTO) {
        if (availableDateDTO == null || availableDateDTO.getAvailableType() == null) {
            return true;
        }

        Integer availableType = availableDateDTO.getAvailableType();
        //判断可用时间
        // 可用日期类型：0：周期使用，1：指定日期适用
        if (availableType == 0) {
            // 周期时间可用 例如：周一 ~~ 周日 可用，判断字段 CycleAvailableDateList
            List<CycleAvailableDateDTO> cycleAvailableDateList = availableDateDTO.getCycleAvailableDateList();
            if (CollectionUtils.isNotEmpty(cycleAvailableDateList)) {
                Set<Integer> availableDaysSet = cycleAvailableDateList.stream()
                        .filter(e -> e.getAvailableDays() != null)
                        .flatMap(e -> e.getAvailableDays().stream())
                        .collect(Collectors.toSet());
                return availableDaysSet.contains(dayOfWeek());
            }
        }else if (availableType == 1){
            // 指定日期可用 判断字段 specifiedDurationDateList
            List<AvailableDurationDateDTO> specifiedDurationDateList = availableDateDTO.getSpecifiedDurationDateList();
            if (CollectionUtils.isNotEmpty(specifiedDurationDateList)) {
                return validSpecifiedDurationDateList(availableDateDTO.getSpecifiedDurationDateList());
            }
        }
        return true;
    }

    public static boolean validSpecifiedDurationDateList(List<AvailableDurationDateDTO> specifiedDurationDateList){
        if (specifiedDurationDateList != null) {
            return specifiedDurationDateList.stream()
                    .filter(e-> Objects.nonNull(e) && org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getAvailableDateRangeDTOS()))
                    .anyMatch(e -> validAvailableDateRange(e.getAvailableDateRangeDTOS()));
        }
        return false;
    }

    public static boolean validAvailableDateRange(List<DateRangeDTO> availableDateRangeDTOS){
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(availableDateRangeDTOS)){
            return false;
        }
        return availableDateRangeDTOS.stream().anyMatch(e-> DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
    }

    public static boolean hitWeekAndHolidayUnavailable(List<Integer> disableDays,Map<String, UnavailabelDate> unavailabelDateMap) {
        if (CollectionUtils.isEmpty(disableDays)) {
            return false;
        }

        // 先判断是否命中了一周不可用设置
        if (disableDays.contains(dayOfWeek())) {
            return true;
        }

        // 再判断是否命中了节假日不可用
        if (MapUtils.isEmpty(unavailabelDateMap)) {
            return false;
        }

        return disableDays.stream().filter(Objects::nonNull).map(item ->
                        // 将设置的一周不可用时间过滤掉
                        unavailabelDateMap.getOrDefault(String.valueOf(item), null)).filter(
                        date -> date != null && StringUtils.isNotBlank(date.getFrom()) && StringUtils.isNotBlank(date.getTo()))
                .anyMatch(date -> DateHelper.isCurrentDateInRange(date.getFrom(), date.getTo()));
    }

    public static boolean hitCustomUnavailable(List<DateRangeDTO> disableDateRangeDTOS) {
        if (CollectionUtils.isEmpty(disableDateRangeDTOS)) {
            return false;
        }
        return disableDateRangeDTOS.stream().filter(e -> e != null && e.getFrom() != null && e.getTo() != null)
                .anyMatch(e -> DateHelper.isCurrentDateInRange(e.getFrom(), e.getTo()));
    }

    public static boolean hitWeekendUnavailable(String weekendTime) {
        // [1,2,3,4] 这个是一周中可用的天数
        if (StringUtils.isBlank(weekendTime)) {
            return false;
        }
        return !weekendTime.contains(String.valueOf(dayOfWeek()));
    }

    public static int dayOfWeek() {
        LocalDate localDate = LocalDate.now();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek.getValue();
    }

    public static ProductDetailReminderVO processHit(ProductDetailReminderVO productDetailReminderVO) {
        if (productDetailReminderVO == null) {
            productDetailReminderVO = new ProductDetailReminderVO();
            GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
            reminderInfoLayerVO.setType(2);
            reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
            productDetailReminderVO.setLayer(reminderInfoLayerVO);
            GuaranteeInstructionsContentVO content = ReminderInfoUtils
                    .buildUnavailableReminderInfo(UNAVAILABLE_TEXT);
            productDetailReminderVO.setSpecialContents(Lists.newArrayList(content));
            return productDetailReminderVO;
        }

        List<GuaranteeInstructionsContentVO> contents = productDetailReminderVO.getSpecialContents();
        GuaranteeInstructionsContentVO content = ReminderInfoUtils
                .buildUnavailableReminderInfo(UNAVAILABLE_TEXT);
        if (CollectionUtils.isEmpty(contents)) {
            productDetailReminderVO.setSpecialContents(Lists.newArrayList(content));
        } else {
            // 不可用须知信息置顶
            List<GuaranteeInstructionsContentVO> newContents = Lists.newArrayList(content);
            newContents.addAll(contents);
            productDetailReminderVO.setSpecialContents(newContents);
        }
        return productDetailReminderVO;
    }

    public static ProductDetailReminderVO processHit(ProductDetailReminderVO productDetailReminderVO, boolean oldDetail) {
        if (productDetailReminderVO == null) {
            productDetailReminderVO = new ProductDetailReminderVO();
            GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
            reminderInfoLayerVO.setType(2);
            reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
            productDetailReminderVO.setLayer(reminderInfoLayerVO);
            GuaranteeInstructionsContentVO content = ReminderInfoUtils
                    .buildUnavailableReminderInfo(UNAVAILABLE_TEXT, oldDetail);
            productDetailReminderVO.setSpecialContents(Lists.newArrayList(content));
            return productDetailReminderVO;
        }

        List<GuaranteeInstructionsContentVO> contents = productDetailReminderVO.getSpecialContents();
        GuaranteeInstructionsContentVO content = ReminderInfoUtils
                .buildUnavailableReminderInfo(UNAVAILABLE_TEXT, oldDetail);
        if (CollectionUtils.isEmpty(contents)) {
            productDetailReminderVO.setSpecialContents(Lists.newArrayList(content));
        } else {
            // 不可用须知信息置顶
            List<GuaranteeInstructionsContentVO> newContents = Lists.newArrayList(content);
            newContents.addAll(contents);
            productDetailReminderVO.setSpecialContents(newContents);
        }
        return productDetailReminderVO;
    }
}
