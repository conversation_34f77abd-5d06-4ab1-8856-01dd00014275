package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ProductCategoryName extends FetcherReturnValueDTO{
    private String secondCategoryName;

    private String thirdCategoryName;

    private String fourthCategoryName;
    
}
