package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.hold.status;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 15:52
 *        com.sankuai.dzcard.navigation.api.DzCardExposureService#findShopAndUserCardHoldStatusWithLongShopIdLite
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
public class CardHoldStatusFetcher extends NormalFetcherContext<CardHoldStatusResult> {

    @RpcClient(url = "com.sankuai.dzcard.navigation.api.DzCardExposureService", timeout = 300)
    private DzCardExposureService dzCardExposureService;

    @Override
    protected CompletableFuture<CardHoldStatusResult> doFetch() {
        try {
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            if(request == null || request.getShepherdGatewayParam() == null || shopIdMapper == null){
                return CompletableFuture.completedFuture(null);
            }
            return AthenaInf
                    .getRpcCompletableFuture(dzCardExposureService
                            .findShopAndUserCardHoldStatusWithLongShopIdLite(buildRequest(request, shopIdMapper)))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build().putTag("scene", "CardHoldStatusFetcher").message(String
                                .format("CardHoldStatusFetcher doFetch error, req: %s", JsonCodec.encode(request))));
                        return null;
                    }).thenApply(res -> {
                        if (res == null) {
                            return null;
                        }
                        return new CardHoldStatusResult(res);
                    });

        } catch (Exception e) {
            log.error("CardHoldStatusFetcher error,request:{}", JsonCodec.encodeWithUTF8(request), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private FindDCCardHoldStatusLiteReqDTO buildRequest(ProductDetailPageRequest request, ShopIdMapper idMapperResult) {
        int platform = request.getPlatformEnum().getCode();
        FindDCCardHoldStatusLiteReqDTO findCardHoldStatusReqDTO = new FindDCCardHoldStatusLiteReqDTO();
        findCardHoldStatusReqDTO.setPlatform(platform);
        findCardHoldStatusReqDTO.setDpShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getDpBestShopId).orElse(0L));
        if (request.getClientTypeEnum().isMtClientType()) {
            long mtUserId = request.getMtUserId();
            findCardHoldStatusReqDTO.setUserId(mtUserId);
            findCardHoldStatusReqDTO
                    .setShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getMtBestShopId).orElse(0L));
        } else {
            long dpUserId = request.getDpUserId();
            findCardHoldStatusReqDTO.setUserId(dpUserId);
            findCardHoldStatusReqDTO
                    .setShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getDpBestShopId).orElse(0L));
        }
        return findCardHoldStatusReqDTO;
    }
}
