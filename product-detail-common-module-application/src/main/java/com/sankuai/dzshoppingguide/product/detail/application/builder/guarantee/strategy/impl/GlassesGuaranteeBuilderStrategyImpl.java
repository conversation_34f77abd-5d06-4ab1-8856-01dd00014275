package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.utils.tag.ShopTagUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 眼镜保障标签构造策略
 */
public class GlassesGuaranteeBuilderStrategyImpl extends BaseGuaranteeBuilderStrategy implements GuaranteeBuilderStrategy {

    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.GLASSES;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        List<DisplayTagDto> shopDisplayTagList = param.getShopDisplayTagList();
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        if (ShopTagUtils.isSafeOptometry(shopDisplayTagList)) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
            content.setText("安心配镜");
            content.setFontColor("#003576");
            contents.add(content);
        }
        if (ShopTagUtils.isGenuineGuarantee(shopDisplayTagList, param.getProductBaseInfo())) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
            content.setText("正品保障");
            content.setFontColor("#003576");
            contents.add(content);
        }
        List<GuaranteeInstructionsContentVO> guaranteeInstructionsContents = buildContents(param);
        if (CollectionUtils.isNotEmpty(guaranteeInstructionsContents)) {
            contents.addAll(guaranteeInstructionsContents);
        }
//        return contents;
        return null;
    }
}
