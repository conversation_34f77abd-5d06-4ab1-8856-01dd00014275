package com.sankuai.dzshoppingguide.product.detail.application.builder.additional;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.addition.vo.AdditionalInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 15:05
 */
@Builder(
        moduleKey = ModuleKeyConstants.ADDITIONAL_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {AdditionInfoFetcher.class}
)
public class AdditionalInfoBuilder extends BaseBuilder<AdditionalInfoVO> {

    @Override
    public AdditionalInfoVO doBuild() {

        if ( !Objects.equals(ProductTypeEnum.DEAL,request.getProductTypeEnum())) {
            return null;
        }
        AdditionInfoResult additionInfo = getDependencyResult(AdditionInfoFetcher.class);
        String additionInfoStr = Optional.ofNullable(additionInfo).map(AdditionInfoResult::getAdditionInfo).orElse(StringUtils.EMPTY);
        String desc = FootMassageUtils.getDesc(additionInfoStr);
        if (StringUtils.isBlank(desc)) {
            return null;
        }
        AdditionalInfoVO additionalInfoVO = new AdditionalInfoVO();
        additionalInfoVO.setName("补充说明");
        additionalInfoVO.setDescModel(desc);
        return additionalInfoVO;
    }
}
