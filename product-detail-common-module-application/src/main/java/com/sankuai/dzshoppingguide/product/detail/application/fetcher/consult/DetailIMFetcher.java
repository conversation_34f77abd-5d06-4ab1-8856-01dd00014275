package com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult;

/**
 * <AUTHOR>
 * @create 2025/3/18 20:26
 */

import com.alibaba.fastjson.JSONObject;
import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.dzim.common.enums.ImSendUnitType;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.constant.CsCenterAccessAppKey;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils.isPureHarmony;
import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.DZ_TRADE_MAPI_WEB_APP_KEY;
import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.PIPING_SERVICE_DEAL_GROUP_ATTR_VALUE;

/**
 * <AUTHOR>
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class, ShopIdMapperFetcher.class, ProductBaseInfoFetcher.class})
@Slf4j
public class DetailIMFetcher extends NormalFetcherContext<DetailIMResult> {

    private static final String CLEANING_SELF_OWN_DEAL_GROUP_ATTR_KEY = "self_own_product";
    private static final String PIPING_SERVICE_DEAL_GROUP_ATTR_KEY = "standardDealGroupKey";

    @Autowired
    private CompositeAtomService compositeAtomService;
    private DealGroupIdMapper dealGroupIdMapper = null;
    private ShopIdMapper shopIdMapper = null;
    private ProductBaseInfo productBaseInfo = null;

    @Override
    protected CompletableFuture<DetailIMResult> doFetch() {
        dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);

        // todo 待确认
        List<AttrDTO> attrs = Objects.nonNull(productBaseInfo) ? productBaseInfo.getAttrs() : Lists.newArrayList();
        // LE保洁自营/无忧通 使用太平洋自营智能门户
        if ((LionConfigUtils.needReplaceImToCsForCleaning() && isCleaningSelfOwnDeal(attrs))
                || (LionConfigUtils.needReplaceImToCsForCarefree() && isCareFreeDeal(attrs))) {
            return compositeAtomService.prepareAccessIn(buildAccessRequestDTO(attrs)).thenApply(res->{
                return new DetailIMResult(getCsCenterUrl(res));
            });
        } else {
            return compositeAtomService.preOnlineConsultUrl(buildClientEntryReqDTO(dealGroupIdMapper, shopIdMapper)).thenApply(res->{
                if (res != null){
                    return new DetailIMResult(getOnlineConsultUrl(res));
                }
                return null;
            });
        }
    }

    /**
     * 获取太平洋智能客服跳链
     */
    public String getCsCenterUrl(AccessResponseDTO result) {
        if (result != null && result.isSuccess()) {
            return result.getUrl();
        }
        return null;
    }

    private String getOnlineConsultUrl(ClientEntryDTO result) {
        if (result == null || !result.isShow() || StringUtils.isBlank(result.getEntryUrl())) {
             return null;
        }

        String originOnlineConsultUrl = result.getEntryUrl();
        if (ClientTypeEnum.MT_KUAI_SHOU_XCX.equals(request.getClientTypeEnum())) {
            String encodeUrl;

            try {
                encodeUrl = URLEncoder.encode(originOnlineConsultUrl, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                log.error("originOnlineConsultUrl={},encode报错", originOnlineConsultUrl, e);
                return null;
            }

            return "/index/pages/h5/h5?f_token=1&noshare=1&weburl=" + encodeUrl;
        }

        return originOnlineConsultUrl;
    }

    public ClientEntryReqDTO buildClientEntryReqDTO(DealGroupIdMapper dealGroupIdMapper, ShopIdMapper shopIdMapper){
        ClientEntryReqDTO clientEntryReqDTO = new ClientEntryReqDTO();
        clientEntryReqDTO.setDpShopId(shopIdMapper.getDpBestShopId());
        clientEntryReqDTO.setClientType(request.getClientType());
        clientEntryReqDTO.setSendUnitId(String.valueOf(dealGroupIdMapper.getDpDealGroupId()));
        clientEntryReqDTO.setSendUnitType(ImSendUnitType.DEALGROUP.unitType);
        Map<String, String> additionalParam = Maps.newHashMapWithExpectedSize(1);
        additionalParam.put("source", "DealDetail");
        clientEntryReqDTO.setAdditionalParam(additionalParam);
        return clientEntryReqDTO;
    }

    public AccessRequestDTO buildAccessRequestDTO(List<AttrDTO> attrs){
        AccessRequestDTO req = new AccessRequestDTO();
        req.setAppKey(getCscAppKey(attrs));
        req.setOpenId(String.valueOf(request.getUserId()));
        req.setLocCity(String.valueOf(request.getCityId()));
        req.setBuExt(buildBuExt());
        return req;
    }

    private String getCscAppKey(List<AttrDTO> attrs) {
        String appKey = "";
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        // 保洁自营
        if (isCleaningSelfOwnDeal(attrs)){
            // 美团app
            if (clientTypeEnum == ClientTypeEnum.MT_APP && !isHarmony()){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_APP;
            }
            // 美团小程序
            if (clientTypeEnum == ClientTypeEnum.MT_XCX){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_MINI_PROGRAM;
            }
            // 美团I站
            if (clientTypeEnum == ClientTypeEnum.MT_I){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_I;
            }
            // 美团鸿蒙App
            if (clientTypeEnum == ClientTypeEnum.MT_APP && isHarmony()){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_HM_APP;
            }
            // 点评app
            if (clientTypeEnum == ClientTypeEnum.DP_APP && !isHarmony()){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_APP;
            }
            // 点评小程序
            if (clientTypeEnum == ClientTypeEnum.DP_XCX){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_MINI_PROGRAM;
            }
            // 点评M站
            if (clientTypeEnum == ClientTypeEnum.DP_M){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_M;
            }
            // 点评鸿蒙App
            if (clientTypeEnum == ClientTypeEnum.DP_APP && isHarmony()){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_DP_HM_APP;
            }
            if (StringUtils.isBlank(appKey)){
                appKey = CsCenterAccessAppKey.CLEANING_DEAL_DETAIL_MT_APP;
            }
        }

        // 无忧通
        if (isCareFreeDeal(attrs)){
            // 美团app
            if (clientTypeEnum == ClientTypeEnum.MT_APP && !isHarmony()){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_APP;
            }
            // 美团小程序
            if (clientTypeEnum == ClientTypeEnum.MT_XCX){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_MINI_PROGRAM;
            }
            // 美团I站
            if (clientTypeEnum == ClientTypeEnum.MT_I){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_I;
            }
            // 美团鸿蒙App
            if (clientTypeEnum == ClientTypeEnum.MT_APP && isHarmony()){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_HM_APP;
            }
            // 点评app
            if (clientTypeEnum == ClientTypeEnum.DP_APP && !isHarmony()){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_APP;
            }
            // 点评小程序
            if (clientTypeEnum == ClientTypeEnum.DP_XCX){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_MINI_PROGRAM;
            }
            // 点评M站
            if (clientTypeEnum == ClientTypeEnum.DP_M){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_M;
            }
            // 点评鸿蒙App
            if (clientTypeEnum == ClientTypeEnum.DP_APP && isHarmony()){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_DP_HM_APP;
            }
            if (StringUtils.isBlank(appKey)){
                appKey = CsCenterAccessAppKey.CAREFREE_DEAL_DETAIL_MT_APP;
            }
        }
        return appKey;
    }

    private String buildBuExt() {
        JSONObject buExtJsonObject = new JSONObject();
        buExtJsonObject.put("merchandiseId", String.valueOf(request.getProductId()));
        buExtJsonObject.put("shopId", String.valueOf(request.getPoiId()));
        return buExtJsonObject.toJSONString();
    }

    private boolean isCleaningSelfOwnDeal(List<AttrDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)){
            return false;
        }
        return DealAttrHelper.validAttr(attrs, CLEANING_SELF_OWN_DEAL_GROUP_ATTR_KEY, "是" );
    }

    private boolean isCareFreeDeal(List<AttrDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)){
            return false;
        }
        List<String> attrValues = DealAttrHelper.getAttributeValuesV2(attrs, PIPING_SERVICE_DEAL_GROUP_ATTR_KEY);
        if (CollectionUtils.isNotEmpty(attrValues)){
            List<String> serviceAttrValueList = Lion.getList(DZ_TRADE_MAPI_WEB_APP_KEY, PIPING_SERVICE_DEAL_GROUP_ATTR_VALUE, String.class, Lists.newArrayList());
            return serviceAttrValueList.stream().anyMatch(attrValues::contains);
        }
        return false;
    }

    public boolean isHarmony(){
        String userAgent = request.getShepherdGatewayParam().getUserAgent();
        return isPureHarmony(userAgent);
    }


}
