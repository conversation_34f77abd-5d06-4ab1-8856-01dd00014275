package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 美团安心改
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 15:54
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = { ProductBaseInfoFetcher.class, ProductServiceProjectFetcher.class }
)
@Slf4j
public class ReassuredRepairReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductServiceProject serviceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (serviceProject == null) {
            return null;
        }

        ProductDetailReminderVO bookingDetailsReminderInfoVO = new ProductDetailReminderVO();
        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        bookingDetailsReminderInfoVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        ReminderInfoUtils.buildReminderInfo("7天无理由退款").ifPresent(contents::add);
        ReminderInfoUtils.buildReminderInfo("过期自动退").ifPresent(contents::add);

        Map<String, ServiceProjectAttrDTO> serviceProjectAttrDTOMap = getStringServiceProjectAttrDTOMap(serviceProject);
        if (serviceProjectAttrDTOMap.containsKey("qizhuangxianzhi")) {
            ServiceProjectAttrDTO qizhuangxianzhi = serviceProjectAttrDTOMap.get("qizhuangxianzhi");
            ServiceProjectAttrDTO qizhuangxianzhi2 = serviceProjectAttrDTOMap.get("qizhuangxianzhi2");
            if (qizhuangxianzhi.getAttrValue().equals("是") && qizhuangxianzhi2!=null) {
                ServiceProjectAttrDTO priceunit = serviceProjectAttrDTOMap.get("priceunit");
                if (priceunit != null) {
                    ReminderInfoUtils.buildReminderInfo(qizhuangxianzhi2.getAttrValue() + priceunit.getAttrValue() + "起装").ifPresent(contents::add);
                }
            }
        }
        if (serviceProjectAttrDTOMap.containsKey("zhichiyufujinketui")) {
            ServiceProjectAttrDTO zhichiyufujinketui = serviceProjectAttrDTOMap.get("zhichiyufujinketui");
            if (zhichiyufujinketui.getAttrValue().equals("是")) {
                ReminderInfoUtils.buildReminderInfo("上门后不满意预付金可退").ifPresent(contents::add);
            }
        }

        String rule = Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getReceiptEffectiveDate).map(ReceiptEffectiveDateDTO::getShowText).orElse(StringUtils.EMPTY);
        ReminderInfoUtils.buildReminderInfo(rule).ifPresent(contents::add);

        bookingDetailsReminderInfoVO.setContents(contents);
        return bookingDetailsReminderInfoVO;

    }


    private Map<String, ServiceProjectAttrDTO> getStringServiceProjectAttrDTOMap(ProductServiceProject serviceProject) {
        return Optional.ofNullable(serviceProject.getServiceProject())
                .map(DealGroupServiceProjectDTO::getMustGroups)
                .filter(mustGroups -> !mustGroups.isEmpty())
                .map(mustGroups -> mustGroups.get(0))
                .map(MustServiceProjectGroupDTO::getGroups)
                .filter(groups -> !groups.isEmpty())
                .map(groups -> groups.get(0))
                .map(ServiceProjectDTO::getAttrs)
                .filter(attrs -> !attrs.isEmpty())
                .map(attrs -> attrs.stream().collect(Collectors.toMap(ServiceProjectAttrDTO::getAttrName, p -> p)))
                .orElse(Collections.emptyMap());
    }
}
