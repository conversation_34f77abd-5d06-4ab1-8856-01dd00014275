package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 17:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TechStockWhiteShopResult extends FetcherReturnValueDTO {
    private boolean isWhiteList;
}
