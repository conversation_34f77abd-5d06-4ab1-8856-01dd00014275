package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.theme;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.enums.BusinessStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ShopCard extends FetcherReturnValueDTO {

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 门店地图跳链
     */
    private String shopMapUrl;

    /**
     * 门店跳链
     */
    private String shopUrl;

    /**
     * 商户电话数组
     */
    private List<String> phoneNos;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 营业时间
     */
    private String businessHour;

    /**
     * 均价文案
     */
    private String avgPrice;

    /**
     * 门店星级，数值在[0,50], 如果传入-1代表无星级信息
     */
    private int shopPower;

    /**
     * 距离文案
     */
    private String distance;

    /**
     * 门店头图
     */
    private String shopPic;

    /**
     * 当前营业状态
     */
    private BusinessStateEnum businessStateEnum;

}
