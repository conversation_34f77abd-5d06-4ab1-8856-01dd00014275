package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy;

import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 足疗三级分类工厂
 *
 * @author: created by hang.yu on 2023/8/17 10:46
 */
@Component
public class MassageToolFactory {

    @Resource
    private Map<String, MassageStrategy> massageToolStrategyMap;

    public static final Set<Long> STANDARD_MASSAGE_CATEGORY_IDS = Arrays.stream(ProductCategoryEnum.values())
            .map(ProductCategoryEnum::getProductCategoryId).collect(Collectors.toSet());

    public static final Set<Long> SINGLE_STANDARD_MASSAGE_CATEGORY_IDS = Sets.newHashSet();
    public static final Set<Long> COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS = Sets.newHashSet();

    static {
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.FOOT_MASSAGE.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.MASSAGE.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.ESSENTIAL_OIL_SPA.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.ULNA.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.EAR_PICKING.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.MOXIBUSTION.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.CUP.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.SCRAPING.getProductCategoryId());
        SINGLE_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.HEAD.getProductCategoryId());

        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_FOOT_MASSAGE.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_MASSAGE.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_ESSENTIAL_OIL_SPA.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_ULNA.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_EAR_PICKING.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_MOXIBUSTION.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_CUP.getProductCategoryId());
        COMBINATION_STANDARD_MASSAGE_CATEGORY_IDS.add(ProductCategoryEnum.COMBINATION_SCRAPING.getProductCategoryId());
    }

    /**
     * 获取材料工具值
     */
    public String getToolValue(ServiceProjectDTO serviceProject) {
        if (CollectionUtils.isEmpty(serviceProject.getAttrs())) {
            return null;
        }
        String strategyName = ProductCategoryEnum.getStrategyName(serviceProject.getCategoryId());
        if (StringUtils.isBlank(strategyName)) {
            return null;
        }
        MassageStrategy massageToolStrategy = massageToolStrategyMap.get(strategyName);
        if (massageToolStrategy == null) {
            return null;
        }
        return massageToolStrategy.getToolValue(serviceProject.getAttrs());
    }

    /**
     * 足疗标准化的团单分类id -> 处理策略名称
     */
    @Getter
    @AllArgsConstructor
    enum ProductCategoryEnum {

        /**
         * 足疗单品
         */
        FOOT_MASSAGE(2104542L, "footMassageStrategyImpl"),

        /**
         * 推拿/按摩（单品）
         */
        MASSAGE(2104612L, "massageAndBoneSettingStrategyImpl"),

        /**
         * 精油SPA（单品）
         */
        ESSENTIAL_OIL_SPA(2104613L, "spaStrategyImpl"),

        /**
         * 推拿正骨（单品）
         */
        ULNA(2104614L, "massageAndBoneSettingStrategyImpl"),

        /**
         * 采耳（单品）
         */
        EAR_PICKING(2104615L, "earStrategyImpl"),

        /**
         * 艾灸（单品）
         */
        MOXIBUSTION(2104616L, "moxibustionStrategyImpl"),

        /**
         * 拔罐（单品）
         */
        CUP(2104617L, "cupStrategyImpl"),

        /**
         * 刮痧（单品）
         */
        SCRAPING(2104618L, "scrapingStrategyImpl"),

        /**
         * 头疗（单品）
         */
        HEAD(2105875L, "headStrategyImpl"),

        /**
         * 足疗（组合套餐）
         */
        COMBINATION_FOOT_MASSAGE(2104656L, "combinationStrategyImpl"),

        /**
         * 推拿/按摩（组合套餐）
         */
        COMBINATION_MASSAGE(2104657L, "combinationStrategyImpl"),

        /**
         * 精油SPA（组合套餐）
         */
        COMBINATION_ESSENTIAL_OIL_SPA(2104658L, "combinationStrategyImpl"),

        /**
         * 推拿正骨（组合套餐）
         */
        COMBINATION_ULNA(2104659L, "combinationStrategyImpl"),

        /**
         * 采耳（组合套餐）
         */
        COMBINATION_EAR_PICKING(2104660L, "combinationStrategyImpl"),

        /**
         * 艾灸（组合套餐）
         */
        COMBINATION_MOXIBUSTION(2104661L, "combinationStrategyImpl"),

        /**
         * 拔罐（组合套餐）
         */
        COMBINATION_CUP(2104662L, "combinationStrategyImpl"),

        /**
         * 刮痧（组合套餐）
         */
        COMBINATION_SCRAPING(2104663L, "combinationStrategyImpl"),
        ;

        /**
         * 服务项目id
         */
        private final Long productCategoryId;

        /**
         * 执行的策略bean名称
         */
        private final String strategyName;


        public static ProductCategoryEnum valueOf(Long productCategoryId) {
            for (ProductCategoryEnum value : ProductCategoryEnum.values()) {
                if (value.getProductCategoryId().equals(productCategoryId)) {
                    return value;
                }
            }
            return null;
        }

        public static String getStrategyName(Long productCategoryId) {
            ProductCategoryEnum productCategoryEnum = valueOf(productCategoryId);
            if (productCategoryEnum == null) {
                return null;
            }
            return productCategoryEnum.getStrategyName();
        }

    }

}
