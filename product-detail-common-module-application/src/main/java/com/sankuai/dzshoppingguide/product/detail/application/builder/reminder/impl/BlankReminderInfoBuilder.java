package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class BlankReminderInfoBuilder extends AbstractReminderInfoBuilder {
    @Override
    public ProductDetailReminderVO doBuild() {
        return null;
    }

    @Override
    public ProductDetailReminderVO preBuild() {
        return null;
    }
}
