package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.DefaultServiceFacilityBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.MassageDealServiceFacilityBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.MassageReserveServiceFacilityBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.facilities.vo.DetailServiceFacilitiesVO;

/**
 * <AUTHOR>
 * @date 2025-03-19
 * @desc 商品服务设施模块
 */
@Builder(
        moduleKey = ModuleKeyConstants.DEAL_DETAIL_FACILITIES,
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class,
                ProductAttrFetcher.class,
                ProductCategoryFetcher.class
        }
)
public class ProductServiceFacilityModuleFactory extends BaseBuilderFactory<DetailServiceFacilitiesVO> {
    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return MassageReserveServiceFacilityBuilder.class;
        } else {
            return getDealServiceFacilityModuleBuilder();
        }
    }

    private Class<? extends BaseVariableBuilder> getDealServiceFacilityModuleBuilder() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        if (productCategory.getProductSecondCategoryId() == 303) {
            return MassageDealServiceFacilityBuilder.class;
        }
        return DefaultServiceFacilityBuilder.class;
    }
}
