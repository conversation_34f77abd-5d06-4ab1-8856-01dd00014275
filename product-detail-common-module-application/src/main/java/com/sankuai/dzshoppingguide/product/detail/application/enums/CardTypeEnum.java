package com.sankuai.dzshoppingguide.product.detail.application.enums;

import lombok.Getter;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/7/17 19:52
 */
@Getter
public enum CardTypeEnum {
    DISCOUNT_CARD(1, "足疗折扣卡"),
    MEMBER_CARD(2, "免费商家会员卡"),
    PREMIUM_MEMBER_CARD(3, "付费商家会员卡");

    private final int code;

    private final String desc;


    CardTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CardTypeEnum fromCode(int code) {
        for (CardTypeEnum value : CardTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (CardTypeEnum value : CardTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }
}
