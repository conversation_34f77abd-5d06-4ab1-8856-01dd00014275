package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/7
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class SkuDefaultSelect extends FetcherReturnValueDTO {

    private final long selectedSkuId;

    public SkuDefaultSelect(long selectedSkuId) {
        this.selectedSkuId = selectedSkuId;
    }

}