package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class DealDetailStructuredUtils {
    /**
     * title构建
     * @param title
     * @return
     */
    public static DealDetailStructuredDetailVO buildTitle(String title) {
        return DealDetailStructuredDetailVO.builder().title(title).type(ViewComponentTypeEnum.TITLE.getType()).build();
    }

    /**
     * 重点展示信息json content构建
     * @param content
     * @return
     */
    public static DealDetailStructuredDetailVO buildContentFromAttr(String content) {
        return DealDetailStructuredDetailVO.builder().content(content).type(ViewComponentTypeEnum.KEY_INFO.getType()).build();
    }

    /**
     * 镜片信息json content构建
     * @param content
     * @return
     */
    public static DealDetailStructuredDetailVO buildLensContentJson(String content) {
        return DealDetailStructuredDetailVO.builder().content(content).type(ViewComponentTypeEnum.EXCEL.getType()).build();
    }

    /**
     * 重点展示信息内元素构建
     * @param title
     * @param content
     * @return
     */
    public static Optional<DealDetailStructuredDetailVO> buildTitleAndContent(String title, String content) {
        return Optional.of(DealDetailStructuredDetailVO.builder().title(title).content(content).build());
    }

    public static Optional<DealDetailStructuredDetailVO> buildContentAndPopupData(String title, String content, String popupData) {
        return Optional.of(DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .title(title)
                .content(content)
                .popupData(popupData)
                .build());
    }

    /**
     * 镜片更多信息构建,带原点前缀
     * @param title
     * @param content
     * @return
     */
    public static Optional<DealDetailStructuredDetailVO> buildTitleAndContentWithPrefix(String title, String content) {
        return Optional.of(DealDetailStructuredDetailVO.builder().title(title).content(content)
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .prefix(ViewComponentTypeEnum.PREFIX_DOT.getType()).build());
    }


    /**
     * 分隔符构建(当前折叠，点击可展开)
     * @return
     */
    public static DealDetailStructuredDetailVO buildCurrentFoldLimiter() {
        return DealDetailStructuredDetailVO.builder().content("showAll").type(ViewComponentTypeEnum.DELIMITER.getType()).build();
    }

    /**
     * 分隔符构建（上方模块信息全量展示）
     * @return
     */
    public static DealDetailStructuredDetailVO buildLimiter() {
        return DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.DELIMITER.getType()).build();
    }

    /**
     * 分隔符构建(当前展开，点击可折叠)
     * @return
     */
    public static DealDetailStructuredDetailVO buildCurrentAllLimiter() {
        return DealDetailStructuredDetailVO.builder().content("showFold").type(ViewComponentTypeEnum.DELIMITER.getType()).build();
    }

    /**
     * 简单属性构建
     * @param key
     * @param title
     * @param productAttr
     * @return
     */
    public static Optional<DealDetailStructuredDetailVO> buildContentFromAttr(String key, String title, ProductAttr productAttr) {
        String result = productAttr.getSkuAttrFirstValue(key);
        if (StringUtils.isBlank(result) || StringUtils.isBlank(title)) {
            return Optional.empty();
        }
        return DealDetailStructuredUtils.buildTitleAndContent(title,result);
    }

    public static Optional<DealDetailStructuredDetailVO> buildContentFromAttrsWithLimit(String key, String title, ProductAttr productAttr) {
        List<String> skuAttrValues = productAttr.getSkuAttrValues(key);
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Optional.empty();
        }
        String result = skuAttrValues.stream().filter(StringUtils::isNotBlank).limit(3).collect(Collectors.joining("、"));
        return DealDetailStructuredUtils.buildTitleAndContent(title,result);
    }

    public static Optional<DealDetailStructuredDetailVO> buildContentFromAttrs(String key, String title, ProductAttr productAttr) {
        List<String> skuAttrValues = productAttr.getSkuAttrValues(key);
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Optional.empty();
        }
        String result = skuAttrValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
        return DealDetailStructuredUtils.buildTitleAndContent(title,result);
    }

    /**
     * 镜片,镜框标题
     */
    public static DealDetailStructuredDetailVO buildLensTitleVO(String title) {
        return DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .title(title).build();
    }


    /**
     * 服务流程元素构建
     * @param title
     * @param duration
     * @return
     */
    public static Optional<DealDetailStructuredDetailVO> buildProcessInfo(String title, String duration) {
        if (StringUtils.isBlank(title)) {
            return Optional.empty();
        }
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("400")
                .title(title);

        if (StringUtils.isNotBlank(duration) && !StringUtils.equals(duration, "0")) {
            builder.detail(duration + "分钟");
        }

        return Optional.of(builder.build());
    }

    public static Optional<DealDetailStructuredDetailVO> buildSubProcessInfo(String content,String popupData) {
        if (StringUtils.isBlank(content)) {
            return Optional.empty();
        }
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.SUB_TEXT.getType())
                .content(content);
        if (StringUtils.isNotBlank(popupData)) {
            builder.popupData(popupData);
        }
        return Optional.of(builder.build());
    }

    /**
     * 服务流程模块,备注信息构建
     * @return
     */
    public static Optional<DealDetailStructuredDetailVO> buildAnnotation() {
        return Optional.of(DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.ANNOTATION.getType()).content("注：服务时长仅供参考，由于个体差异，实际时长请以到店服务为准").build());
    }

    public static Optional<DealDetailStructuredDetailVO> buildServiceProcessTitle(int duration) {
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.TITLE.getType());
        builder.title("服务流程");
        if (duration > 0) {
            builder.content("共" + duration + "分钟");
        }
        return Optional.of(builder.build());
    }

    public static Optional<DealDetailStructuredDetailVO> buildServiceProcessTitle(String durationValue) {
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.TITLE.getType());
        builder.title("服务流程");
        if (StringUtils.isNotBlank(durationValue)) {
            builder.content("共" + durationValue);
        }
        return Optional.of(builder.build());
    }
}
