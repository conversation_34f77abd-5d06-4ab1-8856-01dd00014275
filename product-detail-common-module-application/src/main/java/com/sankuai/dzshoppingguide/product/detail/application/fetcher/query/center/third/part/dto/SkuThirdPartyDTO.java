package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part.dto;

import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import lombok.Getter;

import java.util.Optional;

/**
 * @Author: guang<PERSON><PERSON><PERSON>
 * @Date: 2025/2/6 16:57
 */
@Getter
public class SkuThirdPartyDTO {

    /**
     * 三方id
     */
    private final long thirdPartyId;

    /**
     * 套餐状态，1：有效，0：无效
     */
    private final int status;

    /**
     * 三方套餐id
     */
    private final String thirdPartyDealId;

    public SkuThirdPartyDTO(final DealBasicDTO basicDTO) {
        this.thirdPartyId = Optional.ofNullable(basicDTO).map(DealBasicDTO::getThirdPartyId).orElse(0L);
        this.status = Optional.ofNullable(basicDTO).map(DealBasicDTO::getStatus).orElse(0);
        this.thirdPartyDealId = Optional.ofNullable(basicDTO).map(DealBasicDTO::getThirdPartyDealId).orElse(null);
    }

}
