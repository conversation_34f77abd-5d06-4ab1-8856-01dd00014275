package com.sankuai.dzshoppingguide.product.detail.page.url;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.product.detail.gateway.spi.service.ProductDetailPageService;
import com.sankuai.dzshoppingguide.detail.page.url.api.request.ProductDetailPageUrlQueryRequest;
import com.sankuai.dzshoppingguide.detail.page.url.api.response.ProductDetailPageUrlQueryResponse;
import com.sankuai.dzshoppingguide.detail.page.url.api.service.ProductDetailPageUrlQueryService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 16:20
 */
//使用共享线程池
@MdpPigeonServer(
        serviceInterface = ProductDetailPageUrlQueryService.class,
        url = "com.sankuai.dzshoppingguide.ProductDetailPageUrlQueryService"
)
@Slf4j
public class ProductDetailPageUrlQueryServiceImpl implements ProductDetailPageUrlQueryService {
    @Override
    public ProductDetailPageUrlQueryResponse query(ProductDetailPageUrlQueryRequest request) {
        return null;
    }
}
