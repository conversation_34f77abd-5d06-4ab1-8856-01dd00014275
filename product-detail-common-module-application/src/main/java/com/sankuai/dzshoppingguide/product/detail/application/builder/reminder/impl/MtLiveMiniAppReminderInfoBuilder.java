package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 美团美播小程序
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/7 10:30
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER, moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class MtLiveMiniAppReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
        if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
            return null;
        }
        List<GuaranteeInstructionsContentVO> totalContents = Lists.newArrayList();
        ReminderInfoUtils.buildReminderInfo("7天无理由退款").ifPresent(totalContents::add);
        ReminderInfoUtils.buildReminderInfo("过期自动退").ifPresent(totalContents::add);
        totalContents.addAll(baseReminderInfo.getContents());
        baseReminderInfo.setContents(totalContents);
        return baseReminderInfo;
    }
}
