package com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.detail.dto.DealGroupTemplateDetailDTO;
import com.dianping.deal.detail.dto.MixedContent;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.HtmlUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailContent;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ContentType;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.MixedContentType;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 详情图片模块
 */
@Builder(
        moduleKey = ModuleKeyConstants.DETAIL_IMAGE,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductBaseInfoFetcher.class
        }
)
public class ReserveImageTextDetailModuleBuilder extends BaseVariableBuilder<ImageTextDetailVO> {
    private static final Logger log = LoggerFactory.getLogger(ReserveImageTextDetailModuleBuilder.class);

    @Override
    public ImageTextDetailVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (productAttr == null) {
            return new ImageTextDetailVO();
        }
        // 构建图文详情内容
        DealGroupTemplateDetailDTO templateDetailDTO = convertAttrToData(productAttr);
        if (templateDetailDTO == null) {
            return new ImageTextDetailVO();
        }
        List<ImageTextDetailContent> contents = buildMixedContent(templateDetailDTO);
        return buildDefaultDetailImage(contents);
    }

    /**
     * 构建最终反回图文详情对象
     *
     * @param contents
     * @return
     */
    private ImageTextDetailVO buildDefaultDetailImage(List<ImageTextDetailContent> contents) {
        ImageTextDetailVO imageTextDetailVO = new ImageTextDetailVO();
        imageTextDetailVO.setContents(contents);
        imageTextDetailVO.setFoldThreshold(contents.size() + 1);
        imageTextDetailVO.setFold(false);
        imageTextDetailVO.setTitle("图文详情");
        return imageTextDetailVO;
    }


    /**
     * 获取查询中心中商品详情数据->detail
     * https://watson.nibscp.test.sankuai.com/tools/scppp?bizLine=general&bizCode=nib.general.unified.book&version=2.0&activeTab=product&productId=1037184801
     *
     * @param productAttr
     * @return
     */
    private DealGroupTemplateDetailDTO convertAttrToData(ProductAttr productAttr) {
        DealGroupTemplateDetailDTO result = new DealGroupTemplateDetailDTO();
        try {
            String detail = productAttr.getSkuAttrFirstValue("detail");
            if (StringUtils.isBlank(detail)) {
                return result;
            }
            JSONArray jsonArray = JSON.parseArray(detail);
            List<MixedContent> contents = jsonArray.stream()
                    .map(obj -> parseMixedContent((JSONObject) obj))
                    .filter(content -> content != null)
                    .collect(Collectors.toList());
            result.setMixedContents(contents);
        } catch (Exception e) {
            log.error("Error converting attributes to data for productAttr: {}", productAttr, e);
        }
        return result;
    }

    /**
     * 解析商品详情数据
     * @param jsonObject
     * @return
     */
    private MixedContent parseMixedContent(JSONObject jsonObject) {
        if (jsonObject.containsKey("text")) {
            return new MixedContent() {{
                setType(MixedContentType.TEXT.name());
                setContent(jsonObject.getString("text"));
            }};
        } else if (jsonObject.containsKey("pic")) {
            return new MixedContent() {{
                setType(MixedContentType.IMAGE.name());
                setContent(generateFullPicUrl(jsonObject.getString("pic")));
            }};
        }
        return null;
    }


    /**
     * 复用生成完整的图片链接（不理解为什么不给完整链接，让我拼）
     * @param picUrl
     * @return
     */
    private static String generateFullPicUrl(String picUrl) {
        if (org.apache.commons.lang.StringUtils.isBlank(picUrl)) {
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        if (picUrl.startsWith("/")) {
            return "https://p0.meituan.net" + picUrl;
        }
        if (picUrl.startsWith("http://")) {
            return picUrl.replaceFirst("http://", "https://");
        }
        return picUrl;
    }


    /**
     * 构建图文详情内容
     * @param product
     * @return
     */
    private List<ImageTextDetailContent> buildMixedContent(DealGroupTemplateDetailDTO product) {
        if (CollectionUtils.isEmpty(product.getMixedContents())) {
            return Collections.emptyList();
        }
        return product.getMixedContents().stream()
                .flatMap(data -> {
                    List<ImageTextDetailContent> contentList = new ArrayList<>();
                    switch (MixedContentType.valueOf(data.getType())) {
                        case TEXT:
                            contentList.add(new ImageTextDetailContent(ContentType.TEXT.getType(), HtmlUtils.html2text(data.getContent())));
                            break;
                        case IMAGE:
                            if (StringUtils.isNotBlank(data.getTitle())) {
                                contentList.add(new ImageTextDetailContent(ContentType.TITLE.getType(), data.getTitle()));
                            }
                            if (StringUtils.isNotBlank(data.getDesc())) {
                                contentList.add(new ImageTextDetailContent(ContentType.TEXT.getType(), data.getDesc()));
                            }
                            if (StringUtils.isNotBlank(data.getContent())) {
                                contentList.add(new ImageTextDetailContent(ContentType.PIC.getType(), data.getContent()));
                            }
                            break;
                        default:
                            log.warn("Unsupported content type: {}", data.getType());
                    }
                    return contentList.stream();
                })
                .collect(Collectors.toList());
    }
}
