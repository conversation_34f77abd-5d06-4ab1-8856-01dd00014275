package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/30 16:33
 * @Description: 样式接口
 */
public interface Style<T> extends BaseComponent0 {

    /**
     * 构建样式详情
     */
    List<T> build(DealDetailBuildContext context);
}
