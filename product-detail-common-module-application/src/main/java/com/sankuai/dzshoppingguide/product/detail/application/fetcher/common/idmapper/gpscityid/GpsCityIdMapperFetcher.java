package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.gpscityid;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/13 11:24
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
@Slf4j
public class GpsCityIdMapperFetcher extends NormalFetcherContext<GpsCityIdMapper> {
    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    @Override
    protected CompletableFuture<GpsCityIdMapper> doFetch() {
        int gpsCityId = request.getGpsCityId();
        boolean mt = request.getClientTypeEnum().isMtClientType();
        int mtGpsCityId;
        int dpGpsCityId;
        if (mt) {
            mtGpsCityId = gpsCityId;
            dpGpsCityId = mapperCacheWrapper.fetchDpCityId(gpsCityId);
        } else {
            dpGpsCityId = gpsCityId;
            mtGpsCityId = mapperCacheWrapper.fetchMtCityId(gpsCityId);
        }
        return CompletableFuture.completedFuture(new GpsCityIdMapper(mtGpsCityId, dpGpsCityId));
    }
}
