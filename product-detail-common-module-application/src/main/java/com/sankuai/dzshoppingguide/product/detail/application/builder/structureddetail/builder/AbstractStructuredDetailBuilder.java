package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule.ProductReadjustPriceRule;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule.ProductReadjustPriceRuleFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.NewLineVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/30 16:38
 * @Description: TODO
 */
@Builder(
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                ProductStandardServiceProjectFetcher.class,
                AdditionInfoFetcher.class,
                ProductCategoryFetcher.class,
                SkuDefaultSelectFetcher.class,
                ProductReadjustPriceRuleFetcher.class
        },
        startFetcher = CommonModuleStarter.class
)
public abstract class AbstractStructuredDetailBuilder<T> extends BaseVariableBuilder<ModuleDetailStructuredDetailVO<T>> {

    @Override
    public ModuleDetailStructuredDetailVO<T> doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductServiceProject serviceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductStandardServiceProject standardServiceProject = getDependencyResult(ProductStandardServiceProjectFetcher.class);
        AdditionInfoResult additionInfo = getDependencyResult(AdditionInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        ProductReadjustPriceRule productReadjustPriceRule = getDependencyResult(ProductReadjustPriceRuleFetcher.class);
        if (Objects.isNull(productAttr) || Objects.isNull(serviceProject)) {
            return null;
        }

        DealDetailBuildContext context = DealDetailBuildContext.builder()
                .productAttr(productAttr)
                .productServiceProject(serviceProject)
                .productStandardServiceProject(standardServiceProject)
                .request(request)
                .additionInfo(additionInfo)
                .productCategory(productCategory)
                .skuDefaultSelect(skuDefaultSelect)
                .productReadjustPriceRule(productReadjustPriceRule)
                .build();

        List<T> details = Lists.newArrayList();
        buildStyles(details, context);
        ModuleDetailStructuredDetailVO<T> result = new ModuleDetailStructuredDetailVO<>();
        result.setDealDetails(details);
        return result;
    }

    protected void buildStyles(List<T> details, DealDetailBuildContext context) {
        for (Style<T> style : getStyles()) {
            List<T> styleDetails = style.build(context);
            if (CollectionUtils.isNotEmpty(styleDetails)) {
                details.addAll(styleDetails);
                if (style.needNewLine()) {
                    details.add(buildNewLine());
                }
            }
        }
    }

    protected abstract List<Style<T>> getStyles();

    private T buildNewLine() {
        return (T) NewLineVO.builder().type(ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType()).build();
    }
}
