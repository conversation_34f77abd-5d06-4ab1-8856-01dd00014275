package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2021/5/3 下午3:27
 */
@Data
public class PromoItemM {

    /**
     * 优惠的ID
     */
    private long promoId;

    /**
     * 优惠的类型Id
     */
    private int promoTypeCode;

    /**
     * 优惠类型，如：商家立减
     */
    private String promoType;

    /**
     * 优惠描述
     */
    private String desc;

    /**
     * 优惠金额标签
     */
    private String promoTag;

    /**
     * 优惠价格
     */
    private BigDecimal promoPrice;

    /**
     * 是否新客
     */
    private boolean isNewUser;

    /**
     * 优惠类型（新老客）https://km.sankuai.com/collabpage/1651440412#id-PromoDTO
     */
    private String promoNewOldIdentity;

    /**
     * 是否可领优惠
     */
    private boolean canAssign;

    /**
     * 使用的优惠类型列表，一条优惠条目可能是多个优惠融合而成, com.sankuai.dztheme.generalproduct.enums.PromoItemTypeEnum
     */
    private List<Integer> usedPromoTypes;

    /**
     * 券金额门槛,优惠弹窗使用
     */
    private String priceLimitDesc;

    /**
     * 券使用时间描述，优惠弹窗使用
     */
    private String useTimeDesc;

    /**
     * 优惠来源类型
     */
    private int sourceType;

    /**
     * 优惠折扣
     */
    private BigDecimal promoDiscount;

    /**
     * 优惠图标
     */
    private String icon;

    /**
     * 优惠标识，当数据源为价格服务时透传价格服务的标识，其他业务可自定义
     */
    private String promoIdentity;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 神券领用状态  NO_STATUS(1, "无领用状态"),ASSIGNED(2, "已领"),UN_ASSIGNED(3, "未领");
     */
    private Integer couponAssignStatus;

    /**
     * COUPON_PURCHASE(1, "领券购");
     */
    private Integer promoUseType;

    /**
     * 优惠展示类型
     */
    private String promoShowType;

    /**
     * 优惠名称，用于弹层
     */
    private String promoName;

    /**
     * 优惠解释性标签, 从营销查询优惠策略（commonTag）映射而来，
     * 枚举类参考：com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum
     */
    private List<Integer> promotionExplanatoryTags;

    /**
     * 透传的营销扩展信息
     * key枚举类：com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum
     */
    private Map<String,String> promotionOtherInfoMap;

    /**
     * 券id
     */
    private String couponId;

    /**
     * 优惠文案展示相关信息
     */
    private PromoItemTextM promoItemTextM;

    /**
     * 基础券金额（元）
     */
    private BigDecimal amount;

    /**
     * 券门槛金额（元）
     */
    private BigDecimal minConsumptionAmount;

    /**
     * 透传代理的营销展示文案扩展信息（角标文案）
     * key枚举参考：
     * com.sankuai.nibmkt.promotion.api.common.enums.PromotionTextEnum
     */
    private Map<String, String> promotionDisplayTextMap;

    /**
     * 优惠开始时间
     */
    private Date startTime;

    /**
     * 优惠结束时间
     */
    private Date endTime;

    /**
     * 优惠标题
     */
    private String couponTitle;
}
