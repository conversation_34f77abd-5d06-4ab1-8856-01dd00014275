package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

/**
 * <AUTHOR>
 * @date 2025/4/16 19:59
 */
public interface DentalThirdCategory {
    int TEETH_CLEANING = 27; // 洗牙
    int CHILD_TEETH_CLEANING = 157000; // 儿童洗牙
    int TEETH_FILLING = 29; // 补牙
    int CHILD_TEETH_FILLING = 845; // 儿童补牙
    int TEETH_EXTRACTION = 28; // 拔牙
    int MILK_TEETH_EXTRACTION = 843; // 乳牙拔除
    int PERIODONTAL_TREATMENT = 677; // 牙周治疗
    int TEETH_DESENSITIZATION = 157001; // 牙齿脱敏治疗
    int ROOT_CANAL_TREATMENT = 159000; // 根管/根尖治疗
    int CHILD_ROOT_CANAL_TREATMENT = 147004; // 儿童根管治疗
    int LOOSE_TEETH_FIXATION = 153001; // 松动牙固定
    int DENTAL_CROWN = 149002; // 牙冠修复
    int CHILD_DENTAL_CROWN = 158001; // 儿童牙冠修复
    int DENTURE = 151001; // 义齿/假牙
    int DENTAL_VENEER = 161000; // 牙贴面修复
    int FLUORIDE = 841; // 涂氟
    int PIT_AND_FISSURE_SEALANT = 842; // 窝沟封闭
    int TEETH_WHITENING = 25; // 美白
    int ORTHODONTICS = 207; // 矫正
    int EARLY_ORTHODONTICS = 263; // 早期矫治
    int ORAL_CARE_PRODUCTS = 145003; // 口腔护理用品
    int OTHER_DENTAL_TREATMENT = 154002; // 其他口腔治疗
}
