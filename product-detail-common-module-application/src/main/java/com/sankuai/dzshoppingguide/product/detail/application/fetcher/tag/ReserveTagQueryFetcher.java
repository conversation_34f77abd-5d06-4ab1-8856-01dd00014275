package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag;

import com.dianping.deal.tag.dto.MetaTagDTO;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeRequest;
import com.dianping.deal.tag.dto.MultiSubjectTagRelationJudge;
import com.dianping.deal.tag.enums.SubjectTagRelation;
import com.dianping.deal.tag.enums.SubjectTypeEnum;
import com.dianping.deal.tag.enums.TagSubject;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/17 15:42
 */
@Fetcher(
        timeout = 50000L,
        previousLayerDependencies = {CommonModuleStarter.class, ProductCategoryFetcher.class}
)
@Slf4j
public class ReserveTagQueryFetcher extends NormalFetcherContext<ReserveTagQueryResult> {
    public static List<Long> tagIds;
    static {
        Lion.addConfigListener(LionConstants.APP_KEY,"default", LionConstants.TAGID_CONFIG, configEvent -> parse(configEvent.getValue()));
        tagIds = Lion.getList(LionConstants.APP_KEY,  LionConstants.TAGID_CONFIG, Long.class, Lists.newArrayList());
    }

    public static void parse(String value){
        tagIds = Lion.getList(LionConstants.APP_KEY,  LionConstants.TAGID_CONFIG, Long.class, Lists.newArrayList());
    }

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ReserveTagQueryResult> doFetch() {
        try {
            if ( request.getProductTypeEnum() != ProductTypeEnum.RESERVE ) {
                return CompletableFuture.completedFuture(null);
            }

            // 判断spuType的类型是不是521(足疗预订)
            ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
            Integer secondCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
            if (secondCategoryId != 521) {
                return CompletableFuture.completedFuture(null);
            }

            if (CollectionUtils.isEmpty(tagIds)) {
                return CompletableFuture.completedFuture(null);
            }

            MultiSubjectTagBatchJudgeRequest tagQueryRequest = new MultiSubjectTagBatchJudgeRequest();
            List<SubjectTagRelation> tagRelations = tagIds.stream().map(tagId -> {
                SubjectTagRelation subjectTagRelation = new SubjectTagRelation();
                subjectTagRelation.setTagId(tagId);
                TagSubject tagSubject = new TagSubject();
                tagSubject.setSubjectType(SubjectTypeEnum.PRODUCT.getCode());
                tagSubject.setProductId(request.getProductId());
                subjectTagRelation.setTagSubject(tagSubject);
                return subjectTagRelation;
            }).collect(Collectors.toList());
            tagQueryRequest.setSubjectTagRelations(tagRelations);

            return compositeAtomService.queryTagIdByProductId(tagQueryRequest).thenCompose(res -> {
                if ( res == null || !res.isSuccess() || CollectionUtils.isEmpty(res.getJudgeResult()) ) {
                    return CompletableFuture.completedFuture(null);
                }
                MultiSubjectTagRelationJudge result = res.getJudgeResult().stream().filter(MultiSubjectTagRelationJudge::getJudgeResult).findFirst().orElse(null);
                if ( result == null ) {
                    return CompletableFuture.completedFuture(null);
                }
                return compositeAtomService.queryTagNameByTagIdAsync(Lists.newArrayList(result.getTagId()));
            }).thenApply(res -> {
                if ( res == null || CollectionUtils.isEmpty(res) ) {
                    return null;
                }
                MetaTagDTO metaTagDTO = (MetaTagDTO) res.get(0);
                return new ReserveTagQueryResult(metaTagDTO);
            });
        } catch (Exception e) {
            log.error("TagQueryFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }
}
