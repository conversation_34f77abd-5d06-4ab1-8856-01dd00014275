package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.dto.DentalProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.dto.EyesProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/17 11:45
 */
@Slf4j
public class ServiceProcessUtils {

    public static void setOrder(List<DealDetailStructuredDetailVO> result) {
        List<DealDetailStructuredDetailVO> orderList = result.stream().filter(Objects::nonNull).filter(item -> item.getType() == ViewComponentTypeEnum.NORMAL_TEXT.getType()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        result.forEach(item -> {
            if (item.getType() == ViewComponentTypeEnum.NORMAL_TEXT.getType()) {
                item.setOrder(orderList.indexOf(item) + 1);
            }
        });
    }

    public static List<EyesProcessDTO> parseProcess(List<String> servicesProcessStr) {
        try {
            if (CollectionUtils.isEmpty(servicesProcessStr)) {
                return Collections.emptyList();
            }
            return servicesProcessStr.stream().filter(StringUtils::isNotBlank).map(process -> JSON.parseObject(process, new TypeReference<EyesProcessDTO>() {})).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("parseProcess error,process:{}", JsonCodec.encode(servicesProcessStr),e);
        }
        return Collections.emptyList();
    }

    public static List<DentalProcessDTO> parseDentalProcess(List<String> servicesProcessStr) {
        try {
            if (CollectionUtils.isEmpty(servicesProcessStr)) {
                return Collections.emptyList();
            }
            return servicesProcessStr.stream().filter(StringUtils::isNotBlank).map(process -> JSON.parseObject(process, new TypeReference<DentalProcessDTO>() {})).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("parseProcess error,process:{}", JsonCodec.encode(servicesProcessStr),e);
        }
        return Collections.emptyList();
    }

    public static int parseDuration(String duration) {
        if (StringUtils.isBlank(duration)) {
            return 0;
        }
        try {
            // 先转换为 double，再转为 int
            return (int) Double.parseDouble(duration);
        } catch (NumberFormatException e) {
            log.warn("parseDuration error, duration:{}", duration, e);
            return 0;
        }
    }
}
