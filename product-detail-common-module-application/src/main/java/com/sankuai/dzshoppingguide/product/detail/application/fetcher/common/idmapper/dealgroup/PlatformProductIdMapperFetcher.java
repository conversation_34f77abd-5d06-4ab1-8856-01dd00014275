package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 团购业务ID到平台商品ID映射
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
public class PlatformProductIdMapperFetcher extends NormalFetcherContext<PlatformProductIdMapper> {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<PlatformProductIdMapper> doFetch() {
        if (request.getClientTypeEnum().isMtClientType()) {
            return CompletableFuture.completedFuture(new PlatformProductIdMapper(request.getProductId()));
        }
        BizProductIdConvertRequest bizProductIdConvertRequest = buildRequest(request);
        return compositeAtomService.batchPlatformProductId(bizProductIdConvertRequest).thenApply(result -> {
            if (MapUtils.isEmpty(result)) {
                return new PlatformProductIdMapper(0L);
            }
            Long platformProductId = result.getOrDefault(request.getProductId(), 0L);
            return new PlatformProductIdMapper(platformProductId);
        });
    }

    private BizProductIdConvertRequest buildRequest(ProductDetailPageRequest pageRequest) {
        BizProductIdConvertRequest request = new BizProductIdConvertRequest();
        request.setBizProductIds(Collections.singletonList(pageRequest.getProductId()));
        request.setBizProductIdType(BizProductIdType.DP_DEAL_GROUP_ID);
        return request;
    }
}
