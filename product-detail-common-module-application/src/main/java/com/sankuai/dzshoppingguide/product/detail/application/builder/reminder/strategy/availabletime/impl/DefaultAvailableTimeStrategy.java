package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FetcherResultEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.ALL_DAY;
import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.PARTIAL_TIME;

@Service
public class DefaultAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(FetcherResultDTO fetcherResults) {

        ProductBaseInfo baseInfo = fetcherResults.getProductBaseInfo();
        ProductAttr attr = fetcherResults.getProductAttr();
        List<AttrDTO> attrDTOS = Optional.ofNullable(attr).map(ProductAttr::getSkuAttrList).orElse(Lists.newArrayList());
        int categoryId = fetcherResults.getProductSecondCategoryId();


        if (Objects.isNull(baseInfo) || Objects.isNull(baseInfo.getRule())
                || Objects.isNull((baseInfo.getRule().getUseRule()))) {
            return StringUtils.EMPTY;
        }
        List<String> timesAvailableAll = DealAttrHelper.getAttributeValues(attrDTOS, DealAttrKeys.TIMES_AVAILABLE_ALL);
        if ( LionConfigUtils.hitCustomAvailableTimeOfDays(categoryId)
                && CollectionUtils.isNotEmpty(timesAvailableAll)) {
            // 针对茶馆行业
            if (timesAvailableAll.contains("否")) {
                return PARTIAL_TIME;
            }
        }
        DealGroupUseRuleDTO useRuleDTO = baseInfo.getRule().getUseRule();
        if (Objects.isNull(useRuleDTO.getAvailableDate())
                || CollectionUtils.isEmpty(useRuleDTO.getAvailableDate().getCycleAvailableDateList())) {
            return StringUtils.EMPTY;
        }

        CycleAvailableDateDTO cycleAvailableDateDTO = useRuleDTO.getAvailableDate().getCycleAvailableDateList().get(0);
        if (CollectionUtils.isEmpty(cycleAvailableDateDTO.getAvailableTimeRangePerDay())) {
            return StringUtils.EMPTY;
        }

        DateRangeDTO dateRangeDTO = cycleAvailableDateDTO.getAvailableTimeRangePerDay().get(0);
        if (Objects.isNull(dateRangeDTO) || StringUtils.isBlank(dateRangeDTO.getFrom())
                || StringUtils.isBlank(dateRangeDTO.getTo())) {
            return StringUtils.EMPTY;
        }

        if (dateRangeDTO.getFrom().length() > 11 && dateRangeDTO.getTo().length() > 11) {
            if ("00:00:00".equals(dateRangeDTO.getFrom().substring(11))
                    && "23:59:00".equals(dateRangeDTO.getTo().substring(11))) {
                return ALL_DAY;
            } else {
                return PARTIAL_TIME;
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.DEFAULT_STRATEGY;
    }
}