package com.sankuai.dzshoppingguide.product.detail.application.utils.jsonpath;


import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.ValueOperationTypeEnum;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValueTransformer {
    // 转换操作正则表达式（支持带引号参数）
    private static final Pattern TRANSFORM_PATTERN =
            Pattern.compile("^(\\w+)\\(([\"']?)(.*?)\\2\\)$");

    public static Object applyTransform(Object value, String transformRule) {
        if (transformRule == null || transformRule.isEmpty()) {
            return value;
        }

        Matcher matcher = TRANSFORM_PATTERN.matcher(transformRule);
        if (!matcher.find()) {
            throw new IllegalArgumentException("Invalid transform rule format: " + transformRule);
        }

        String operation = matcher.group(1).toLowerCase();
        ValueOperationTypeEnum operationTypeEnum = ValueOperationTypeEnum.getByType(operation);
        String arg = matcher.group(3);

        switch (operationTypeEnum) {
            case CONCAT:
                return handleConcat(value, arg);
            case DATE_FORMAT:
                return handleDateFormat(value, arg);
            default:
                throw new UnsupportedOperationException("Unsupported transform operation: " + operation);
        }
    }

    private static String handleConcat(Object value, String suffix) {
        if (value == null) return "";
        return value.toString() + suffix;
    }

    private static String handleDateFormat(Object value, String pattern) {
        if (!(value instanceof LocalDateTime)) {
            throw new IllegalArgumentException(
                    "Date format requires LocalDateTime input, got: " +
                            (value != null ? value.getClass().getSimpleName() : "null")
            );
        }
        return ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(pattern));
    }
}
