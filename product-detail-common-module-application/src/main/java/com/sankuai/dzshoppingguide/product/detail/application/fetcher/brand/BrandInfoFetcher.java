package com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.meituan.shangou.standardquery.thrift.command.ListSgBrandByIdsCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Fetcher(
        previousLayerDependencies = {ProductAttrFetcher.class}
)
@Slf4j
public class BrandInfoFetcher extends NormalFetcherContext<BrandInfo> {



    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<BrandInfo> doFetch() throws Exception {
        Optional<ProductAttr> productAttrOpt = getDependencyResult(ProductAttrFetcher.class, ProductAttr.class);

        if (!productAttrOpt.isPresent()) {
            return CompletableFuture.completedFuture(null);
        }

        ProductAttr productAttr = productAttrOpt.get();

        String brandIdStr = productAttr.getSkuAttrFirstValue(Constants.BRAND_ID);

        if (StringUtils.isBlank(brandIdStr)) {
            return CompletableFuture.completedFuture(null);
        }

        long brandId = NumberUtils.toLong(brandIdStr, 0);

        if (brandId <= 0) {
            return CompletableFuture.completedFuture(null);
        }

        return compositeAtomService.queryBrandInfoByBrandId(buildCommand(brandId)).thenApply(res -> {
            if (res == null || StringUtils.isBlank(res.getZhName())) {
                return null;
            }

            return new BrandInfo(res.getZhName());
        });
    }

    private ListSgBrandByIdsCommand buildCommand(long brandId) {
        ListSgBrandByIdsCommand command = new ListSgBrandByIdsCommand();
        command.setIdList(Lists.newArrayList(brandId));
        return command;
    }
}
