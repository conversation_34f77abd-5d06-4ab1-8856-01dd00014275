package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule;

import com.dianping.joy.booking.shop.api.MassageShopService;
import com.dianping.joy.booking.shop.api.dto.massage.MassageShopConfig;
import com.dianping.joy.booking.shop.api.dto.massage.PeriodStock;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateAndTimeUtils;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.technician.trade.api.schedule.dto.DayOfWeekWorkTime;
import com.sankuai.technician.trade.api.schedule.dto.TechScheduleConfigDTO;
import com.sankuai.technician.trade.api.schedule.meta.WorkTimeInterval;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/10 15:40
 */
@Fetcher(
        previousLayerDependencies = {ShopIdMapperFetcher.class, TechScheduleConfigFetcher.class, TechStockWhiteShopFetcher.class, ShopInfoFetcher.class}
)
@Slf4j
public class ShopScheduleTimeFetcher extends NormalFetcherContext<ShopScheduleTimeResult> {
    @RpcClient(url = "com.dianping.joy.booking.shop.api.MassageShopService")
    private MassageShopService massageShopServiceFuture;

    private static final long EIGHT_HOUR_MS = 8 * 3600 * 1000;
    private static final CacheClientConfig CACHE_CONFIG = new CacheClientConfig("redis-vc", "master-slave", 200);

    private static final String cacheCategory =  "dzp_massage_book_shop_config";

    private static final int expiredHour = 1;
    @Override
    protected CompletableFuture<ShopScheduleTimeResult> doFetch() {
        try {
            if (request.getProductTypeEnum() != ProductTypeEnum.RESERVE) {
                return CompletableFuture.completedFuture(null);
            }
            ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
            int shopCategoryId = Optional.ofNullable(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getMainCategoryId).orElse(0);

            TechStockWhiteShopResult techStockWhiteShopResult = getDependencyResult(TechStockWhiteShopFetcher.class);
            Boolean isThirtyTechStockShop = Optional.ofNullable(techStockWhiteShopResult).map(TechStockWhiteShopResult::isWhiteList).orElse(false);

            TechScheduleConfig techScheduleConfig = getDependencyResult(TechScheduleConfigFetcher.class);
            List<TechScheduleConfigDTO> configs = Optional.ofNullable(techScheduleConfig).map(TechScheduleConfig::getTechScheduleConfigDTOList).orElse(Lists.newArrayList());

            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            Long dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);

            CompletableFuture<MassageShopConfig> massageShopConfigFuture = AthenaInf.getRpcCompletableFuture(massageShopServiceFuture.loadByShopIdL(dpShopId)).exceptionally(e -> {
                log.error(XMDLogFormat.build()
                        .putTag("method", "massageShopServiceFuture.loadByShopIdL")
                        .message(String.format("massageShopServiceFuture.loadByShopIdL error, dpShopId : %s", dpShopId)));
                return null;
            });

            // 这个地方的categoryId指的是商户分类
            CacheKey cacheKey = new CacheKey(cacheCategory, String.format("%d-%d", dpShopId, shopCategoryId));
            CacheClient cacheClient = AthenaInf.getCacheClient(CACHE_CONFIG);
            return cacheClient.asyncGetReadThrough(cacheKey, new TypeReference<MassageShopConfig>() {
                    },
                    e -> massageShopConfigFuture, expiredHour * 3600, 0).thenApply(res -> {
                if ( res == null ) {
                    return null;
                }
                ShopScheduleTimeResult result = new ShopScheduleTimeResult();
                // 获取门店今日排班开始时间
                result.setTodayScheduleStartTime(getTodayScheduleStartTime(res));
                // 获取门店今日排班结束时间
                result.setTodayScheduleEndTime(getTodayScheduleEndTime(res));


                if ( isThirtyTechStockShop && CollectionUtils.isNotEmpty(configs)) {
                    result.setTodayScheduleStartTime(getTechShopTodayScheduleStartTime(configs));
                    result.setTodayScheduleEndTime(getTechShopTodayScheduleEndTime(configs));
                }
                return result;
            });


        } catch (Exception e) {
            log.error("MassageShopConfigFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private long getTechShopTodayScheduleStartTime(List<TechScheduleConfigDTO> configs) {
        if (configs == null || CollectionUtils.isEmpty(configs)) {
            return 0;
        }
        // 此处需要获取昨日和今日的排班，根据用户访问时间来选择需要的排班日期，例如商家每天的排班都是10-次日6点，用户凌晨1点访问，应该获取昨日的排班
        // 星期一-七对应1-7
        int todayWeekNum = DateAndTimeUtils.convertWeekDay();
        int lastDayWeekNum = todayWeekNum - 1 > 0 ? todayWeekNum - 1 : 7;
        long now = new Date().getTime();

        // 获取所有技师昨天排班时间的最早时间 和 最晚时间
        long yesterdayEarliestTime = getDayEarliestTime(configs, lastDayWeekNum) - 7 * 24 * 3600 * 1000;
        long yesterdayEndTime = getDayEndTime(configs, lastDayWeekNum)  - 7 * 24 * 3600 * 1000;
        // 获取所有技师今天排班时间的最早时间
        long todayEarliestTime = getDayEarliestTime(configs, todayWeekNum);

        return now > yesterdayEndTime ? todayEarliestTime : yesterdayEarliestTime;
    }

    private long getTechShopTodayScheduleEndTime(List<TechScheduleConfigDTO> configs) {
        if (configs == null || CollectionUtils.isEmpty(configs)) {
            return 0;
        }
        // 此处需要获取昨日和今日的排班，根据用户访问时间来选择需要的排班日期，例如商家每天的排班都是10-次日6点，用户凌晨1点访问，应该获取昨日的排班
        // 星期一-七对应1-7
        int todayWeekNum = DateAndTimeUtils.convertWeekDay();
        int lastDayWeekNum = todayWeekNum - 1 > 0 ? todayWeekNum - 1 : 7;
        long now = new Date().getTime();

        // 获取所有技师昨天排班时间的最晚时间
        long yesterdayEndTime = getDayEndTime(configs, lastDayWeekNum) - 7 * 24 * 3600 * 1000;
        // 获取所有技师今天排班时间的最晚时间
        long todayEndTime = getDayEndTime(configs, todayWeekNum);

        return now > yesterdayEndTime ? todayEndTime : yesterdayEndTime;
    }

    private long getDayEarliestTime(List<TechScheduleConfigDTO> configs, int week) {
        long defaultEarliestTime = DateAndTimeUtils.convertZeroLongTime(0);
        if (CollectionUtils.isEmpty(configs) || week <= 0 || week > 7) {
            return defaultEarliestTime;
        }
        return configs.stream().filter(Objects::nonNull).map(e -> getSingleTechDayStartTime(e, week)).min(Comparator.naturalOrder()).orElse(defaultEarliestTime);
    }

    private long getDayEndTime(List<TechScheduleConfigDTO> configs, int week) {
        long defaultEndTime = DateAndTimeUtils.convertZeroLongTime(1);
        if (CollectionUtils.isEmpty(configs) || week <= 0 || week > 7) {
            return defaultEndTime;
        }
        return configs.stream().filter(Objects::nonNull).map(e -> getSingleTechDayEndTime(e, week)).max(Comparator.naturalOrder()).orElse(defaultEndTime);
    }

    private long getSingleTechDayStartTime(TechScheduleConfigDTO techScheduleConfigDTO, int week) {
        if (CollectionUtils.isEmpty(techScheduleConfigDTO.getDayOfWeekWorkTimeList())) {
            return DateAndTimeUtils.convertZeroLongTime(0);
        }
        List<DayOfWeekWorkTime> dayOfWeekWorkTimeList = techScheduleConfigDTO.getDayOfWeekWorkTimeList();
        List<WorkTimeInterval> workTimeIntervals = dayOfWeekWorkTimeList.stream().filter(e -> e.getDayOfWeek() == week).map(DayOfWeekWorkTime::getDayWorkTime).findFirst().orElse(null);
        if (CollectionUtils.isEmpty(workTimeIntervals)) {
            return DateAndTimeUtils.convertZeroLongTime(0);
        }
        WorkTimeInterval workTimeInterval = workTimeIntervals.get(0);
        return convertToDate(workTimeInterval.getBeginHour(), workTimeInterval.getBeginMinute(), week, workTimeInterval.isBelongToPreviousDay()).getTime();
    }

    private long getSingleTechDayEndTime(TechScheduleConfigDTO techScheduleConfigDTO, int week) {
        if (CollectionUtils.isEmpty(techScheduleConfigDTO.getDayOfWeekWorkTimeList())) {
            return DateAndTimeUtils.convertZeroLongTime(1);
        }
        List<DayOfWeekWorkTime> dayOfWeekWorkTimeList = techScheduleConfigDTO.getDayOfWeekWorkTimeList();
        List<WorkTimeInterval> workTimeIntervals = dayOfWeekWorkTimeList.stream().filter(e -> e.getDayOfWeek() == week).map(DayOfWeekWorkTime::getDayWorkTime).findFirst().orElse(null);
        if (CollectionUtils.isEmpty(workTimeIntervals)) {
            return DateAndTimeUtils.convertZeroLongTime(1);
        }
        WorkTimeInterval workTimeInterval = workTimeIntervals.get(workTimeIntervals.size() - 1);
        return convertToDate(workTimeInterval.getEndHour(), workTimeInterval.getEndMinute(), week, workTimeInterval.isBelongToPreviousDay()).getTime();
    }

    public Date convertToDate(int hour, int minute, int week, boolean acrossDay) {
        long weekToDateStartTime = convertWeekToDateStartTime(week, true);
        hour = acrossDay ? hour + 24 : hour;
        long time = weekToDateStartTime + (hour * 60L + minute) * 60 * 1000;
        return new Date(time);
    }

    /**
     * 将"周几"转化为将来最近的日期0点时间
     */
    public long convertWeekToDateStartTime(int week, boolean before) {
        Calendar calendar = Calendar.getInstance();
        int today = calendar.get(Calendar.DAY_OF_WEEK) > 1 ? calendar.get(Calendar.DAY_OF_WEEK) - 1 : 7;
        int days = week - today;
        days = before ? days : (days < 0 ? days + 7 : days);
        calendar.add(Calendar.DAY_OF_YEAR, days);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    public long getTodayScheduleStartTime(MassageShopConfig massageShopConfig) {
        if (massageShopConfig == null || org.apache.commons.collections4.MapUtils.isEmpty(massageShopConfig.getWeekPeriodStockMap())) {
            return 0;
        }
        // 此处需要获取昨日和今日的排班，根据用户访问时间来选择需要的排班日期，例如商家每天的排班都是10-次日6点，用户凌晨1点访问，应该获取昨日的排班
        int todayWeekNum = DateAndTimeUtils.convertWeekDay();
        int lastDayWeekNum = todayWeekNum - 1 > 0 ? todayWeekNum - 1 : 7;
        long now = new Date().getTime();
        List<PeriodStock> lastDatPeriodStocks = massageShopConfig.getWeekPeriodStockMap().get(lastDayWeekNum);
        if ( CollectionUtils.isEmpty(lastDatPeriodStocks)) {
            return getaStartTime(massageShopConfig, todayWeekNum);
        }
        long lastDayScheduleEndTime = lastDatPeriodStocks.stream()
                .filter(e -> e.getPeriod() != null && e.getPeriod().getEnd() != null)
                .map(e -> e.getPeriod().getEnd().getTime()).max(Comparator.naturalOrder()).orElse(0L);
        long lastDayShopEndTime = DateAndTimeUtils.convertZeroLongTime(-1) + lastDayScheduleEndTime + EIGHT_HOUR_MS;

        if (now > lastDayShopEndTime) {
            return getaStartTime(massageShopConfig, todayWeekNum);
        }
        // 否则取昨日排班开始时间
        long lastDayScheduleStartTime = lastDatPeriodStocks.stream()
                .filter(e -> e.getPeriod() != null && e.getPeriod().getBegin() != null)
                .map(e -> e.getPeriod().getBegin().getTime()).min(Comparator.naturalOrder()).orElse(0L);
        return DateAndTimeUtils.convertZeroLongTime(-1) + lastDayScheduleStartTime + EIGHT_HOUR_MS;
    }

    public long getTodayScheduleEndTime(MassageShopConfig massageShopConfig) {
        if (massageShopConfig == null || org.apache.commons.collections4.MapUtils.isEmpty(massageShopConfig.getWeekPeriodStockMap())) {
            return 0;
        }
        // 此处需要获取昨日和今日的排班，根据用户访问时间来选择需要的排班日期，例如商家每天的排班都是10-次日6点，用户凌晨1点访问，应该获取昨日的排班
        int todayWeekNum = DateAndTimeUtils.convertWeekDay();
        int lastDayWeekNum = todayWeekNum - 1 > 0 ? todayWeekNum - 1 : 7;
        long now = new Date().getTime();
        List<PeriodStock> lastDatPeriodStocks = massageShopConfig.getWeekPeriodStockMap().get(lastDayWeekNum);
        if (CollectionUtils.isEmpty(lastDatPeriodStocks)) {
            return getEndTime(massageShopConfig, todayWeekNum);
        }
        long lastDayScheduleEndTime = lastDatPeriodStocks.stream()
                .filter(e -> e.getPeriod() != null && e.getPeriod().getEnd() != null)
                .map(e -> e.getPeriod().getEnd().getTime()).max(Comparator.naturalOrder()).orElse(0L);
        long lastDayShopEndTime = DateAndTimeUtils.convertZeroLongTime(-1) + lastDayScheduleEndTime + EIGHT_HOUR_MS;

        if (now > lastDayShopEndTime) {
            // 用户访问时间过了昨日排班结束时间，取今天排班结束时间
            return getEndTime(massageShopConfig, todayWeekNum);
        }
        // 否则取昨日排班结束时间
        return lastDayShopEndTime;
    }

    private Long getEndTime(MassageShopConfig res, int todayWeekNum) {
        List<PeriodStock> todayPeriodStocks = res.getWeekPeriodStockMap().get(todayWeekNum);
        if (CollectionUtils.isEmpty(todayPeriodStocks)) {
            return 0L;
        }
        long scheduleEndTime = todayPeriodStocks.stream()
                .filter(e -> e.getPeriod() != null && e.getPeriod().getEnd() != null)
                .map(e -> e.getPeriod().getEnd().getTime()).max(Comparator.naturalOrder()).orElse(0L);
        return DateAndTimeUtils.convertZeroLongTime(0) + scheduleEndTime + EIGHT_HOUR_MS;
    }

    private Long getaStartTime(MassageShopConfig res, int todayWeekNum) {
        // 用户访问时间过了昨日排班结束时间，取今天排班开始时间
        List<PeriodStock> todayPeriodStocks = res.getWeekPeriodStockMap().get(todayWeekNum);
        if (CollectionUtils.isEmpty(todayPeriodStocks)) {
            return 0L;
        }
        long scheduleStartTime = todayPeriodStocks.stream()
                .filter(e -> e.getPeriod() != null && e.getPeriod().getBegin() != null)
                .map(e -> e.getPeriod().getBegin().getTime()).min(Comparator.naturalOrder()).orElse(0L);
        return DateAndTimeUtils.convertZeroLongTime(0) + scheduleStartTime + EIGHT_HOUR_MS;
    }
}
