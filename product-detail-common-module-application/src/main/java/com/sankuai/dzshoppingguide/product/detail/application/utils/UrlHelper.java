package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CollectedProductUrlConfig;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

@Log4j
public class UrlHelper {

    // 获取商户地址
    public static String getShopUrl(ProductDetailPageRequest request, long dpShopId, long mtShopId) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/gc/poi/detail?id=%s", mtShopId);
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://shopinfo?shopid=%s", dpShopId);
        }
        return null;
    }

    // 跳转首页
    public static String getHomeUrl(ProductDetailPageRequest request) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/home");
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://home");
        }
        return null;
    }

    public static String getShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper)  {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getMtBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId +  "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getDpBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/dianping.html?url=dianping%3A%2F%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId + "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        return null;
    }

    public static String getCollectedProductDetailUrl(String productKey, String productId, String shopId, String appVersion) {
        CollectedProductUrlConfig productUrlConfig = getCollectedProductUrlConfig(productKey);
        if (Objects.isNull(productUrlConfig)) {
            return StringUtils.EMPTY;
        }
        String minAppVersion = productUrlConfig.getAppVersion();
        if (VersionUtils.isGreaterThanOrEqual(appVersion, minAppVersion)) {
            return String.format(productUrlConfig.getProductDetailUrl(), shopId, productId);
        }
        return String.format(productUrlConfig.getOldProductDetailUrl(), shopId, productId);
    }

    public static String getCollectedShopUrl(String productKey, String shopId) {
        CollectedProductUrlConfig productUrlConfig = getCollectedProductUrlConfig(productKey);
        if (Objects.isNull(productUrlConfig)) {
            return StringUtils.EMPTY;
        }
        return String.format(productUrlConfig.getShopUrl(), shopId);
    }

    private static CollectedProductUrlConfig getCollectedProductUrlConfig(String key) {
        Map<String, CollectedProductUrlConfig> config = LionConfigUtils.getCollectedProductUrlConfig();
        return config.get(key);
    }

    public static String getGoodReviewUrl(long dealId, ClientTypeEnum clientType) {

        if (Objects.equals(ClientTypeEnum.DP_APP, clientType)) {
            return String.format("dianping://review?referid=%s&refertype=1", dealId);
        }
        if (Objects.equals(ClientTypeEnum.MT_APP, clientType)) {
            return String.format("imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s", dealId);
        }
        return StringUtils.EMPTY;
    }
}
