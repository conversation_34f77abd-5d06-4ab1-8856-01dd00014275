package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-14
 * @desc 团购详情数据源定义
 */
@Getter
public enum StructuredDetailDataSourceEnum {
    PRODUCT_ATTR("productAttr", "商品属性"),
    SERVICE_PROJECT("serviceProject", "服务项目属性"),
    ;

    private final String code;
    private final String desc;

    StructuredDetailDataSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
