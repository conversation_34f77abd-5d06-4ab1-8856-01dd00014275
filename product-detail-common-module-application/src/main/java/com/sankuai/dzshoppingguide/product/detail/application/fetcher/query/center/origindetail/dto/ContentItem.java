package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-26
 * @desc
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = RichTextContent.class, name = "richtext"),
        @JsonSubTypes.Type(value = ImageListContent.class, name = "imageList")
})
public abstract class ContentItem implements Serializable {
}
