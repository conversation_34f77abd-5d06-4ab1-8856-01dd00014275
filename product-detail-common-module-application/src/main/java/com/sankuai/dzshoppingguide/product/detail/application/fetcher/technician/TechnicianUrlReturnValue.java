package com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TechnicianUrlReturnValue extends FetcherReturnValueDTO {
    Map<Integer, String> urlMap;
}
