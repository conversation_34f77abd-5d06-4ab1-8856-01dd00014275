package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/16
 */
@Slf4j
public class DealSkuUtils {

    public static List<DealGroupDealDTO> getDigitalApplianceDealSkuList(List<DealGroupDealDTO> dealGroupDealDTOS) {
        return dealGroupDealDTOS.stream()
                .filter(Objects::nonNull)
                .filter(deal -> Objects.nonNull(deal.getBasic()) && StringUtils.isNotBlank(deal.getBasic().getTitle()))
                // 过滤掉无效的skuId
                .filter(deal -> deal.getBasic().getStatus() == 1)
                .collect(Collectors.toList());
    }

    public static List<DealGroupDealDTO> getValidDealSkuList(List<DealGroupDealDTO> dealGroupDealDTOS) {
        return dealGroupDealDTOS.stream()
                // 过滤掉没有销售属性的skuId
                .filter(d -> CollectionUtils.isNotEmpty(d.getAttrs()))
                // 过滤掉没有标题的skuId
                .filter(d -> Objects.nonNull(d.getBasic()) && StringUtils.isNotBlank(d.getBasic().getTitle()))
                // 过滤掉无效的skuId
                .filter(d -> d.getBasic().getStatus() == 1)
                // 过滤非分时段定价skuId
                .filter(d -> hitMultiSku(d.getAttrs())).collect(Collectors.toList());
    }

    private static boolean hitMultiSku(List<AttrDTO> attrs) {
        AttrDTO attrDTO = attrs.stream().filter(a -> Objects.equals(a.getName(), Constants.SELL_DIFFERENT_GRADES))
//                && Objects.equals(a.getValue(), Constants.TIME_SEGMENT_PRICING)
//                .filter(a -> Objects.equals(a.getName(), Constants.SEGMENT_TYPE)
//                && (Objects.equals(a.getValue(), Constants.DAY_NIGHT_RANGE) || (Objects.equals(a.getValue(), Constants.WEEKDAY_WEEKEND_RANGE))))
                .findFirst().orElse(null);
        return Objects.nonNull(attrDTO);
    }

}
