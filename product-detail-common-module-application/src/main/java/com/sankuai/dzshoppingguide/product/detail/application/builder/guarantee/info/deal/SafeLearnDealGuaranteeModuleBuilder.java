package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FloatingLayerOpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.FeaturesLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-20
 * @desc 安心学保障模块构建器
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class
        }
)
public class SafeLearnDealGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

    @Override
    public ProductDetailGuaranteeVO doBuild() {
        // 1. 保障条
        List<GuaranteeInstructionsContentVO> contents = buildSafeLearnContents();
        // 2. 保障浮层
        FeaturesLayer floatingLayer = buildSafeLearnFloatingLayer();
        return new ProductDetailGuaranteeVO("保障", contents, floatingLayer, FloatingLayerOpenTypeEnum.JUMP_TO_DETAIL);
    }

    private FeaturesLayer buildSafeLearnFloatingLayer() {
        LayerConfig layerConfig = new LayerConfig();
        layerConfig.setJumpUrl(LionConfigUtils.getAnXinXueDetailPage(request.getClientTypeEnum().isMtClientType()));
        FeaturesLayer floatingLayer = new FeaturesLayer();
        floatingLayer.setLayerConfigs(Collections.singletonList(layerConfig));
        return floatingLayer;
    }

    private List<GuaranteeInstructionsContentVO> buildSafeLearnContents() {
        GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
        content.setText("线上购课 · 按次扣费 · 安心退款");
        Icon icon = new Icon();
        icon.setIcon("https://p0.meituan.net/ingee/f21b4b5a0ce92992a8911a4bfbfa1e6f2519.png");
        icon.setIconHeight(12);
        icon.setIconWidth(48);
        content.setPrefixIcon(icon);
        content.setFontColor("#8E3C12");
        return Collections.singletonList(content);
    }
}
