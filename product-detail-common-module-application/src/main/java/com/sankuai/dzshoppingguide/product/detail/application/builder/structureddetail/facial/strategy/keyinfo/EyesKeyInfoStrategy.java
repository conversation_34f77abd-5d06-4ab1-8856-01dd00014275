package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.keyinfo;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.EyesThirdCategory;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.EyesAttrUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 眼睛
 * <AUTHOR>
 * @date 2025/4/14 20:03
 */
@Component
public class EyesKeyInfoStrategy extends AbstractSubModuleBuildStrategy {

    @Override
    public String getSubModuleName() {
        return SubModuleKey.KEY_INFO;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        return categoryId == SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId();
    }

    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> content = Lists.newArrayList();
        ProductCategory productCategory = context.getProductCategory();
        int thirdCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null) {
            return Collections.emptyList();
        }
        Long availableShopCount = context.getAvailableShopCount();
        switch (thirdCategoryId) {
            case EyesThirdCategory.CHILD_MEDICAL_OPTICAL:
            case EyesThirdCategory.CONTACT_LENS_FITTING:
                buildOptical(productAttr, availableShopCount).ifPresent(content::add);
                buildOpticalData(productAttr).ifPresent(content::add);
                buildApplicableCrowd(productAttr).ifPresent(content::add);
                break;
            case EyesThirdCategory.ADULT_MEDICAL_OPTICAL:
                buildOptical(productAttr, availableShopCount).ifPresent(content::add);
                buildOpticalData(productAttr).ifPresent(content::add);
                break;
            case EyesThirdCategory.VISUAL_TRAINING:
                buildOptical(productAttr, availableShopCount).ifPresent(content::add);
                buildApplicableCrowd(productAttr).ifPresent(content::add);
                break;
            case EyesThirdCategory.SMILE:
            case EyesThirdCategory.FEMTOSECOND:
            case EyesThirdCategory.EXCIMER:
                buildInstrumentModel(productAttr).ifPresent(content::add);
                buildApplicableCrowd(productAttr).ifPresent(content::add);
                EyesAttrUtils.buildApplicableDegree(productAttr).ifPresent(content::add);
                break;
            case EyesThirdCategory.CRYSTAL_SURGERY:
                buildCrystalProduct(productAttr).ifPresent(content::add);
                buildInstrumentModel(productAttr).ifPresent(content::add);
                buildApplicableCrowd(productAttr).ifPresent(content::add);
                EyesAttrUtils.buildApplicableDegree(productAttr).ifPresent(content::add);
                break;
            case EyesThirdCategory.DRY_EYE_LASER:
            case EyesThirdCategory.DRY_EYE_THERAPY:
                buildTreatmentTechnology(productAttr).ifPresent(content::add);
                buildInstrumentModel(productAttr).ifPresent(content::add);
                break;

            default:
                break;
        }
        return content;
    }

    /**
     * 验光操作
     * 儿童医学验光、角膜接触镜配镜检查、成人医学验光
     */
    private Optional<DealDetailStructuredDetailVO> buildOptical(ProductAttr productAttr, Long shopCount) {
        String selection = EyesAttrUtils.handleMultiValueAttr(productAttr, "OperatorsSelection", "/", "可选");
        String popupData = EyesAttrUtils.buildOptometristBubblePopupData(productAttr, "OperatorsSelection", shopCount);

        if (StringUtils.isBlank(selection)) {
            return Optional.empty();
        }

        return DealDetailStructuredUtils.buildContentAndPopupData("验光操作", selection, popupData);
    }

    /**
     * 验光数据
     * 儿童医学验光、角膜接触镜配镜检查、成人医学验光
     */
    private Optional<DealDetailStructuredDetailVO> buildOpticalData(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("optometry_data","验光数据",productAttr);
    }

    /**
     * 治疗技术
     * 干眼激光、干眼理疗
     */
    private Optional<DealDetailStructuredDetailVO> buildTreatmentTechnology(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("TreatmentTechnologies", "治疗技术", productAttr);
    }

    /**
     * 晶体产品
     * 晶体手术
     */
    private Optional<DealDetailStructuredDetailVO> buildCrystalProduct(ProductAttr productAttr) {
        String brand = productAttr.getSkuAttrFirstValue("CrystalBrand");
        String product = productAttr.getSkuAttrFirstValue("CrystalProduct");
        if (StringUtils.isAnyBlank(brand, product)) {
            return Optional.empty();
        }
        return DealDetailStructuredUtils.buildTitleAndContent("晶体产品", brand + " - " + product);
    }

    /**
     * 仪器型号
     * 全飞秒、飞秒、准分子、晶体手术、干眼激光、干眼理疗
     */
    private Optional<DealDetailStructuredDetailVO> buildInstrumentModel(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("instrument", "仪器型号", productAttr);
    }

    /**
     * 适用人群
     * 儿童医学验光、角膜接触镜配镜检查、视觉训练、全飞秒、飞秒、准分子、晶体手术
     */
    private Optional<DealDetailStructuredDetailVO> buildApplicableCrowd(ProductAttr productAttr) {
        String people = productAttr.getSkuAttrFirstValue("applicable_population");
        if (StringUtils.isBlank(people)) {
            return Optional.empty();
        }

        if (StringUtils.equals("各年龄段均适用", people)) {
            return DealDetailStructuredUtils.buildTitleAndContent("适用人群", people);
        }

        if (StringUtils.equals("指定年龄范围", people)) {
            String minAge = productAttr.getSkuAttrFirstValue("applicable_age_min");
            String maxAge = productAttr.getSkuAttrFirstValue("applicable_age_max");
            
            if (StringUtils.isAnyBlank(minAge, maxAge)) {
                return Optional.empty();
            }
            
            return DealDetailStructuredUtils.buildTitleAndContent("适用人群", String.format("%s岁-%s岁", minAge, maxAge));
        }

        return Optional.empty();
    }

    /**
     * 适用度数
     * 全飞秒、飞秒、准分子、晶体手术
     */
    // private Optional<DealDetailStructuredDetailVO> buildApplicableDegree(ProductAttr productAttr) {
    //     String applicableDegrees = productAttr.getSkuAttrFirstValue("applicable_degrees");
    //     if (StringUtils.isBlank(applicableDegrees)) {
    //         return Optional.empty();
    //     }
    //
    //     List<String> degreeRanges = Lists.newArrayList();
    //
    //     if (applicableDegrees.contains("近视")) {
    //         processMyopia(productAttr, degreeRanges);
    //     }
    //
    //     if (applicableDegrees.contains("散光")) {
    //         processAstigmatism(productAttr, degreeRanges);
    //     }
    //
    //     if (applicableDegrees.contains("远视")) {
    //         processHyperopia(productAttr, degreeRanges);
    //     }
    //
    //     if (degreeRanges.isEmpty()) {
    //         return Optional.empty();
    //     }
    //
    //     return DealDetailStructuredUtils.buildTitleAndContent("适用度数", String.join("、", degreeRanges));
    // }
    //
    // private void processMyopia(ProductAttr productAttr, List<String> degreeRanges) {
    //     String minDiameter = productAttr.getSkuAttrFirstValue("ApplicableMinDiameter");
    //     String maxValue = productAttr.getSkuAttrFirstValue("ApplicableMyopiaMaxValue");
    //
    //     if (StringUtils.isBlank(minDiameter) && StringUtils.isBlank(maxValue)) {
    //         return;
    //     }
    //
    //     if (StringUtils.isBlank(minDiameter) || "0".equals(minDiameter)) {
    //         degreeRanges.add("近视" + maxValue + "度以下");
    //     } else if (StringUtils.isBlank(maxValue)) {
    //         degreeRanges.add("近视" + minDiameter + "度以上");
    //     } else {
    //         degreeRanges.add("近视" + minDiameter + "-" + maxValue + "度");
    //     }
    // }
    //
    // private void processAstigmatism(ProductAttr productAttr, List<String> degreeRanges) {
    //     String minAstigmatism = productAttr.getSkuAttrFirstValue("ApplicableMinimumAstigmatism");
    //     String maxAstigmatism = productAttr.getSkuAttrFirstValue("ApplicableMaximumAstigmatism");
    //
    //     if (StringUtils.isBlank(minAstigmatism) && StringUtils.isBlank(maxAstigmatism)) {
    //         return;
    //     }
    //
    //     if (StringUtils.isBlank(minAstigmatism) || "0".equals(minAstigmatism)) {
    //         degreeRanges.add("散光" + maxAstigmatism + "度以下");
    //     } else if (StringUtils.isBlank(maxAstigmatism)) {
    //         degreeRanges.add("散光" + minAstigmatism + "度以上");
    //     } else {
    //         degreeRanges.add("散光" + minAstigmatism + "-" + maxAstigmatism + "度");
    //     }
    // }
    //
    // private void processHyperopia(ProductAttr productAttr, List<String> degreeRanges) {
    //     String minHyperopic = productAttr.getSkuAttrFirstValue("ApplicableHyperopicMinimumValue");
    //     String maxHyperopic = productAttr.getSkuAttrFirstValue("ApplicableHyperopicMaximumValue");
    //
    //     if (StringUtils.isBlank(minHyperopic) && StringUtils.isBlank(maxHyperopic)) {
    //         return;
    //     }
    //
    //     if (StringUtils.isBlank(minHyperopic) || "0".equals(minHyperopic)) {
    //         degreeRanges.add("远视" + maxHyperopic + "度以下");
    //     } else if (StringUtils.isBlank(maxHyperopic)) {
    //         degreeRanges.add("远视" + minHyperopic + "度以上");
    //     } else {
    //         degreeRanges.add("远视" + minHyperopic + "-" + maxHyperopic + "度");
    //     }
    // }

} 