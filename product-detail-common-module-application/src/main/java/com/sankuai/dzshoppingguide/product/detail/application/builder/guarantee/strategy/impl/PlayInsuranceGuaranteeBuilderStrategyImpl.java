package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 游乐险保障标签构造策略
 */
public class PlayInsuranceGuaranteeBuilderStrategyImpl implements GuaranteeBuilderStrategy {
    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.PLAY_INSURANCE;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        return null;
    }
}
