package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.MassageStrategy;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/8/17 11:01
 */
public abstract class AbstractMassageStrategy implements MassageStrategy {

    /**
     * 热敷工具
     */
    protected static final String HOTPACK_TOOL = "hotpackTool";

    /**
     * 按摩工具
     */
    protected static final String MASSAGE_TOOL = "massageTool";

    /**
     * 一次性材料及工具
     */
    protected static final String DISPOSABLE_MATERIAL = "disposableMaterial";

    /**
     * 其他工具
     */
    protected static final String UNCLASSIFIED_TOOLS = "unclassifiedTools";

    /**
     * 泡脚桶
     */
    protected static final String FOOT_BATH_BASIN = "footbathBucket";

    /**
     * 泡脚包
     */
    protected static final String FOOT_BATH_BAG = "footbathMaterial";

    /**
     * 免费精油
     */
    protected static final String FREE_ESSENTIAL_OIL = "freeEssentialOil";

    /**
     * 艾灸材料
     */
    protected static final String MOXIBUSTION_MATERIAL = "moxibustionMaterial";

    /**
     * 艾灸工具/仪器
     */
    protected static final String MOXIBUSTION_TOOL = "moxibustionTool";

    /**
     * 特色灸法
     */
    protected static final String MOXIBUSTION_METHOD = "moxibustionMethod";

    /**
     * 刮痧工具
     */
    protected static final String SCRAPING_TOOL = "scrapingTool";

    /**
     * 刮痧材料
     */
    protected static final String SCRAPING_MATERIAL = "scrapingMaterial";

    /**
     * 拔罐罐体
     */
    protected static final String CUPPING_TOOL = "cuppingTool";

    /**
     * 特色采耳工具
     */
    protected static final String EAR_PICKING_TOOL = "earpickingTool";


    protected static final String SPERATOR = "、";

    protected static final Integer TWO = 2;


    protected String getAttrValue(List<ServiceProjectAttrDTO> serviceProjectAttrs, String attrName) {
        return serviceProjectAttrs.stream()
                .filter(skuAttrItemDto -> attrName.equals(skuAttrItemDto.getAttrName()))
                .map(ServiceProjectAttrDTO::getAttrValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取一次性材料及工具和其他工具
     */
    protected List<String> getMixedTools(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        List<String> mixedTools = Lists.newArrayList();
        // 一次性材料及工具
        String disposableMaterial = getAttrValue(serviceProjectAttrs, DISPOSABLE_MATERIAL);
        if (StringUtils.isNotBlank(disposableMaterial)) {
            mixedTools.addAll(Lists.newArrayList(disposableMaterial.split(SPERATOR)));
        }

        // 其他工具
        String unclassifiedTools = getAttrValue(serviceProjectAttrs, UNCLASSIFIED_TOOLS);
        if (StringUtils.isNotBlank(unclassifiedTools)) {
            mixedTools.addAll(Lists.newArrayList(unclassifiedTools.split(SPERATOR)));
        }

        return mixedTools;
    }

}
