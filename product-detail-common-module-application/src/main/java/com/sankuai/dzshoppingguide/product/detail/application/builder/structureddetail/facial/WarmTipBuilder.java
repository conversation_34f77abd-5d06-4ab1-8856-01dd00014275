package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 温馨提示
 */
@Component
public class WarmTipBuilder extends AbstractDealDetailBuilder {


    @Override
    public List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        // 2.提示信息
        List<DealDetailStructuredDetailVO> warmTipItems = buildWarmTipItems(context);
        if (CollectionUtils.isEmpty(warmTipItems)) {
            return null;
        }

        // 1.标题
        result.add(DealDetailStructuredUtils.buildTitle("温馨提示"));

        result.addAll(warmTipItems);

        // 3.分隔符
        result.add(DealDetailStructuredUtils.buildLimiter());
        return result;
    }

    private List<DealDetailStructuredDetailVO> buildWarmTipItems(DealDetailBuildContext context) {
        List<String> values = context.getProductAttr().getSkuAttrValue("WarmTip");
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        return values.stream().map(this::buildSingleItem).collect(Collectors.toList());
    }


    private DealDetailStructuredDetailVO buildSingleItem(String value) {
        return DealDetailStructuredDetailVO.builder()
                .content(value)
                .type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .contentColor("#555")
                .prefix(ViewComponentTypeEnum.PREFIX_DOT.getType())
                .build();
    }

}
