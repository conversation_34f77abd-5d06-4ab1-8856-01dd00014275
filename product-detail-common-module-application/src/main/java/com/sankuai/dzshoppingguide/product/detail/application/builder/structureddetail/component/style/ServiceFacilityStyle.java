package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums.ServiceFacilityEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums.ServiceMaterialAndToolEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.SnackTimeDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant.*;

/**
 * <AUTHOR>
 * @date 2025/5/6 17:22
 */
@Component
@Slf4j
public class ServiceFacilityStyle implements Style<DealDetailStructuredDetailVO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        if (Objects.isNull(productAttr)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> serviceFacilities = Lists.newArrayList();

        // 小吃简餐 FREE_SNACKS 新增
        List<DealDetailStructuredDetailVO> snackItems = buildSnackItems(productAttr);
        serviceFacilities.addAll(snackItems);

        // 茶点水果 FREE_SNACKS_FRUITS 新增
        List<DealDetailStructuredDetailVO> teaAndFruitItems = buildCommonFacility(productAttr,FREE_SNACKS_FRUITS, "茶点水果");
        serviceFacilities.addAll(teaAndFruitItems);

        // 泡脚包 FOOT_SOAK_PACK 新增
        List<DealDetailStructuredDetailVO> footSoakItems = buildCommonFacility(productAttr,FOOT_SOAK_PACK, "泡脚包");
        serviceFacilities.addAll(footSoakItems);

        // 免费精油 精油SPA 108009
        List<DealDetailStructuredDetailVO> freeOil = buildCommonFacility(productAttr,FREE_ESSENTIAL_OIL, "免费精油");
        serviceFacilities.addAll(freeOil);

        // 热敷工具 HOT_COMPRESS_TOOLS 新增
        List<DealDetailStructuredDetailVO> hotCompressItems = buildCommonFacility(productAttr,HOT_COMPRESS_TOOLS, "热敷工具");
        serviceFacilities.addAll(hotCompressItems);

        // 特色工具 其他行业
        List<DealDetailStructuredDetailVO> specialTools = buildCommonFacility(productAttr,FEATURE_TOOLS, "特色工具");
        serviceFacilities.addAll(specialTools);

        // 特色工具 采耳 109016
        List<DealDetailStructuredDetailVO> earPickingTools = buildCommonFacility(productAttr,EAR_PICKING_TOOL, "特色工具");
        serviceFacilities.addAll(earPickingTools);

        // 按摩工具 MASSAGE_TOOLS 新增
        List<DealDetailStructuredDetailVO> massageItems = buildCommonFacility(productAttr,MASSAGE_TOOLS, "按摩工具");
        serviceFacilities.addAll(massageItems);

        // 特色材料 艾灸 101019 小儿推拿 103026
        List<DealDetailStructuredDetailVO> specialMaterial = buildCommonFacility(productAttr,FEATURE_MATERIAL, "特色材料");
        serviceFacilities.addAll(specialMaterial);

        // 拔罐罐体 拔罐 103025
        List<DealDetailStructuredDetailVO> cuppingTool = buildCommonFacility(productAttr,CUPPING_TOOL, "拔罐罐体");
        serviceFacilities.addAll(cuppingTool);

        // 衣物布草 disposableMaterial 已有
        List<DealDetailStructuredDetailVO> laundryItems = buildCommonFacility(productAttr,DISPOSABLE_MATERIAL, "衣物布草");
        serviceFacilities.addAll(laundryItems);

        // 女性用品 FeminineProducts 已有
        List<DealDetailStructuredDetailVO> femaleItems = buildCommonFacility(productAttr,FEMININE_PRODUCTS, "女性用品");
        serviceFacilities.addAll(femaleItems);

        /**
         * 刮痧材料
         */
        buildFacility(serviceFacilities, productAttr, TISSUE_FOR_SCRAPPING, "刮痧材料");

        /**
         * 刮痧工具
         */
        buildFacility(serviceFacilities, productAttr, TORTURE_TOOL, "刮痧工具");

        /**
         * 修脚工具
         */
        buildFacility(serviceFacilities, productAttr, FOOT_CARE_TOOLS, "修脚工具");

        /**
         * 泡脚包
         */
        buildFacility(serviceFacilities, productAttr, FOOT_BATH_MATERIAL, "泡脚包");

        /**
         * 泡脚桶
         */
        buildFacility(serviceFacilities, productAttr, FOOT_BATH_BUCKET, "泡脚桶");

        // 玩乐设施 serviceFacility 不变
        List<DealDetailStructuredDetailVO> playFacilities = buildCommonFacility(productAttr,PLAY_FACILITY_KEY, "玩乐设施");
        serviceFacilities.addAll(playFacilities);

        // 其他服务 OtherStoreServices 不变
        List<DealDetailStructuredDetailVO> otherServices = buildCommonFacility(productAttr,OTHER_FACILITY_KEY, "其他服务");
        serviceFacilities.addAll(otherServices);

        // // 1. 材料工具
        // List<DealDetailStructuredDetailVO> materialTools = buildServiceMaterialTools(productAttr);
        // serviceFacilities.addAll(materialTools);

        // 过滤空值
        List<DealDetailStructuredDetailVO> facilities = serviceFacilities.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(facilities)) {
            DealDetailStructuredDetailVO moduleName = DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_16.getType())
                    .title("服务设施")
                    .build();
            facilities.add(0, moduleName);
        }
        return facilities;
    }

    /**
     * 构造服务设施
     * @param serviceFacilities
     * @param productAttr
     * @param attrKey
     * @param title
     */
    public void buildFacility(List<DealDetailStructuredDetailVO> serviceFacilities, ProductAttr productAttr,String attrKey,String title){
        List<DealDetailStructuredDetailVO> playFacilities = buildCommonFacility(productAttr,attrKey, title);
        if (CollectionUtils.isNotEmpty(playFacilities) && CollectionUtils.isNotEmpty(serviceFacilities)) {
            serviceFacilities.addAll(playFacilities);
        }
    }

    /**
     * 构建小吃简餐 FREE_SNACKS
     * @return
     */
    private List<DealDetailStructuredDetailVO> buildSnackItems(ProductAttr productAttr) {
        List<String> freeSnacks = productAttr.getSkuAttrValues(FREE_SNACKS);
        if (CollectionUtils.isEmpty(freeSnacks)) {
            return Lists.newArrayList();
        }

        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        result.add(DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.FACILITY_TYPE_18.getType()).content(JSON.toJSONString(freeSnacks)).build());
        if (CollectionUtils.isNotEmpty(result)) {
            DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_17.getType())
                    .title("小吃简餐");
            String popupData = buildPopupData(productAttr);
            if (StringUtils.isNotBlank(popupData)) {
                builder.subTitle("供餐时段")
                        .popupIcon("https://p0.meituan.net/ingee/4328a5335fe3993414dbb9de41708c762107.png")
                        .popupData(popupData);
            }
            result.add(0, builder.build());
        }
        return result;
    }

    private String buildPopupData(ProductAttr productAttr) {
        // SNACKS_SERVICE_TIME
        List<String> snacksServiceTime = productAttr.getSkuAttrValues(SNACKS_SERVICE_TIME);
        List<SnackTimeDTO> snackTimeDTOS = FootMassageUtils.parseProcess(snacksServiceTime, SnackTimeDTO.class);
        if (CollectionUtils.isEmpty(snackTimeDTOS)) {
            return StringUtils.EMPTY;
        }

        String snacksServiceTimeStr = snackTimeDTOS.stream().filter(Objects::nonNull).map(e -> e.getStartTime() + "-" + AvailableTimeHelper.getTimeOfDayDoc(e.getEndTime())).collect(Collectors.joining("，"));

        return JSON.toJSONString(DealDetailStructuredDetailVO.builder().title("供餐时段").content(snacksServiceTimeStr).type(ViewComponentTypeEnum.FACILITY_TYPE_6.getType()).build());
    }

    private List<DealDetailStructuredDetailVO> buildCommonFacility(ProductAttr productAttr,String attrKey,String title) {
        if (StringUtils.isBlank(attrKey)) {
            return Lists.newArrayList();
        }
        List<String> skuAttrValues = productAttr.getSkuAttrValues(attrKey);
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Lists.newArrayList();
        }

        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(skuAttrValues)) {
            result.add(DealDetailStructuredDetailVO.builder().type(ViewComponentTypeEnum.FACILITY_TYPE_18.getType()).content(JSON.toJSONString(skuAttrValues)).build());
        }

        if (CollectionUtils.isNotEmpty(result)) {
            result.add(0, DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_17.getType())
                    .title(title).build());
        }

        return result;
    }

    /**
     * 构建其他服务
     * @param firstMustSkuModel
     * @return
     */
    private List<DealDetailStructuredDetailVO> buildOtherServices(ServiceProjectDTO firstMustSkuModel) {
        String otherStoreServices = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), OTHER_FACILITY_KEY);
        if (StringUtils.isBlank(otherStoreServices) || NOTHING_VALUE.equals(otherStoreServices)) {
            return Lists.newArrayList();
        }
        List<Icon> otherServices = Lists.newArrayList();
        for (String otherServiceItem : otherStoreServices.split(ATTR_VALUE_SEPARATE)) {
            Icon item = new Icon(FACILITY_ITEM_ICON, otherServiceItem);
            otherServices.add(item);
        }
        List<DealDetailStructuredDetailVO> otherFacilities = Lists.newArrayList();
        // 其他服务标题
        buildFacilityItemTitle(ServiceFacilityEnum.UNCLASSIFIED_SERVICES).ifPresent(otherFacilities::add);
        // 其他服务
        buildFacilityItem(otherServices).ifPresent(otherFacilities::add);
        return otherFacilities;
    }

    /**
     * 构建玩乐设施 暂时不删除,估计用得上
     * @param firstMustSkuModel
     * @return
     */
    private List<DealDetailStructuredDetailVO> buildPlayFacilities(ServiceProjectDTO firstMustSkuModel) {
        String serviceFacility = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), PLAY_FACILITY_KEY);
        if (StringUtils.isBlank(serviceFacility) || NOTHING_VALUE.equals(serviceFacility)) {
            return Lists.newArrayList();
        }
        List<Icon> facilities = Lists.newArrayList();
        for (String facilityValue : serviceFacility.split(ATTR_VALUE_SEPARATE)) {
            Icon item = new Icon(FACILITY_ITEM_ICON, facilityValue);
            facilities.add(item);
        }
        List<DealDetailStructuredDetailVO> playFacilities = Lists.newArrayList();
        // 玩乐设施标题
        buildFacilityItemTitle(ServiceFacilityEnum.PLAY_FACILITY).ifPresent(playFacilities::add);
        // 玩乐设施
        buildFacilityItem(facilities).ifPresent(playFacilities::add);
        return playFacilities;
    }


    private static final String NOTHING_VALUE = "无";

    private static final String ATTR_VALUE_SEPARATE = "、";

    private static final String FACILITY_ITEM_ICON = "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png";

    /**
     * 不删除,估计用得上
     * @param firstMustSkuModel
     * @return
     */
    private List<DealDetailStructuredDetailVO> buildServiceMaterialTools(ServiceProjectDTO firstMustSkuModel) {
        List<Icon> tools = Lists.newArrayList();
        for (ServiceMaterialAndToolEnum toolEnum : ServiceMaterialAndToolEnum.values()) {
            String toolValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), toolEnum.getToolCode());
            if (StringUtils.isNotBlank(toolValue)) {
                tools.add(new Icon(toolEnum.getIcon(), toolValue));
            }
        }
        if (CollectionUtils.isEmpty(tools)) {
            return Lists.newArrayList();
        }
        List<DealDetailStructuredDetailVO> materialTools = Lists.newArrayList();
        // 服务设施标题
        buildFacilityItemTitle(ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL).ifPresent(materialTools::add);
        // 材料工具
        buildFacilityItem(tools).ifPresent(materialTools::add);
        return materialTools;
    }

    private Optional<DealDetailStructuredDetailVO> buildFacilityItem(List<Icon> contents) {
        List<String> textContents = Optional.ofNullable(contents)
                .orElse(Lists.newArrayList())
                .stream()
                .map(Icon::getText)
                .collect(Collectors.toList());
        return Optional.of(DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.FACILITY_TYPE_3.getType())
                .content(JSON.toJSONString(textContents))
                .build());
    }

    private Optional<DealDetailStructuredDetailVO> buildFacilityItemTitle(ServiceFacilityEnum serviceFacilityEnum) {
        return  Optional.of(DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.FACILITY_TYPE_2.getType())
                .title(serviceFacilityEnum.getServiceFacility())
                .icon(serviceFacilityEnum.getNewIcon())
                .build());
    }


}
