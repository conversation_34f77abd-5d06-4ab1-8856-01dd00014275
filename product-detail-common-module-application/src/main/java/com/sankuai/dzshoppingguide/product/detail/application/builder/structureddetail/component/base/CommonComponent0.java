package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/30 16:31
 * @Description: 通用组件接口, 支持任意BaseStructuredDetailVO子类
 */
public interface CommonComponent0<T> extends BaseComponent0 {

    /**
     * 构建结构化详情项
     *
     * @param vo 结构化详情VO
     * @return 构建后的结构化详情项列表
     */
    // TODO: 根据业务进一步抽象, 实现类先做空实现处理, 自己实现定制方法
    List<T> build(T vo);
}
