package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 11:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopIdMapper extends FetcherReturnValueDTO {
    private long dpBestShopId;
    private long mtBestShopId;
}
