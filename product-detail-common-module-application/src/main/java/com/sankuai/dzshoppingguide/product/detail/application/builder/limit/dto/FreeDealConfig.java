package com.sankuai.dzshoppingguide.product.detail.application.builder.limit.dto;

import lombok.Data;

import java.util.List;

@Data
public class FreeDealConfig {
    /**
     * 价格提示文案
     * 可选
     */
    private List<DescriptionTag> priceTags;
    /**
     * 销量文案前缀
     * 可选
     */
    private String saleDescPrefix;
    /**
     * 底部行动点文案
     * 必须
     */
    private String buttonText;
    /**
     * 底部行动点-美团APP链接模板
     */
    private String mtAppSchema;
    /**
     * 底部行动点-美团H5链接模板
     */
    private String mtH5Schema;
    /**
     * 底部行动点-点评APP链接模板
     */
    private String dpAppSchema;
    /**
     * 底部行动点-点评H5链接模板
     */
    private String dpH5Schema;
    /**
     * 底部行动点置灰文案
     * 可选
     */
    private String emptyButtonText;
    /**
     * 限制文案
     * 不为空-展示{Limit}固定文案，为空-展示平台的兜底逻辑
     */
    private List<String> limit;
    /**
     * 是否展示划线价(市场价)
     */
    private boolean showMarketPrice = false;
}
