package com.sankuai.dzshoppingguide.product.detail.application.builder.limit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.limit.dto.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FreeDealUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.limit.vo.CommonFloatLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.limit.vo.LimitContent;
import com.sankuai.dzshoppingguide.product.detail.spi.limit.vo.LimitModuleVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.technician.category.enums.TechCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

@Builder(
        moduleKey = ModuleKeyConstants.LIMIT_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class, ProductCategoryFetcher.class, ProductAttrFetcher.class}
)
@Slf4j
public class LimitModuleBuilder extends BaseBuilder<LimitModuleVO> {

    public static final int NO_LIMIT = 0;
    public static final int LIMIT_ONE = 1;
    public static final int LIMIT_MULTI = 2;
    private static final String SUIT_PERSON = "suitable_person_first_not_member";
    private static final String LIMIT_ONE_PERSON = "limit_one";
    private static final String LIMIT_ONE_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "limit_one_suit_person_first_not_member";
    private static final String LIMIT_MULTI_PERSON = "limit_multi";
    private static final String LIMIT_MULTI_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "limit_multi_suit_person_first_not_member";
    private static final String NO_LIMIT_PERSON = "no_limit";
    private static final String NO_LIMIT_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER = "no_limit_suit_person_first_not_member";

    // 美团美播小程序/提单小程序
    private final List<ClientTypeEnum> mtLiveClients =  Lists.newArrayList(ClientTypeEnum.MT_LIVE_XCX, ClientTypeEnum.MT_LIVE_ORDER_XCX);

    private ProductAttr productAttr;

    @Override
    public LimitModuleVO doBuild() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);

        LimitModuleVO limitModuleVO = new LimitModuleVO();
        List<LimitContent> contents = Lists.newArrayList();
        List<CommonFloatLayer> layerConfigs = Lists.newArrayList();
        limitModuleVO.setContents(contents);
        limitModuleVO.setLayerConfigs(layerConfigs);


        //是否来自酒吧营销经理
        if (isFromBarManager()) {
            buildLimitInfo("需要商家审核退款").ifPresent(contents::add);
        }

        boolean freeDeal = FreeDealUtils.isFreeDeal(productBaseInfo, productCategory);
        FreeDealConfig freeDealConfig = FreeDealUtils.getFreeDealConfig(productBaseInfo, productCategory);
        List<String> limits = Optional.ofNullable(freeDealConfig).map(FreeDealConfig::getLimit).orElse(Lists.newArrayList());
        if (freeDeal && CollectionUtils.isNotEmpty(limits)) {
            limits.stream().map(this::buildLimitInfo).filter(Optional::isPresent).map(Optional::get).forEach(contents::add);
            return limitModuleVO;
        }

        DealGroupBuyRuleDTO buyRuleDTO = Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getRule).map(DealGroupRuleDTO::getBuyRule).orElse(null);
        if (buyRuleDTO == null) {
            return limitModuleVO;
        }

        // 丽人限购条，有浮层效果，可按配置行业下发
        boolean beauty = LionConfigUtils.hitLimitInfoConfig(FreeDealUtils.getSecondCategoryId(productCategory));
        if (beauty && Objects.nonNull(buyRuleDTO.getMaxPerUser())) {
            // 获取限购条信息、限购条浮层
            CommonFloatLayer limitInfoConfig = getLimitInfo(buyRuleDTO.getMaxPerUser());
            if (Objects.isNull(limitInfoConfig)){
                return limitModuleVO;
            }
            // 填充限购人数信息
            buildLayerConfig(limitInfoConfig, buyRuleDTO);

            // 限购条新字段，可支持高亮
            buildLimitExtendByMap(contents, limitInfoConfig);

            // 构造限购条浮层
            buildLimitLayer(layerConfigs,limitInfoConfig);

            return limitModuleVO;
        }

        // 非丽人相关行业
        if(buyRuleDTO.getMaxPerUser() > 0){
            String limitInfo = String.format("每人限购%s张",  buyRuleDTO.getMaxPerUser());
            buildLimitInfo(limitInfo).ifPresent(contents::add);
        }

        if (CollectionUtils.isEmpty(contents) && CollectionUtils.isEmpty(layerConfigs)){
            return null;
        }
        return limitModuleVO;
    }

    public void buildLimitExtendByMap(List<LimitContent> limitsExtends, CommonFloatLayer limitInfoConfig){
        if (StringUtils.isNotBlank(limitInfoConfig.getTitle())){
            LimitContent limitExtend = LimitContent.builder()
                    .text(limitInfoConfig.getTitle())
                    .type(limitInfoConfig.getTextType())
                    .style(limitInfoConfig.getTextStyle())
                    .build();
            limitsExtends.add(limitExtend);
        }
    }

    /**
     * 构造限制条浮层
     */
    public void buildLimitLayer(List<CommonFloatLayer> layerConfigs,CommonFloatLayer limitInfoConfig){
        if (identifyMtLiveMiniApp()) {
            return;
        }
        if (!isValidLimitInfoConfig(limitInfoConfig)){
            return;
        }
        layerConfigs.add(limitInfoConfig);
    }

    private Boolean isValidLimitInfoConfig(CommonFloatLayer limitInfoConfig){
        if (Objects.isNull(limitInfoConfig)
                || StringUtils.isBlank(limitInfoConfig.getDesc())){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean identifyMtLiveMiniApp() {
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        return mtLiveClients.contains(clientTypeEnum);
    }


    public void buildLayerConfig(CommonFloatLayer layerConfig, DealGroupBuyRuleDTO buyRuleDTO){
        if (Objects.nonNull(layerConfig) && Objects.nonNull(buyRuleDTO)){
            String title = layerConfig.getTitle();
            String desc = layerConfig.getDesc();
            layerConfig.setTitle(title != null ? String.format(title, buyRuleDTO.getMaxPerUser()) : "");
            layerConfig.setDesc(desc != null ? String.format(desc, buyRuleDTO.getMaxPerUser()) : "");
        }
    }


    /**
     * 获取限购条及限购条浮层信息
     * @return
     */
    public CommonFloatLayer getLimitInfo(int maxPerUser){

        // 获取适用人群
        List<String> suitPerson = productAttr.getSkuAttrValue(SUIT_PERSON);
        Map<String, CommonFloatLayer> generalLayerConfigMap = Lion.getMap(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.GENERAL_LAYER_CONFIG, CommonFloatLayer.class, Collections.emptyMap());

        // 不限购买次数
        if (maxPerUser == NO_LIMIT) {
            return getNoLimitInfo(suitPerson, generalLayerConfigMap);
        }
        // 限制购买次数1次
        if (maxPerUser == LIMIT_ONE) {
            return getOneLimitInfo(suitPerson, generalLayerConfigMap);
        }
        //限制购买次数>=2次
        if (maxPerUser >= LIMIT_MULTI){
            return getMultiLimitInfo(suitPerson, generalLayerConfigMap);
        }
        return null;
    }

    private CommonFloatLayer getNoLimitInfo( List<String> suitPerson, Map<String, CommonFloatLayer> generalLayerConfigMap){
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(NO_LIMIT_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(NO_LIMIT_PERSON);
        }
    }
    private CommonFloatLayer getOneLimitInfo( List<String> suitPerson, Map<String, CommonFloatLayer> generalLayerConfigMap){
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(LIMIT_ONE_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(LIMIT_ONE_PERSON);
        }
    }
    private CommonFloatLayer getMultiLimitInfo( List<String> suitPerson, Map<String, CommonFloatLayer> generalLayerConfigMap){
        // 勾选只适用于初次到店非会员
        if (suitPerson.contains("只适用于初次到店非商户会员使用")) {
            return generalLayerConfigMap.get(LIMIT_MULTI_PERSON_SUIT_PERSON_FIRST_NOT_MEMBER);
        } else { // 未勾选只适用于初次到店非会员
            return generalLayerConfigMap.get(LIMIT_MULTI_PERSON);
        }
    }

    /**
     * 来源是否是酒吧营销经理
     * @return
     */
    private boolean isFromBarManager(){
        String pass_paramStr = Optional.ofNullable(request.getCustomParam()).map(param -> param.getParam(RequestCustomParamEnum.pass_param)).orElse("");
        if (StringUtils.isEmpty(pass_paramStr)){
            return false;
        }
        try {
            JSONObject pass_paramJsonObject = JSON.parseObject(pass_paramStr);
            String craftsmanExtParamStr = pass_paramJsonObject.getString("craftsmanExtParam");
            if (StringUtils.isEmpty(craftsmanExtParamStr)) {
                return false;
            }
            JSONObject craftsmanExtParamJsonObject = JSON.parseObject(craftsmanExtParamStr);
            Integer techCategoryId = craftsmanExtParamJsonObject.getInteger("techCategoryId");
            if (techCategoryId == null){
                return false;
            }
            return techCategoryId == TechCategoryEnum.JOY_BAR_MANAGER.getCategoryId();
        }catch (Exception e){
            log.error("解析来源字段异常", e);
        }
        return false;
    }

    private Optional<LimitContent> buildLimitInfo(String text) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(text)) {
            return Optional.empty();
        }
        return Optional.of(LimitContent.builder().text(text).build());
    }
}
