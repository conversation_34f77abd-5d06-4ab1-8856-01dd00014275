package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PeriodInfoM implements Serializable {
    /**
     * 时段开始时间
     */
    private String periodStartTime;

    /**
     * 时段结束时间
     */
    private String periodEndTime;

    /**
     * 时段基准价
     */
    private BigDecimal periodOriginSalePrice;

    /**
     * 结束时间是否跨天
     */
    private Boolean acrossDay;
}
