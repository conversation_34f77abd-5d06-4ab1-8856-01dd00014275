package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.LEInsuranceAgreementEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.utils.tag.ShopTagUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 保洁自营保障构造策略
 */
public class CleaningSelfOperationGuaranteeBuilderStrategyImpl extends BaseGuaranteeBuilderStrategy implements GuaranteeBuilderStrategy {

    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.CLEANING_SELF_OPERATION;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        LEInsuranceAgreementEnum leInsuranceAgreementEnum = ShopTagUtils.getLEInsuranceAgreementEnum(param.getShopDisplayTagList());
        List<GuaranteeInstructionsContentVO> result = Lists.newArrayList();
        if (leInsuranceAgreementEnum == LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT) {
            result.add(new GuaranteeInstructionsContentVO("迟到爽约赔"));
            result.add(new GuaranteeInstructionsContentVO("财产损失赔"));
            result.add(new GuaranteeInstructionsContentVO("不满意重做"));
        } else if (leInsuranceAgreementEnum != null) {
            GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO(leInsuranceAgreementEnum.getText());
            content.setFontColor("#744E2A");
            Icon icon = new Icon();
            icon.setIcon("https://p1.meituan.net/travelcube/9c4b563d359a7c5dd078efb00b57bb8b2155.png");
            content.setPrefixIcon(icon);
            result.add(content);
        }
        List<GuaranteeInstructionsContentVO> guaranteeInstructionsContents = buildContents(param);
        if (CollectionUtils.isNotEmpty(guaranteeInstructionsContents)) {
            result.addAll(guaranteeInstructionsContents);
        }
        return null;
    }


}
