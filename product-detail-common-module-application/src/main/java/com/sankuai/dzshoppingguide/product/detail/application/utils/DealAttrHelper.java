package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.DealDetailStructuredModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.DealStructModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.MustSkusGroupModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.SkuModel;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class DealAttrHelper {

    private static final List<String> WORKDAYS = Lists.newArrayList("1001", "1002", "1003", "1004", "1005");


    public static boolean isJavaListString(String listString) {
        // 使用正则表达式判断是否符合 Java List 的 toString() 格式
        return listString.matches("^\\[.*\\]$");
    }

    public static List<String> buildContents(String attrValue) {
        if (StringUtils.isBlank(attrValue)) {
            return null;
        }
        if (DealAttrHelper.isJavaListString(attrValue)) {
            return JSONObject.parseArray(attrValue, String.class);
        }
        return Lists.newArrayList(attrValue);
    }

    public static String joinListByDelimiter(List<String> values, String delimiter) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(values) || Objects.isNull(delimiter)) {
            return null;
        }
        return String.join(delimiter, values);
    }

    public static String joinListByDelimiter(Set<String> values, String delimiter) {
        if (CollectionUtils.isEmpty(values) || Objects.isNull(delimiter)) {
            return null;
        }
        return String.join(delimiter, values);
    }

    public static DealStructModel parseDealStructModel(String dealStructContent) {
        if (StringUtils.isBlank(dealStructContent)) {
            return null;
        }
        return JsonUtils.fromJson(dealStructContent, DealStructModel.class);
    }

    public static SkuModel getFirstMustSkuFromDealStructuredModel(DealDetailStructuredModel dealDetailStructuredData) {
        if (Objects.isNull(dealDetailStructuredData)
                || Objects.isNull(dealDetailStructuredData.getDealDetailSkuUniStructuredModel())
                || org.apache.commons.collections4.CollectionUtils.isEmpty(dealDetailStructuredData.getDealDetailSkuUniStructuredModel().getMustGroups())) {
            return null;
        }
        MustSkusGroupModel mustSkusGroupModel = dealDetailStructuredData.getDealDetailSkuUniStructuredModel().getMustGroups().get(0);
        if (Objects.isNull(mustSkusGroupModel) || CollectionUtils.isEmpty(mustSkusGroupModel.getSkus())) {
            return null;
        }
        return mustSkusGroupModel.getSkus().get(0);
    }

    public static ServiceProjectDTO getFirstMustSkuFromServiceProject(DealGroupServiceProjectDTO serviceProject) {
        if (Objects.isNull(serviceProject) || CollectionUtils.isEmpty(serviceProject.getMustGroups())) {
            return null;
        }
        MustServiceProjectGroupDTO mustSkusGroupModel = serviceProject.getMustGroups().get(0);
        if (Objects.isNull(mustSkusGroupModel) || CollectionUtils.isEmpty(mustSkusGroupModel.getGroups())) {
            return null;
        }
        return mustSkusGroupModel.getGroups().get(0);
    }

    public static String getAttrValue(List<ServiceProjectAttrDTO> attrs, String attrName) {
        if (CollectionUtils.isEmpty(attrs) || StringUtils.isBlank(attrName)) {
            return StringUtils.EMPTY;
        }
        return attrs.stream()
                .filter(attr -> attrName.equals(attr.getAttrName()))
                .findFirst()
                .map(ServiceProjectAttrDTO::getAttrValue)
                .orElse(StringUtils.EMPTY);
    }

    public static boolean needReservation(List<AttrDTO> attrs) {
        return org.apache.commons.collections.CollectionUtils.isNotEmpty(attrs) && (hasAttribute(attrs, DealAttrKeys.RESERVATION, DealAttrKeys.RESERVATION_VALUE_YES)
                || hasAttribute(attrs, DealAttrKeys.RESERVATION_2, DealAttrKeys.RESERVATION_VALUE_YES)
                || hasAttribute(attrs, DealAttrKeys.RESERVATION_3, DealAttrKeys.RESERVATION_VALUE_YES));
    }

    private static boolean hasAttribute(List<AttrDTO> attrs, String key, String value) {
        List<String> attrValues = getAttributeValues(attrs, key);
        return org.apache.commons.collections.CollectionUtils.isNotEmpty(attrValues) && attrValues.contains(value);
    }

    public static boolean isSupportHomeService(List<AttrDTO> attrs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.SUPPORT_HOME_SERVICE);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static boolean isSupportShopService(List<AttrDTO> attrs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String available = getFirstValue(attrs, DealAttrKeys.SUPPORT_SHOP_SERVICE);
        return StringUtils.isNotEmpty(available) && available.equals("是");
    }

    public static String getFirstValue(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValues(attributeDtoList, key);
        return org.apache.commons.collections.CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static List<String> getAttributeValues(List<AttrDTO> attributeDtoList, String key) {
        if (CollectionUtils.isEmpty(attributeDtoList)) {
            return Collections.emptyList();
        }
        for (AttrDTO attr : attributeDtoList) {
            if (attr != null && StringUtils.equals(attr.getName(), key)) {
                return attr.getValue();
            }
        }
        return Collections.emptyList();
    }

    public static boolean onlyVerificationOne(List<AttrDTO> attrs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String firstValue = getFirstValueV2(attrs, DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        if (StringUtils.isBlank(firstValue)) {
            return false;
        }
        return "单次到店仅可核销一次，仅能一人使用".equals(firstValue);
    }

    private static String getFirstValueV2(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValuesV2(attributeDtoList, key);
        return org.apache.commons.collections.CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static List<String> getAttributeValuesV2(List<AttrDTO> attributeDtoList, String key) {
        if (CollectionUtils.isNotEmpty(attributeDtoList)) {
            for (AttrDTO attr : attributeDtoList) {
                if (attr != null && StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Collections.emptyList();
    }

    public static String getTimes(List<AttrDTO> attrs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return getFirstValueV2(attrs, DealAttrKeys.SYS_MULTI_SALE_NUMBER);
    }

    public static boolean workDayAvailable(List<AttrDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        List<String> attrHolidays = getAttributeValues(attrs, DealAttrKeys.HOLIDAY_AVAILABLE);
        if (CollectionUtils.isEmpty(attrHolidays) || org.apache.commons.collections.CollectionUtils.isEmpty(WORKDAYS)) {
            return false;
        }
        boolean flag = false;
        if (attrHolidays.contains("1006") && attrHolidays.contains("1007")) {
            flag = true;
        }
        for (String workday : WORKDAYS) {
            if (attrHolidays.contains(workday)) {
                return false;
            }
        }
        return flag;
    }

    public static boolean isWuYouTongAttribute(AttrDTO attributeDTO) {
        if (attributeDTO == null || StringUtils.isEmpty(attributeDTO.getName()) || CollectionUtils.isEmpty(attributeDTO.getValue())) {
            return false;
        }
        if (!"standardDealGroupKey".equals(attributeDTO.getName())) {
            return false;
        }

        String value = attributeDTO.getValue().get(0);
        Set<String> wuyoutongValuesSet = LionConfigUtils.getWuYouTongAttrValues();
        return StringUtils.isNotEmpty(value) && wuyoutongValuesSet.contains(value);
    }

    public static boolean validAttr(List<AttrDTO> attrs, String attrName, String attrValue) {
        if (CollectionUtils.isEmpty(attrs) || StringUtils.isEmpty(attrValue)) {
            return false;
        }
        String firstValue = getFirstValueV2(attrs, attrName);
        if (StringUtils.isBlank(firstValue)) {
            return false;
        }
        return attrValue.equals(firstValue);
    }

    private static StandardAttributeValueDTO getAttrValue(StandardAttributeDTO standardAttribute, String attrName) {
        if (Objects.isNull(standardAttribute) || StringUtils.isBlank(attrName)) {
            return null;
        }
        List<StandardAttributeItemDTO> attrs = standardAttribute.getAttrs();
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        List<StandardAttributeValueDTO> standardAttributeValueDTOS = attrs.stream()
                .filter(attr -> attrName.equals(attr.getAttrName()))
                .findFirst()
                .map(StandardAttributeItemDTO::getAttrValues)
                .orElse(org.apache.commons.compress.utils.Lists.newArrayList());
        if (CollectionUtils.isEmpty(standardAttributeValueDTOS)) {
            return null;
        }
        // 查询中心返回的List长度一般为1, 可能有坑！！！
        return standardAttributeValueDTOS.get(0);
    }

    /**
     * 返回简单类型
     *
     * @param standardAttribute
     * @param attrName
     * @return 返回null首先排查B端数据是否为简单类型
     */
    public static List<String> getSimpleAttrValue(StandardAttributeDTO standardAttribute, String attrName) {
        StandardAttributeValueDTO attrValue = getAttrValue(standardAttribute, attrName);
        if (Objects.isNull(attrValue)) {
            return null;
        }

        Integer type = attrValue.getType();
        // 内部校验, 正常返回简单类型, 异常打印日志
        if (Objects.equals(type, 0)) {
            // 目前simpleValues长度均为1, 可能为单值String, 可能为多值json
            String simpleValue = attrValue.getSimpleValues().get(0);
            boolean multiValueArrayServiceProject = LionConfigUtils.isMultiValueArrayServiceProject(attrName);
            if (multiValueArrayServiceProject) {
                return JsonUtils.fromJson(simpleValue, new TypeReference<List<String>>() {
                });
            } else {
                return Collections.singletonList(simpleValue);
            }
        }
        else {
            log.error("type字段不正确, type={}", type);
            return null;
        }
    }

    /**
     * 返回复杂类型
     *
     * @param standardAttribute
     * @param attrName
     * @return 返回null首先排查B端数据是否为复杂类型
     */
    public static List<StandardAttributeDTO> getComplexAttrValue(StandardAttributeDTO standardAttribute, String attrName) {
        StandardAttributeValueDTO attrValue = getAttrValue(standardAttribute, attrName);
        if (Objects.isNull(attrValue)) {
            return null;
        }

        Integer type = attrValue.getType();
        // 内部校验, 正常返回复杂类型, 异常打印日志
        if (Objects.equals(type, 1)) {
            String jsonStr = attrValue.getComplexValues();
            return JsonUtils.fromJson(jsonStr, new TypeReference<List<StandardAttributeDTO>>() {
            });
        }
        else {
            log.error("type字段不正确, type={}", type);
            return null;
        }
    }

}
