package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-04-23
 * @desc 茶馆 棋牌套餐 629
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductCategoryFetcher.class,
                ProductServiceProjectFetcher.class
        }
)
public class TeaHouseCheeseCardsDealStructuredDetailBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {

    @Override
    DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
        // 项目时长
        return DealDetailStructuredDetailVO.builder()
                    .title("套餐详情")
                    .type(ViewComponentTypeEnum.DETAIL_TYPE_11.getType())
                    .build();
    }

    @Override
    List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
        if (Objects.isNull(productAttr)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> packageDetails = Lists.newArrayList();
        // 空间类型
        String spaceType = productAttr.getSkuAttrFirstValue("rooms");
        if (StringUtils.isNotBlank(spaceType)) {
            packageDetails.add(buildStructuredDetailVO("空间类型", spaceType,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 使用时长
        String applicableDuration = productAttr.getSkuAttrFirstValue("max_service_limited_time");
        // 超时规则信息
        String timeoutInformation = productAttr.getSkuAttrFirstValue("TimeoutInformation2");
        if (StringUtils.isNotBlank(applicableDuration)) {
            packageDetails.add(buildStructuredDetailVO("使用时长", applicableDuration, timeoutInformation, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 空间配套
        String supportService = productAttr.getSkuAttrFirstValue("supportService");
        if (StringUtils.isNotBlank(supportService)) {
            packageDetails.add(buildStructuredDetailVO("空间配套", supportService,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 茶水服务
        String teaService = productAttr.getSkuAttrFirstValue("Teaservice");
        if (StringUtils.isNotBlank(teaService)) {
            packageDetails.add(buildStructuredDetailVO("茶水服务", teaService,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        // 附加服务
        String additionalServices = productAttr.getSkuAttrFirstValue("AdditionalServices");
        if (StringUtils.isNotBlank(additionalServices)) {
            packageDetails.add(buildStructuredDetailVO("附加服务", additionalServices,null, ViewComponentTypeEnum.DETAIL_TYPE_15.getType()));
        }

        return packageDetails;
    }


}
