package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnPurchaseNoteDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayItemDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayValueDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PurchaseNoteModuleDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 16:31
 */
public class DealTimesCardRefundInfoUtils {

    private static final String OLD_REFUND_INFO = "有效期内可申请全额退款";
    private static final String NEW_REFUND_INFO = "若次数未使用，可随时退全部实付金额。若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额=团购实付金额-已核销次数的单次价格之和。";
    private static final String NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS = "购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若发生核销后再申请退款，剩余所有未核销次数将一起退款";

    public static void handleDealTimesCardRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO,DealCtx ctx) {
        if (Objects.isNull(purchaseNoteDTO) || ctx == null || !ctx.isDealTimesCard()) {
            return;
        }

        List<Long> whiteList = LionConfigUtils.getCustomerWhiteList();
        if (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(ctx.getCustomerId())) {
            // 白名单客户处理逻辑
            handleWhiteListCustomer(purchaseNoteDTO, ctx);
            return;
        }

        // 小程序的处理逻辑
        handleMiniRefundDetail(purchaseNoteDTO, ctx);
    }

    private static void handleMiniRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        if (!ctx.isMiniProgram()) {
            return;
        }
        if ( Objects.isNull(purchaseNoteDTO)) {
            return;
        }

        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }
        // 删除新的退款规则
        deleteNewRefundInfo(purchaseNoteDTO);
        // 替换旧退款规则文字描述
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO);
    }

    private static void handleWhiteListCustomer(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        if (Objects.isNull(purchaseNoteDTO)) {
            return;
        }

        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }

        deleteNewRefundInfo(purchaseNoteDTO);
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS);
    }

    private static void deleteNewRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO) {
        List<PurchaseNoteModuleDTO> beforePnModules = purchaseNoteDTO.getPnModules();

        if (CollectionUtils.isEmpty(beforePnModules)) {
            return;
        }

        List<PurchaseNoteModuleDTO> afterPnModules = beforePnModules.stream().filter(Objects::nonNull)
                .filter(module -> !Objects.equals("退款规则", module.getPnModuleName())).collect(Collectors.toList());

        purchaseNoteDTO.setPnModules(afterPnModules);
    }

    private static void replaceRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO, String newRefundInfo) {
        List<PurchaseNoteModuleDTO> pnModules = purchaseNoteDTO.getPnModules();
        if (CollectionUtils.isEmpty(pnModules)) {
            return;
        }
        Optional<PurchaseNoteModuleDTO> warmTipsOpt = pnModules.stream().filter(Objects::nonNull)
                .filter(module -> Objects.equals("温馨提示", module.getPnModuleName())).findFirst();
        if (!warmTipsOpt.isPresent()) {
            return;
        }
        PurchaseNoteModuleDTO warmTips = warmTipsOpt.get();
        List<PnStandardDisplayItemDTO> pnItems = warmTips.getPnItems();
        if (CollectionUtils.isEmpty(pnItems)) {
            return;
        }
        // 找出包含旧退款信息的模块
        Optional<PnStandardDisplayValueDTO> oldRefundInfoOpt = pnItems.stream().filter(Objects::nonNull)
                .map(PnStandardDisplayItemDTO::getPnItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotBlank(item.getPnValue())
                        && item.getPnValue().contains(OLD_REFUND_INFO))
                .findFirst();

        // 有则改之
        if (oldRefundInfoOpt.isPresent()) {
            PnStandardDisplayValueDTO pnStandardDisplayValueDTO = oldRefundInfoOpt.get();
            pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
            return;
        }

        // 无则加勉
        Optional<PnStandardDisplayItemDTO> firstPnStandardDisplayItemDTOOpt = pnItems.stream().filter(Objects::nonNull)
                .findFirst();

        if (!firstPnStandardDisplayItemDTOOpt.isPresent()) {
            return;
        }
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = firstPnStandardDisplayItemDTOOpt.get();
        List<PnStandardDisplayValueDTO> pnItemValues = pnStandardDisplayItemDTO.getPnItemValues();
        if (CollectionUtils.isEmpty(pnItemValues)) {
            pnStandardDisplayItemDTO.setPnItemValues(Lists.newArrayList(buildPnStandardDisplayValueDTO(newRefundInfo)));
        } else {
            pnItemValues.add(buildPnStandardDisplayValueDTO(newRefundInfo));
        }
    }

    private static PnStandardDisplayValueDTO buildPnStandardDisplayValueDTO(String newRefundInfo) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(1);
        pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
        return pnStandardDisplayValueDTO;
    }
}
