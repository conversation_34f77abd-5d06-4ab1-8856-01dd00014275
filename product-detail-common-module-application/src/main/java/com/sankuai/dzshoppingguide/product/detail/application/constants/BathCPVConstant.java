package com.sankuai.dzshoppingguide.product.detail.application.constants;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/23 10:43
 * @Description: 洗浴行业CPV
 */
public interface BathCPVConstant {

    // 通用字段, 与类目ID无关
    /**
     * 项目分类
     */
    String SKU_CATE_ID = "skuCateId";

    /**
     * 项目名称
     * TODO：待和静姐确认，目前老服务项目：name，新服务项目：serviceProjectName(直接属性名取值即可)
     */
    String PROJECT_NAME = "serviceProjectName";

    /**
     * 服务项目份数
     */
    String AMOUNT = "amount";

    /**
     * 服务开始时间
     */
    String START_TIME = "start_time";

    /**
     * 服务结束时间
     */
    String END_TIME = "end_time";

    /**
     * 服务内容描述
     */
    String CONTENT = "content";

    // sys字段
    /**
     * 图文详情
     */
    String SYS_RICH_TEXT = "sys_richText";

    /**
     * 补充说明
     */
    String SYS_TEXT_AREA = "sys_textArea";

    /**
     * 节假日不可用日期
     */
    String SYS_EXCEPT_HOLIDAY = "sys_exceptHoliday";

    /**
     * 自定义不可用日期
     */
    String SYS_EXCEPT_DATE = "sys_exceptDate";





    // 浴资票
    /**
     * 可用时段
     */
    String AVAILABLE_TIME_PERIOD= "AvailableTimePeriod3";

    /**
     * 可用时段名称
     */
    String AVAILABLE_TIME_PERIOD_NAME = "AvailableTimePeriodName";

    /**
     * 时段范围（多值）
     */
    String TIME_RANGE = "TimeRange3";

    /**
     * 时间段（开始时间点）
     */
    String START_TIME_POINT = "StartTimePoint";

    /**
     * 时间段（结束时间点）
     */
    String END_TIME_POINT = "EndTimePoint";

    /**
     * 适用时长
     */
    String APPLICABLE_DURATION = "ApplicableDuration";

    /**
     * 适用人群（成人数量）
     */
    String ADULT_POPULATION = "AdultPopulation2";

    /**
     * 适用人群（儿童数量）
     */
    String CHILDREN_POPULATION = "ChildCount2";

    /**
     * 儿童判断标准
     */
    String CHILD_INSTRUCTIONS = "ChildInstructions";

    /**
     * 亮点描述（项目描述）TODO: 团单待确认字段
     */
    String HIGHLIGHT_DESCRIPTION = "content";

    // 自助餐服务, 注意不同三级ID下的自助餐字段略有区别

    /**
     * 自助餐/餐饮用餐时长（用餐时长）
     */
    String BUFFET_DURATION = "Selfservicediningduration";

    /**
     * 自助餐供餐时间（多值）
     */
    String BUFFET_SERVICE_TIME = "selfservicedininghours";

    /**
     * 自助餐开始时间
     */
    String BUFFET_START_TIME = "selfservicerestaurantstarttime";

    /**
     * 自助餐结束时间
     */
    String BUFFET_END_TIME = "selfservicerestaurantclosingtime";

    /**
     * 时段
     */
    String TIME_FRAME = "timeframe1";

    /**
     * 服务内容描述（餐食亮点描述）
     */
    String BUFFET_HIGHLIGHTS = "content";


    // 过夜服务模块
    /**
     * 过夜服务
     */
    String OVERNIGHT_SERVICE = "OvernightServices";

    /**
     * 过夜规则（过夜服务详情）
     */
    String OVERNIGHT_RULES = "overnightRules";

    /**
     * 过夜开始时间
     */
    String OVERNIGHT_START_TIME = "stayOverStartTime";

    /**
     * 过夜结束时间
     */
    String OVERNIGHT_END_TIME = "stayOverEndTime";

    /**
     * 免费早餐
     */
    String FREE_BREAKFAST = "freeBreakfast";

    /**
     * 过夜规则描述
     */
    String STAY_OVER_DESC = "stayOverDesc";





    // 服务设施
    /**
     * 免费设施
     */
    String FREE_FACILITY = "free_facilities";

    /**
     * 桑拿/汗蒸
     */
    String SAUNA = "sauna";

    /**
     * 汤池
     */
    String BATHING_POOL = "bathing_pool";

    /**
     * 玩乐设施
     */
    String LEISURE = "leisure";

    /**
     * 休息区域
     */
    String REST_AREA = "RestArea";

    /**
     * 免费洗浴用品
     */
    String FREE_BATHING_SUPPLIES = "free_bathing_supplies";

    /**
     * 一次性卫生用品
     */
    String DISPOSABLE_MATERIALS = "disposable_materials";

    /**
     * 洗浴用品
     */
    String BATHING_SUPPLIES = "bathing_supplies";






    // 店内服务

    // 店内服务通用字段
    /**
     * 项目服务时长
     */
    String SERVICE_DURATION = "serviceDuration";

    // 搓澡CPV
    /**
     * 搓澡材料（按条件展示）（多值）
     */
    String BATH_MATERIALS = "Bathmaterials";

    /**
     * 额外服务（多值）
     */
    String EXTRA_SERVICE = "ExtraService";

    /**
     * 适用人群
     */
    String SUIT_CROWDS = "suitCrowds";

    // 按摩养生CPV
    /**
     * 服务类型
     */
    String SPU_CATEGORY = "spuCategory";

    /**
     * 服务部位范围
     */
    String BODY_REGION = "bodyRegion";

    /**
     * 局部服务部位（多值）
     */
    String SERVICE_POSITION = "ServicePosition";

    // 餐饮CPV
    /**
     * 餐饮供给
     */
    String CUISINE_TYPES = "CuisineTypes";

    /**
     * 【按条件展示】单点
     *  二级：餐食内容（多值）
     */
    String MENU_FOOD_CONTENT = "MenuFoodContent";

    /**
     * 【按条件展示】自助餐
     *  二级：餐食内容（多值）
     */
    String BUFFET_CONTENT = "Selfservicediningcontent";

    /**
     * 餐饮供餐时间（多值）
     */
    String FOOD_SERVICE_TIME = "foodServiceHours";

    /**
     * 时段
     */
    String FOOD_SERVICE_HOURS = "FoodServiceHours4";

    /**
     * 餐食开始时间
     */
    String MEAL_START_TIME = "Mealcommencementtime";

    /**
     * 餐食结束时间
     */
    String MEAL_END_TIME = "MealEndTime";


    // 玩乐CPV
    /**
     * 玩乐类型
     */
    String PLAY_TYPE = "PlayType";

    /**
     * 时长单位
     */
    String DURATION_UNIT = "DurationUnit";

    // 住宿休憩CPV
    /**
     * 房间种类
     */
    String ROOM_TYPE = "RoomType";

    /**
     * 是否提供餐食
     */
    String IS_PROVIDE_MEAL = "Doyouoffermeals";

    /**
     * 餐食内容【按条件展示】（多值）
     */
    String BREAKFAST_TYPE = "BreakfastType";

    /**
     * 退房时间
     */
    String CHECK_OUT_TIME = "CheckOutTime";

    // 美容spa
    /**
     * 服务效果（多值）
     */
    String SERVICE_EFFECT = "serviceeffect";

    /**
     * 服务类型（按条件展示）（多值）
     */
    String SERVICE_TYPE = "ServiceType17";

}
