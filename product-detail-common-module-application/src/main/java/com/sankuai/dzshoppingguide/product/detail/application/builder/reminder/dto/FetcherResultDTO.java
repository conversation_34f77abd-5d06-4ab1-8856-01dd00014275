package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FetcherResultDTO {
    private ProductBaseInfo productBaseInfo;
    private ProductAttr productAttr;
    private int productSecondCategoryId;
    private ProductServiceProject productServiceProject;
}
