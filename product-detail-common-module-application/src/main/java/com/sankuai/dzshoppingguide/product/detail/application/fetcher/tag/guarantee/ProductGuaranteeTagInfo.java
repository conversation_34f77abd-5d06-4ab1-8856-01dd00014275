package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障标签信息
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ProductGuaranteeTagInfo extends FetcherReturnValueDTO {
    /**
     * 价格保障信息
     */
    private ObjectGuaranteeTagDTO priceProtectionInfo;

    /**
     * 买贵必赔信息
     */
    private ObjectGuaranteeTagDTO bestPriceGuaranteeInfo;

    /**
     * 安心医
     */
    private ObjectGuaranteeTagDTO safeMedicalGuaranteeInfo;

    /**
     * 安心学
     */
    private ObjectGuaranteeTagDTO safeLearnGuaranteeInfo;

    /**
     * 放心种植
     */
    private TagDTO safeImplantTagDTO;

    /**
     * 安心练
     */
    private ObjectGuaranteeTagDTO safeExerciseGuaranteeInfo;
}
