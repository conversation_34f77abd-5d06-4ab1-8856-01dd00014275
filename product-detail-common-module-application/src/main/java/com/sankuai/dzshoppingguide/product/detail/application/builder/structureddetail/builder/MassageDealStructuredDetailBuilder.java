package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.factory.StyleFactory;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.MassageVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-29
 * @desc
 */
@Builder(
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class}
)
public class MassageDealStructuredDetailBuilder extends AbstractStructuredDetailBuilder<MassageVO> {

    @Resource
    private StyleFactory styleFactory;

    @Override
    protected List<Style<MassageVO>> getStyles() {
        return Lists.newArrayList(
                styleFactory.createFreeMealStyle()
        );
    }
}
