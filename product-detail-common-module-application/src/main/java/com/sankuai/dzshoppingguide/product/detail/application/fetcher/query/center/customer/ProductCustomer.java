package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * @Author: guang<PERSON><PERSON><PERSON>
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductCustomer extends FetcherReturnValueDTO {

    /**
     * 业务客户id
     */
    private final long originCustomerId;

    /**
     * 平台客户id
     */
    private final long platformCustomerId;

    public ProductCustomer(long originCustomerId, long platformCustomerId) {
        this.originCustomerId = originCustomerId;
        this.platformCustomerId = platformCustomerId;
    }

}
