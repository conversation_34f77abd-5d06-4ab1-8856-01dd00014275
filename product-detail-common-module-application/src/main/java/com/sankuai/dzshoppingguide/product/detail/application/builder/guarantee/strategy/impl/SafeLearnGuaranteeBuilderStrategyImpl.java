package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 安心学保障条构造策略
 */
public class SafeLearnGuaranteeBuilderStrategyImpl implements GuaranteeBuilderStrategy {
    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.SAFE_LEARN;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        ProductGuaranteeTagInfo productGuaranteeTagInfo = param.getProductGuaranteeTagInfo();
        if (Objects.isNull(productGuaranteeTagInfo) || Objects.isNull(productGuaranteeTagInfo.getSafeLearnGuaranteeInfo())) {
            return null;
        }
        ProductGuaranteeDTO productGuaranteeDTO = new ProductGuaranteeDTO();
        // 保障信息
        ProductDetailGuaranteeVO productDetailGuaranteeVO = new ProductDetailGuaranteeVO();
        productDetailGuaranteeVO.setContents(buildGuaranteeContents());
        productGuaranteeDTO.setProductDetailGuaranteeVO(productDetailGuaranteeVO);
        // 保障浮层配置
//        productDetailGuaranteeVO.setLayer(buildGuaranteeLayerConfig(param.getPageRequest()));
        // 保障浮层
        productGuaranteeDTO.setProductDetailGuaranteeLayerVO(null);
        return productGuaranteeDTO;
    }

    private GuaranteeInstructionsBarLayerVO buildGuaranteeLayerConfig(ProductDetailPageRequest pageRequest) {
        GuaranteeInstructionsBarLayerVO layerConfig = new GuaranteeInstructionsBarLayerVO();
        layerConfig.setJumpUrl(LionConfigUtils.getAnXinXueDetailPage(pageRequest.getClientTypeEnum().isMtClientType()));
//        layerConfig.setType(LayerJumpTypeEnum.JUMP_URL.getCode());
        return layerConfig;
    }

    private List<GuaranteeInstructionsContentVO> buildGuaranteeContents() {
        GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
        content.setText("线上购课 · 按次扣费 · 安心退款");
        Icon icon = new Icon();
        icon.setIcon("https://p0.meituan.net/ingee/f21b4b5a0ce92992a8911a4bfbfa1e6f2519.png");
        icon.setIconHeight(12);
        icon.setIconWidth(48);
        content.setPrefixIcon(icon);
        content.setFontColor("#8E3C12");
        return Collections.singletonList(content);
    }
}
