package com.sankuai.dzshoppingguide.product.detail.application.builder.shop;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShop;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.shop.AvailableShopTag;

@Builder(
        moduleKey = ModuleKeyConstants.SHOP_TAG,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ShopIdMapperFetcher.class,
                ProductBestShopFetcher.class,
        }
)
public class ShopTagBuilder extends BaseBuilder<AvailableShopTag> {

    @Override
    public AvailableShopTag doBuild() {
        ProductBestShop productBestShop = getDependencyResult(ProductBestShopFetcher.class);
        AvailableShopTag availableShopTag = new AvailableShopTag();
        availableShopTag.setFontSize("12");
        availableShopTag.setFontColor("#555555");
        availableShopTag.setIcon(getIcon());
        availableShopTag.setLinkUrl(ShopUrlUtils.getShopListUrl(request));
        availableShopTag.setTagHeader("门店");
        availableShopTag.setTagText(String.format("%d家门店适用", getShopCount(productBestShop)));
        return availableShopTag;
    }

    private Icon getIcon() {
        Icon icon = new Icon();
        icon.setIcon("https://p0.meituan.net/ingee/5bdf750a730fd2999c49e6a32a4753f0424.png");
        icon.setIconHeight(0);
        icon.setIconWidth(0);
        icon.setText("");
        return icon;
    }

    private long getShopCount(ProductBestShop productBestShop) {
        if (productBestShop == null) {
            return 1;
        }
        return productBestShop.getTotalCount();
    }

}
