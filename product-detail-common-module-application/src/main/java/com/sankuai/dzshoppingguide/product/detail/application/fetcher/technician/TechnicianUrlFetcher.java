package com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician;

import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.technician.biz.common.PlatformEnum;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.dianping.technician.dto.TechnicianDetailUrlRequest;
import com.dianping.technician.service.TechnicianUrlService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.technician.query.center.result.TechItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: litengfei04
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class, DoctorFetcher.class},
        timeout = 3000
)
@Slf4j
public class TechnicianUrlFetcher extends NormalFetcherContext<TechnicianUrlReturnValue> {

    @MdpPigeonClient(url = "com.dianping.technician.service.TechnicianUrlService", callType = CallMethod.CALLBACK, timeout = 3000)
    private TechnicianUrlService technicianUrlService;

    @Override
    protected CompletableFuture<TechnicianUrlReturnValue> doFetch() {
        DoctorReturnValue doctorReturnValue = getDependencyResult(DoctorFetcher.class);
        if (doctorReturnValue == null || CollectionUtils.isEmpty(doctorReturnValue.getTechItems())) {
            return CompletableFuture.completedFuture(null);
        }
        List<Integer> technicianList = doctorReturnValue.getTechItems().stream().map(TechItem::getTechId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(technicianList)) {
            return CompletableFuture.completedFuture(null);
        }
        TechnicianDetailUrlRequest urlRequest = buildDetailUrlRequest(technicianList);
        return query(urlRequest)
                .thenApply(r -> {
                    if (r == null || !r.respSuccess()) {
                        return null;
                    }
                    Map<Integer, String> dataMap = (Map<Integer, String>) r.getData();
                    if (MapUtils.isEmpty(dataMap)) {
                        return null;
                    }
                    TechnicianUrlReturnValue technicianReturnValue = new TechnicianUrlReturnValue();
                    technicianReturnValue.setUrlMap(dataMap);
                    return technicianReturnValue;
                })
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "DoctorFetcher")
                            .putTag("method", "doFetch")
                            .message(String.format("doFetch error, request : %s", JsonCodec.encode(urlRequest))));
                    return null;
                });
    }

    private TechnicianDetailUrlRequest buildDetailUrlRequest(List<Integer> techIds) {
        TechnicianDetailUrlRequest technicianDetailUrlRequest = new TechnicianDetailUrlRequest();
        technicianDetailUrlRequest.setTechnicianIds(techIds);
        technicianDetailUrlRequest.setPlatform(request.getClientTypeEnum().isMtClientType() ? PlatformEnum.MEI_TUAN.getValue() : PlatformEnum.DIAN_PING.getValue());
        technicianDetailUrlRequest.setInApp(true);
        return technicianDetailUrlRequest;
    }

    private CompletableFuture<TechnicianResp> query(TechnicianDetailUrlRequest urlRequest) {
        CompletableFuture<TechnicianResp> future = PigeonCallbackUtils.setPigeonCallback(TechnicianResp.class);
        technicianUrlService.batchGetDetailUrl(urlRequest);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "technicianUrlService")
                    .putTag("method", "batchGetDetailUrl")
                    .message(String.format("batchGetDetailUrl error, request : %s, error message:%s", JsonCodec.encodeWithUTF8(urlRequest), e)));
            return null;
        });
        return future;
    }

}