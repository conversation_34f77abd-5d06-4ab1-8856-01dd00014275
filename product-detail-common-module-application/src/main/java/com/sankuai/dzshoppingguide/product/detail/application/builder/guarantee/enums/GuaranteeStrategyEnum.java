package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 保障模块策略枚举
 */
@AllArgsConstructor
@Getter
public enum GuaranteeStrategyEnum {
    DEFAULT(0, "通用保障条展示策略"),
    NONE(1, "不展示，如美团美播小程序、无忧通场景"),
    SAFE_LEARN(2, "安心学团单"),
    TRADE_ASSURANCE(3, "交易保障团单"),
    REASSURED_REPAIR(4, "局改装修-安心改团单"),
    EDU_ONLINE(5, "在线教育团单"),
    GLASSES(6, "眼科安心配镜和正品保障"),
    ORAL_DENTISTRY(7, "口腔齿科"),
    PRICE_PROTECTION(9, "价保标签"),
    PLAY_INSURANCE(10, "游乐险"),
    CLEANING_SELF_OPERATION(11, "保洁自营"),
    PERFORMANCE_GUARANTEE(12, "履约保障标签"),
    ;

    final int code;
    final String desc;
}
