package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

/**
 * <AUTHOR>
 * @date 2025/4/16 19:17
 */
public interface EyesThirdCategory {
    int CHILD_MEDICAL_OPTICAL = 147003; // 儿童医学验光
    int CONTACT_LENS_FITTING = 111000; // 角膜接触镜配镜检查
    int ADULT_MEDICAL_OPTICAL = 154000; // 成人医学验光
    int CHILD_NORMAL_GLASSES = 155000; // 儿童普通眼镜
    int MEDICAL_GLASSES = 854; // 医学配镜
    int DEFOCUS_GLASSES = 146001; // 离焦镜
    int OK_GLASSES = 201; // OK镜
    int DEFOCUS_SOFT_LENS = 117000; // 离焦软镜
    int RGP_LENS = 114002; // RGP镜
    int OTHER_CONTACT_LENS = 145001; // 其他接触镜
    int VISUAL_TRAINING = 113001; // 视觉训练
    int MYOPIA_PRE_SURGERY = 198; // 近视术前检查
    int SMILE = 856; // 全飞秒
    int FEMTOSECOND = 855; // 飞秒
    int EXCIMER = 111002; // 准分子
    int CRYSTAL_SURGERY = 857; // 晶体手术
    int DRY_EYE_EXAM = 112006; // 干眼检查
    int DRY_EYE_LASER = 117001; // 干眼激光
    int DRY_EYE_THERAPY = 146002; // 干眼理疗
}
