package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto.PeriodPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto.ProductItemM;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

public class ReservationPromoUtils {


    public static BigDecimal getPeriodPromoLowestSalePrice(ProductItemM productItemM) {
        List<PeriodPriceM> periodPriceM = productItemM.getPeriodPriceM();
        if (CollectionUtils.isEmpty(periodPriceM)) {
            return getPeriodOriginalSalePriceWithException(productItemM);
        }
        return periodPriceM.stream().filter(e -> e.getPeriodPromoPrices() != null && e.isCanBook()).map(e -> e.getPeriodPromoPrices().getPeriodPromoPrice()).min(Comparator.naturalOrder()).orElse(getPeriodOriginalSalePriceWithException(productItemM));
    }

    public static BigDecimal getPeriodFuturePromoLowestSalePrice(ProductItemM productItemM) {
        List<PeriodPriceM> periodPriceM = productItemM.getPeriodPriceM();
        if (CollectionUtils.isEmpty(periodPriceM)) {
            return productItemM.getOriginalSalePrice();
        }
        return periodPriceM.stream().filter(e -> e.getPeriodFuturePromoPrices() != null && e.isCanBook()).map(e -> e.getPeriodFuturePromoPrices().getPeriodPromoPrice()).min(Comparator.naturalOrder()).orElse(productItemM.getOriginalSalePrice());
    }

    public static boolean isPeriodFuturePromoCheaper(ProductItemM productItemM,boolean userHoldCardStatus ,boolean shopHoldCardStatus) {
        if (!shopHoldCardStatus && !userHoldCardStatus) {
            return false;
        }

        // 判断会员价是否比非会员价便宜
        BigDecimal periodPromoLowestSalePrice = getPeriodPromoLowestSalePrice(productItemM); // 非会员价
        BigDecimal periodFuturePromoLowestSalePrice = getPeriodFuturePromoLowestSalePrice(productItemM); // 会员价
        if (periodFuturePromoLowestSalePrice == null) {
            return false;
        }
        if (periodPromoLowestSalePrice == null) {
            return true;
        }

        // 极端情况，用户持卡下，如果会员价和非会员价相等，优先出会员价promo(400201)展示
        if (userHoldCardStatus && periodPromoLowestSalePrice.compareTo(periodFuturePromoLowestSalePrice) == 0){
            return true;
        }
        return periodPromoLowestSalePrice.compareTo(periodFuturePromoLowestSalePrice) > 0;
    }

    // 兜底展示时段基准价，非优惠价
    public static BigDecimal getPeriodOriginalSalePriceWithException(ProductItemM productItemM) {
        return productItemM.getOriginalSalePrice();
    }

    public static String getPeriodOriginalSalePriceStrWithException(ProductItemM productItemM) {
        if (productItemM.getOriginalSalePrice() == null) {
            return "";
        }
        return productItemM.getOriginalSalePrice().stripTrailingZeros().toPlainString();
    }

}
