package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.combine;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@AllArgsConstructor
public class ProductCombineInfo extends FetcherReturnValueDTO {
    private List<CombineDTO> combines;
}
