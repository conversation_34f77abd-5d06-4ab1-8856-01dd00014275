package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025-05-15
 * @desc
 */
@Getter
public enum ValueOperationTypeEnum {
    CONCAT("concat", "拼接"),
    DATE_FORMAT("dateFormat", "日期格式化"),
    ;

    final String type;
    final String desc;

    ValueOperationTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ValueOperationTypeEnum getByType(String type) {
        return Arrays.stream(ValueOperationTypeEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst().orElse(null);
    }
}
