package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.combine;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCombineBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.CombineTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品组合信息
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductCombineInfoFetcher extends ComponentFetcherContext<QueryByDealGroupIdRequestBuilder, QueryCenterAggregateReturnValue, ProductCombineInfo> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        int productSecondCategoryId = Integer.parseInt(request.getCustomParam(RequestCustomParamEnum.productSecondCategoryId));
        CombineTypeEnum combineTypeEnum = CategoryCombineTypeEnum.getEnumByCategoryId(productSecondCategoryId);
        IdTypeEnum idTypeEnum = request.getClientTypeEnum().isMtClientType() ? IdTypeEnum.MT : IdTypeEnum.DP;
        DealGroupCombineBuilder dealGroupCombineBuilder = DealGroupCombineBuilder.builder(idTypeEnum)
                .combineTypeEnum(combineTypeEnum)
                .shopId(request.getPoiId());
        requestBuilder.combine(dealGroupCombineBuilder);
    }

    @Override
    protected FetcherResponse<ProductCombineInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Optional<ProductCombineInfo> productCombineInfo = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getCombines)
                .map(ProductCombineInfo::new);
        return FetcherResponse.succeed(productCombineInfo.orElse(null));
    }
}
