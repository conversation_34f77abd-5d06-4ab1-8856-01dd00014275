package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.billiards;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/28 21:10
 * @Description: TODO
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                ProductCategoryFetcher.class
        }
)
public class BilliardsFeesDealStructuredDetailBuilder extends AbstractBilliardsDealStructuredDetailBuilder {

    @Override
    protected List<DealDetailStructuredDetailVO> buildFeesDetails(ProductAttr productAttr) {
        return Collections.emptyList();
    }

    @Override
    protected DealDetailStructuredDetailVO buildFeesTitle(ProductAttr productAttr) {
        return null;
    }
}
