package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 免费团购
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/10 19:37
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductAttrFetcher.class}
)
@Slf4j
public class FreeDealReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (productAttr == null || org.apache.commons.collections.CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return null;
        }
        AttrDTO attrDTO = productAttr.getSkuAttrList().stream()
                .filter(a -> "free_product_notice".equals(a.getName())).findFirst()
                .orElse(null);

        if ( attrDTO == null || org.apache.commons.collections.CollectionUtils.isEmpty(attrDTO.getValue())) {
            return null;
        }
        ProductDetailReminderVO productDetailReminderVO = new ProductDetailReminderVO();
        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        productDetailReminderVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        List<String> value = attrDTO.getValue();
        value.forEach(item -> ReminderInfoUtils.buildReminderInfo(item).ifPresent(contents::add));
        productDetailReminderVO.setContents(contents);
        return productDetailReminderVO;
    }
}
