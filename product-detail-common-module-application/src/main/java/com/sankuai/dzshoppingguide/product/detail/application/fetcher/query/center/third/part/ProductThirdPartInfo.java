package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part.dto.SkuThirdPartyDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupThirdPartyDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/6 11:28
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductThirdPartInfo extends FetcherReturnValueDTO {

    /**
     * product三方信息
     */
    private final DealGroupThirdPartyDTO productThirdPartyInfo;

    /**
     * sku三方信息
     * Map<SkuId,SkuThirdPartyDTO>
     */
    private final Map<Long, SkuThirdPartyDTO> skuThirdPartyInfoMap;

    public ProductThirdPartInfo(final DealGroupThirdPartyDTO productThirdPartyInfo,
                                final Map<Long, SkuThirdPartyDTO> skuThirdPartyInfoMap) {
        this.productThirdPartyInfo = productThirdPartyInfo;
        this.skuThirdPartyInfoMap = skuThirdPartyInfoMap;
    }

    public Optional<DealGroupThirdPartyDTO> getProductThirdPartyInfo() {
        return Optional.ofNullable(productThirdPartyInfo);
    }

    public Optional<SkuThirdPartyDTO> getSkuThirdPartyInfo(long skuId) {
        return Optional.ofNullable(skuThirdPartyInfoMap).map(map -> map.get(skuId));
    }

}
