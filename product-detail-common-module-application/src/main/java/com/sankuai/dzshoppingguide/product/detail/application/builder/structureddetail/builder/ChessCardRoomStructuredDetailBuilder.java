package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.factory.StyleFactory;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.ChessCardRoomVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: caisiyuan03
 * @Date: 2025/4/28 20:26
 * @Description: 二级类目: 棋牌室  三级类目: 棋牌套餐 123003
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ProductServiceProjectFetcher.class,
                ProductCategoryFetcher.class
        }
)
public class ChessCardRoomStructuredDetailBuilder extends AbstractStructuredDetailBuilder<ChessCardRoomVO> {

    @Resource
    private StyleFactory styleFactory;

    @Override
    protected List<Style<ChessCardRoomVO>> getStyles() {
        return Lists.newArrayList(
                styleFactory.createChessCardPackagesStyle(),
                styleFactory.createAdditionNotesStyle()
        ).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }
}
