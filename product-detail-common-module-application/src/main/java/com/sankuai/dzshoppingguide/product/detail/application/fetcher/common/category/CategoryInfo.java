package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/15 10:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CategoryInfo extends FetcherReturnValueDTO {
    /**
     * 三级分类名称
     */
    private String categoryName;
}
