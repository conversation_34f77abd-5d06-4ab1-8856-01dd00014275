package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.keyinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.escort.utils.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.GlassesThirdCategory.*;

/**
 * 眼镜
 * <AUTHOR>
 * @date 2025/4/14 20:03
 */
@Component
public class GlassesKeyInfoStrategy extends AbstractSubModuleBuildStrategy {

    @Override
    public String getSubModuleName() {
        return SubModuleKey.KEY_INFO;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        // todo 判断是否是眼镜相关的类目
        return categoryId == SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId();
    }
    
    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> content = Lists.newArrayList();
        ProductCategory productCategory = context.getProductCategory();
        int thirdCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null) {
            return Collections.emptyList();
        }
        Long shopCount = context.getAvailableShopCount();
        switch (thirdCategoryId) {
            case OPTICAL:
                buildOptometryWithoutDot(productAttr, shopCount).ifPresent(content::add);
                buildOptometryData(productAttr).ifPresent(content::add);
                break;
            case CHILD_OPTICAL:
                buildOptometryWithoutDot(productAttr, shopCount).ifPresent(content::add);
                buildOptometryData(productAttr).ifPresent(content::add);
                buildSuitablePeopleWithoutDot(productAttr).ifPresent(content::add);
                break;
            case REPAIR_GLASS:
                buildRepairAndMaintain(productAttr).ifPresent(content::add);
                break;
            case OTHER_GLASS:
                buildServiceType(productAttr).ifPresent(content::add);
                break;
            case INTELLIGENT_GLASS:
                buildBrand(productAttr).ifPresent(content::add);
                buildModel(productAttr).ifPresent(content::add);
                break;
            default:
                break;
        }
        return content;
    }

    /**
     * 验光操作 optometrist+（optometrist_experience）
     */
    private Optional<DealDetailStructuredDetailVO> buildOptometryWithoutDot(ProductAttr productAttr, Long shopCount) {
        // 实现验光操作的构建逻辑
        List<String> optometrist = productAttr.getSkuAttrValue("optometrist2");
        if (CollectionUtils.isEmpty(optometrist)) {
            return Optional.empty();
        }
        String experienceStr = productAttr.getSkuAttrFirstValue("optometrist_experience");
        if (optometrist.size() == 1) {
            if (StringUtils.isNotBlank(experienceStr)) {
                return DealDetailStructuredUtils.buildTitleAndContent("验光操作", String.format("%s（从业%s）", optometrist.get(0), experienceStr));
            }
            // 当optometrist2为单选时，展示[optometrist2+（从业optometrist_experience）]
            return DealDetailStructuredUtils.buildTitleAndContent("验光操作", optometrist.get(0));
        }
        String bubblePopupData = "";
        if (shopCount != null && shopCount > 1) {
            bubblePopupData = JSON.toJSONString(DealDetailStructuredDetailVO.builder()
                    .content("该团购多门店可用，实际验光操作人员可联系商家确认")
                    .build());
        }
        // 当optometrist2为多选（数据类型变更为数组）时，以“/”区隔展示optometrist2数组中的内容+“可选”，不展示optometrist_experience
        return DealDetailStructuredUtils.buildContentAndPopupData("验光操作", String.join("/", optometrist) + "可选", bubblePopupData);
    }

    /**
     * 适用人群
     */
    private Optional<DealDetailStructuredDetailVO> buildSuitablePeopleWithoutDot(ProductAttr productAttr) {
        String people = productAttr.getSkuAttrFirstValue("applicable_population");
        if (StringUtils.isBlank(people)) {
            return Optional.empty();
        }

        if (StringUtils.equals("各年龄段均适用", people)) {
            return DealDetailStructuredUtils.buildTitleAndContent("适用人群", people);
        }

        if (StringUtils.equals("指定年龄范围", people)) {
            String minAge = productAttr.getSkuAttrFirstValue("applicable_age_min");
            String maxAge = productAttr.getSkuAttrFirstValue("applicable_age_max");

            if (StringUtils.isAnyBlank(minAge, maxAge)) {
                return Optional.empty();
            }

            return DealDetailStructuredUtils.buildTitleAndContent("适用人群", String.format("%s岁-%s岁", minAge, maxAge));
        }

        return Optional.empty();
    }


    /**
     * 验光数据
     */
    private Optional<DealDetailStructuredDetailVO> buildOptometryData(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("optometry_data","验光数据",productAttr);
    }

    /**
     * 维修保养服务
     */
    private Optional<DealDetailStructuredDetailVO> buildRepairAndMaintain(ProductAttr productAttr) {
        List<String> skuAttrValues = productAttr.getSkuAttrValues("maintenance");
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Optional.empty();
        }
        String result = skuAttrValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
        return DealDetailStructuredUtils.buildTitleAndContent("维修保养服务",result);
    }

    /**
     * 服务类型
     */
    private Optional<DealDetailStructuredDetailVO> buildServiceType(ProductAttr productAttr) {
        return DealDetailStructuredUtils.buildContentFromAttr("ServiceType9","服务类型",productAttr);
    }

    /**
     * 品牌
     */
    private Optional<DealDetailStructuredDetailVO> buildBrand(ProductAttr productAttr) {
        // 实现品牌的构建逻辑 SmartGlassesBrand
        return DealDetailStructuredUtils.buildContentFromAttr("SmartGlassesBrand","品牌",productAttr);
    }

    /**
     * 型号
     */
    private Optional<DealDetailStructuredDetailVO> buildModel(ProductAttr productAttr) {
        // 实现型号的构建逻辑
        return DealDetailStructuredUtils.buildContentFromAttr("IntelligentGlassesSeries","型号",productAttr);
    }
}
