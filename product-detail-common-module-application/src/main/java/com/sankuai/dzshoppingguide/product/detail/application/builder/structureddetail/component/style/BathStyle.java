package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.BathCommonComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/23 16:07
 * @Description: 二级类目: 洗浴, 包含浴资票、店内服务
 */
@Slf4j
@Component
public class BathStyle implements Style<DealDetailStructuredDetailVO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        ProductCategory productCategory = Optional.ofNullable(context).map(DealDetailBuildContext::getProductCategory).orElse(null);
        ProductStandardServiceProject productStandardServiceProject = Optional.ofNullable(context).map(DealDetailBuildContext::getProductStandardServiceProject).orElse(null);
        if (Objects.isNull(productStandardServiceProject)) {
            return Collections.emptyList();
        }

        List<Optional<List<DealDetailStructuredDetailVO>>> buildResults = Lists.newArrayList(
                // 浴资票(m选n)
                BathCommonComponent.buildBathTicket(productCategory, productStandardServiceProject),
                // 按摩足疗(m选n)
                BathCommonComponent.buildMassage(productCategory, productStandardServiceProject),
                // 搓澡(m选n)
                BathCommonComponent.buildScrub(productCategory, productStandardServiceProject),
                // 餐饮(m选n) || 自助餐(m选n)
                BathCommonComponent.buildFood(productCategory, productStandardServiceProject),
                // 住宿休憩(m选n)
                BathCommonComponent.buildAccommodation(productCategory, productStandardServiceProject),
                // 玩乐(m选n)
                BathCommonComponent.buildPlay(productCategory, productStandardServiceProject),
                // 美容spa(m选n)
                BathCommonComponent.buildSPA(productCategory, productStandardServiceProject),
                // 附加服务(m选n)
                BathCommonComponent.buildAdditionalService(productStandardServiceProject),
                // 以下M选N
                BathCommonComponent.buildOptionalService(productStandardServiceProject),
                // 过夜(只展示免费过夜, 负责过夜在trade服务中)
                BathCommonComponent.buildFreeOvernight(productAttr)
        );

        return buildWithSeparator(buildResults);
    }

    private List<DealDetailStructuredDetailVO> buildWithSeparator(List<Optional<List<DealDetailStructuredDetailVO>>> items) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        boolean hasContent = false;

        DealDetailStructuredDetailVO separator = DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_DIVIDER_STRIP.getType())
                .build();

        for (Optional<List<DealDetailStructuredDetailVO>> item : items) {
            if (item.isPresent() && !item.get().isEmpty()) {
                if (hasContent) {
                    result.add(separator);
                }
                result.addAll(item.get());
                hasContent = true;
            }
        }

        return result;
    }
}
