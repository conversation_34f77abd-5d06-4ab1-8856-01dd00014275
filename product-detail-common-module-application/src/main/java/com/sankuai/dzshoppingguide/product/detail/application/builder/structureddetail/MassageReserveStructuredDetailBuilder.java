package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.AttrConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.Configs;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.config.DetailConfigs;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.BodyPartDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.ServiceProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_1;
import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_2;
import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_3;
import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_4;
import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_5;

/**
 * <AUTHOR>
 * @date 2025-02-27
 * @desc 足疗预订结构化详情模块
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ReserveTagQueryFetcher.class
        }
)
public class MassageReserveStructuredDetailBuilder extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> {
    public static DetailConfigs configs;
    static {
        Lion.addConfigListener(LionConstants.APP_KEY,"default", LionConstants.DETAIL_CONFIG, configEvent -> parse(configEvent.getValue()));
        configs = Lion.getBean(LionConstants.APP_KEY,  LionConstants.DETAIL_CONFIG, DetailConfigs.class, new DetailConfigs());
    }

    public static void parse(String value){
        configs = JSONObject.parseObject(value, DetailConfigs.class);
    }

    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ReserveTagQueryResult reserveTagQueryResult = getDependencyResult(ReserveTagQueryFetcher.class);
        ModuleDetailStructuredDetailVO result = new ModuleDetailStructuredDetailVO();
        try {
            List<DealDetailStructuredDetailVO> dealDetails = Lists.newArrayList();
            result.setDealDetails(dealDetails);

            // 服务流程
            buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);

            // 免费服务
            buildFreeServiceByConfig(productAttr, dealDetails, configs);
            return result;
        } catch (Exception e) {
            log.error("ModuleDetailStructuredDetailBuilder doBuild() error", e);
            return result;
        }
    }

    /**
     * 构造服务流程
     * @param productAttr
     * @param dealDetails
     */
    public void buildServiceItems(ProductAttr productAttr, ReserveTagQueryResult reserveTagQueryResult, List<DealDetailStructuredDetailVO> dealDetails){
        if (Objects.isNull(productAttr)) {
            return;
        }

        // 构造服务标题 项目类型+项目总时长
        buildServiceTitle(productAttr, reserveTagQueryResult, dealDetails);

        // 服务部位
        String bodyPartStr = productAttr.getSkuAttrFirstValue("bodyPart");
        buildBodyPart(bodyPartStr, dealDetails);

        // 服务流程
        String serviceProcessStr = productAttr.getSkuAttrFirstValue("serviceProcess");
        buildServiceProcess(serviceProcessStr, dealDetails);
    }

    // 构造服务标题
    public void buildServiceTitle(ProductAttr productAttr, ReserveTagQueryResult reserveTagQueryResult, List<DealDetailStructuredDetailVO> dealDetails){
        // 构造服务标题 项目类型+项目总时长
        String serviceType = Optional.ofNullable(reserveTagQueryResult).map(ReserveTagQueryResult::getMetaTagDTO).map(MetaTagDTO::getValue).orElse(org.apache.commons.lang.StringUtils.EMPTY);

        // 项目时长
        String content = getDuration(productAttr.getSkuAttrFirstValue("duration"));
        DealDetailStructuredDetailVO serviceTitle = DealDetailStructuredDetailVO.builder()
                .type(DETAIL_TYPE_1.getType())
                .title(serviceType)
                .content(content)
                .build();
        dealDetails.add(serviceTitle);
    }

    public String getDuration(String duration){
        if (StringUtils.isNotBlank(duration)){
            return duration + "分钟";
        }
        return null;
    }

    public void buildBodyPart(String bodyPartStr, List<DealDetailStructuredDetailVO> dealDetails){
        DealDetailStructuredDetailVO bodyPart =
                DealDetailStructuredDetailVO.builder()
                        .type(DETAIL_TYPE_2.getType())
                        .title("服务部位")
                        .content(getBodyPart(bodyPartStr))
                        .build();
        dealDetails.add(bodyPart);
    }

    public String getBodyPart(String bodyPartStr){
        if (StringUtils.isBlank(bodyPartStr)){
            return null;
        }
        List<BodyPartDTO> bodyPartDTOS = JSONObject.parseArray(bodyPartStr, BodyPartDTO.class);
        List<String> bodyParts = bodyPartDTOS.stream().map(e -> e.getName()).collect(Collectors.toList());
        return joinListByDelimiter(bodyParts, "、");
    }

    public String joinListByDelimiter(List<String> values, String delimiter){
        if (CollectionUtils.isEmpty(values) || Objects.isNull(delimiter)) {
            return null;
        }
        return String.join(delimiter, values);
    }

    public void buildServiceProcess(String serviceProcessStr, List<DealDetailStructuredDetailVO> dealDetails){
        if (StringUtils.isBlank(serviceProcessStr)){
            return;
        }
        List<ServiceProcessDTO> serviceProcessDTOS = null;
        try {
            serviceProcessDTOS = JSONObject.parseArray(serviceProcessStr, ServiceProcessDTO.class);
        } catch (Exception e) {
            log.error("buildServiceProcess error, serviceProcessStr:{}, productId={}", serviceProcessStr, request.getProductId(), e);
        }
        if (CollectionUtils.isEmpty(serviceProcessDTOS)) {
            return;
        }
        // 保证服务流程的顺序是按照No 从小到大排序的
        sortServiceProcessList(serviceProcessDTOS);
        for (int i = 0; i <serviceProcessDTOS.size(); i++) {
            ServiceProcessDTO serviceProcessDTO = serviceProcessDTOS.get(i);
            DealDetailStructuredDetailVO item ;

            String title = null;
            int order = serviceProcessDTO.getNo();
            String content =  serviceProcessDTO.getBodyName();
            String subContent = joinListByDelimiter(serviceProcessDTO.getServiceProduct(), "、");
            String detail = Objects.nonNull(serviceProcessDTO.getDuration()) ? stripDuration(serviceProcessDTO.getDuration()) + "分钟" : null;

            if (order == 1){
                title = "服务流程";
            }
            item = DealDetailStructuredDetailVO.builder()
                    .type(DETAIL_TYPE_3.getType())
                    .title(title)
                    .content(content)
                    .subContent(subContent)
                    .detail(detail)
                    .order(order)
                    .build();
            dealDetails.add(item);
        }
    }

    private String stripDuration(String duration) {
        if (StringUtils.isBlank(duration)) {
            return null;
        }
        return StringUtils.strip(duration, " ");
    }

    /**
     * 对 List<ServiceProcessDTO> 按照 no 从小到大排序
     *
     * @param serviceProcessList 需要排序的列表
     */
    public void sortServiceProcessList(List<ServiceProcessDTO> serviceProcessList) {
        if (serviceProcessList == null || serviceProcessList.isEmpty()) {
            return; // 如果列表为空或为 null，直接返回
        }

        // 使用 Comparator 定义排序规则
        Comparator<ServiceProcessDTO> comparator = Comparator.comparingInt(ServiceProcessDTO::getNo);

        // 对列表进行排序
        Collections.sort(serviceProcessList, comparator);
    }

    // 通过配置构造 免费服务
    public void buildFreeServiceByConfig(ProductAttr productAttr, List<DealDetailStructuredDetailVO> dealDetails, DetailConfigs configs) {
        if (Objects.isNull(productAttr)) {
            return;
        }
        String title = productAttr.getSkuAttrFirstValue("freeFood");
        if (StringUtils.isBlank(title)){
            return;
        }
        if (configs == null || configs.getFreeFoodConfigs() == null) {
            return;
        }
        List<Configs> freeFoodConfigs = configs.getFreeFoodConfigs();
        Map<String, List<AttrConfig>> attrValueToConfig = freeFoodConfigs.stream().collect(Collectors.toMap(e->e.getUpperAttr().getAttrCnName(), Configs::getConfigs));

        List<AttrConfig> attrConfigs = attrValueToConfig.get(title);
        if (CollectionUtils.isEmpty(attrConfigs)) {
            return;
        }

        // 构造免费服务标题
        buildFreeServiceTitle(productAttr, dealDetails);

        // 构造免费服务项目
        attrConfigs.forEach(attrConfig -> {
            String attrValuesStr = productAttr.getSkuAttrFirstValue(attrConfig.getAttrName());
            List<String> attrValues = DealAttrHelper.buildContents(attrValuesStr);
            buildFreeServiceItem(attrConfig.getDisplayName(), ProductAttr.joinListByDelimiter(attrValues, attrConfig.getDelimiter()) , attrConfig.getIcon(), dealDetails);

        });
    }

    public void buildFreeServiceTitle(ProductAttr productAttr, List<DealDetailStructuredDetailVO> dealDetails){

        String title = productAttr.getSkuAttrFirstValue("freeFood");
        String content = buildMealValue(productAttr.getSkuAttrFirstValue("MealValue"));

        DealDetailStructuredDetailVO freeServiceTitle = DealDetailStructuredDetailVO.builder()
                .type(DETAIL_TYPE_4.getType())
                .title(title)
                .content(content)
                .detail("免费")
                .build();
        dealDetails.add(freeServiceTitle);
    }

    public String buildMealValue(String mealValue){
        if (StringUtils.isBlank(mealValue)){
            return null;
        }
        return "价值￥" + mealValue;
    }

    public void buildFreeServiceItem(String itemTitle, String itemContent, String icon, List<DealDetailStructuredDetailVO> dealDetails){
        if (StringUtils.isBlank(itemContent)){
            return;
        }
        DealDetailStructuredDetailVO title = DealDetailStructuredDetailVO.builder()
                .type(DETAIL_TYPE_5.getType())
                .title(itemTitle)
                .content(itemContent)
                .icon(icon)
                .build();
        dealDetails.add(title);
    }
}
