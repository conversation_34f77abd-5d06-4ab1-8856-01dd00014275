package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.buy.DealGroupBuyRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * 帮助构造有效期
 * <p>
 * 当receiptDateType==0时返回购买后x至x有效
 * 当receiptDateType==1时返回购买后receiptValidDays天内有效
 */
@Slf4j
public class EffectiveDateHelper {

    public static final String EDU_VOCATIONAL_ZERO_EFFECTIVE = "每人限购%s次*购买后商家将致电为您服务";

    public static String getEffectiveDate(DealCtx dealCtx) {
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = getReceiptEffectiveDateDTO(dealCtx);
        if (receiptEffectiveDateDTO == null) {
            return "";
        }
        if (receiptEffectiveDateDTO.getReceiptDateType() != null) {
            if (receiptEffectiveDateDTO.getReceiptDateType() == 0
                    && receiptEffectiveDateDTO.getReceiptBeginDate() != null && receiptEffectiveDateDTO.getReceiptEndDate() != null) {
                String beginDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptBeginDate()));
                String endDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptEndDate()));
                return (beginDate == null && endDate == null) ? "" : String.format("%s至%s有效", beginDate, endDate);
            }
            if (receiptEffectiveDateDTO.getReceiptDateType() == 1
                    && receiptEffectiveDateDTO.getReceiptValidDays() != null) {
                return "购买后" + receiptEffectiveDateDTO.getReceiptValidDays() + "天内有效";
            }
        }
        return "";
    }

    /**
     * 在线课的有效期
     *
     * @param dealCtx
     * @return
     */
    public static String getEffectiveDateForOnlineCourse(DealCtx dealCtx) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.helper.EffectiveDateHelper.getEffectiveDateForOnlineCourse(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx)");
        if (dealCtx == null) {
            return "";
        }
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = getReceiptEffectiveDateDTO(dealCtx);
        if (receiptEffectiveDateDTO == null) {
            return "";
        }
        if (receiptEffectiveDateDTO.getReceiptDateType() != null) {
            if (receiptEffectiveDateDTO.getReceiptDateType() == 0
                    && receiptEffectiveDateDTO.getReceiptBeginDate() != null && receiptEffectiveDateDTO.getReceiptEndDate() != null) {
                String beginDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptBeginDate()));
                String endDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptEndDate()));
                return (beginDate == null && endDate == null) ? "" : String.format("有效期至%s", endDate);
            }
            if (receiptEffectiveDateDTO.getReceiptDateType() == 1
                    && receiptEffectiveDateDTO.getReceiptValidDays() != null) {
                return "开课后" + receiptEffectiveDateDTO.getReceiptValidDays() + "天内有效";
            }
        }
        return "";
    }

    private static ReceiptEffectiveDateDTO getReceiptEffectiveDateDTO(DealCtx dealCtx) {
        Optional<ReceiptEffectiveDateDTO> receiptEffectiveDateDTOOptional = Optional.of(dealCtx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getReceiptEffectiveDate);
        return receiptEffectiveDateDTOOptional.orElse(null);
    }

    public static String getEffectiveDateForVoCaEduZero(DealCtx dealCtx) {
        if (dealCtx == null) {
            return "";
        }
        Integer maxPerUser = Optional.of(dealCtx)
                .map(DealCtx::getDealGroupDTO)
                .map(DealGroupDTO::getRule)
                .map(DealGroupRuleDTO::getBuyRule)
                .map(DealGroupBuyRuleDTO::getMaxPerUser)
                .orElse(0);
        if (maxPerUser == null || maxPerUser < 1) {
            return getEffectiveDate(dealCtx);
        }
        return String.format(EDU_VOCATIONAL_ZERO_EFFECTIVE, maxPerUser);
    }

    public static String getEffectiveDate(ProductBaseInfo baseInfo) {
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = getReceiptEffectiveDateDTO(baseInfo);
        if (receiptEffectiveDateDTO==null){
            return "";
        }
        if (receiptEffectiveDateDTO.getReceiptDateType() != null) {
            if (receiptEffectiveDateDTO.getReceiptDateType() == 0
                    && receiptEffectiveDateDTO.getReceiptBeginDate() != null && receiptEffectiveDateDTO.getReceiptEndDate() != null) {
                String beginDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptBeginDate()));
                String endDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptEndDate()));
                return (beginDate == null && endDate == null) ? "" : String.format("%s至%s有效", beginDate, endDate);
            }
            if (receiptEffectiveDateDTO.getReceiptDateType() == 1
                    && receiptEffectiveDateDTO.getReceiptValidDays() != null) {
                return "购买后" + receiptEffectiveDateDTO.getReceiptValidDays() + "天内有效";
            }
        }
        return "";
    }

    private static ReceiptEffectiveDateDTO getReceiptEffectiveDateDTO(ProductBaseInfo baseInfo) {
        return Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getReceiptEffectiveDate).orElse(null);
    }
}
