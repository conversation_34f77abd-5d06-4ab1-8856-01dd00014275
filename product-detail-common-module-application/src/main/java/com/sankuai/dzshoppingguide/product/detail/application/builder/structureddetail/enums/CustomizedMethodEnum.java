package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomizedMethodEnum {

    /**
     * 眼镜-适用度数
     */
    GlassApplicableDegrees("getGlassApplicableDegrees"),

    /**
     * 眼镜-品牌
     */
    EyeglassFrameInfo("getEyeglassFrameInfo"),

    /**
     * 眼科-眼镜框选择信息
     */
    EyeglassFrameSelection("getEyeglassFrameSelection"),

    /**
     * 眼科-品牌
     */
    LensSelectionInfo("getLensSelectionInfo"),

    /**
     * 眼科度数范围
     */
    EyeDegreeRanges("getEyeDegreeRanges"),


    ;

    private String method;



}
