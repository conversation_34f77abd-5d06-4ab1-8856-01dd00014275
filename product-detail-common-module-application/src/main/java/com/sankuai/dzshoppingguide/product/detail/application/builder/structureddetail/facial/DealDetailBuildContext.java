package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.CategoryInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule.ProductReadjustPriceRule;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.DoctorReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.technician.TechnicianUrlReturnValue;
import lombok.Builder;
import lombok.Data;

/**
 * Context class to hold all parameters needed for building deal details
 */
@Data
@Builder
public class DealDetailBuildContext {
    private ProductAttr productAttr;
    private ReserveTagQueryResult reserveTagQueryResult;
    private ProductServiceProject productServiceProject;
    private ProductStandardServiceProject productStandardServiceProject;
    private ProductCategory productCategory;
    private CategoryInfo categoryInfo;
    private DoctorReturnValue doctorReturnValue;
    private TechnicianUrlReturnValue technicianUrlReturnValue;
    private ProductDetailPageRequest request;
    private Long availableShopCount;
    private AdditionInfoResult additionInfo;
    private ProductReadjustPriceRule productReadjustPriceRule;
    private SkuDefaultSelect skuDefaultSelect;


}