package com.sankuai.dzshoppingguide.product.detail.application.enums;

/**
 * 点评筛选规则
 * Created by jason on 15/3/20.
 */
public class ReviewFilterType {

    // 无排序
    public static final int RANK_NONE = 0;

    // 默认点评排序
    public static final int RANK_DEFAULT = 1;

    // 团购点评
    public static final int RANK_TUANGOU = 2;

    // 更新时间倒排
    public static final int RANK_LASTTIME = 3;

    // UGC质量分排序
    public static final int RANK_QUALITY = 4;

    // 好评：五星和四星
    public static final int RANK_GOOD = 5;

    // 差评：一星和两星
    public static final int RANK_BAD = 6;

    // 摘要排序
    public static final int RANK_ABSTRACT = 7;

    // 算法提供的排序
    public static final int RANK_ALGO = 8;

    // 好友点评
    public static final int RANK_FRIEND = 9;

    // 带图点评
    public static final int RANK_PICTURE = 10;

}
