package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.config;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-14
 * @desc 结构化详情数据处理层配置
 */
@Data
public class StructuredDetailDataProcessLayerConfig implements Serializable {
    private ViewComponentTypeEnum componentType;
    private List<DataProcessMapper> mapping;
}
