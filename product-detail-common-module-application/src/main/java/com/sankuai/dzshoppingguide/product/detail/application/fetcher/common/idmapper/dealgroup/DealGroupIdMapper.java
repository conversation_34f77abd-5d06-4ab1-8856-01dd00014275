package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/13 11:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealGroupIdMapper extends FetcherReturnValueDTO {
    private long mtDealGroupId;
    private long dpDealGroupId;
}
