package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.AttrConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.Configs;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.FacilitiesVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.facilities.vo.DetailServiceFacilitiesVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2025-02-28
 * @desc 足疗按摩预订服务设施
 */
@Slf4j
@Builder(
        moduleKey = ModuleKeyConstants.DEAL_DETAIL_FACILITIES,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {CommonModuleStarter.class, ProductAttrFetcher.class}
)
public class MassageReserveServiceFacilityBuilder extends BaseVariableBuilder<DetailServiceFacilitiesVO> {

    public static Configs configs;
    static {
        Lion.addConfigListener(LionConstants.APP_KEY,"default", LionConstants.DETAIL_FACILITY_CONFIG, configEvent -> parse(configEvent.getValue()));
        configs = LionConfigUtils.getFacilityConfigs();
    }

    public static void parse(String value){
        configs = JSONObject.parseObject(value, Configs.class);
    }

    @Override
    public DetailServiceFacilitiesVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        DetailServiceFacilitiesVO result = new DetailServiceFacilitiesVO();
        try{
            List<FacilitiesVO> detailServiceFacilities = Lists.newArrayList();
            result.setDetailServiceFacilities(detailServiceFacilities);

            // 服务设施详情
            buildFacilitiesDetail(productAttr, detailServiceFacilities);
            return result;
        }catch (Exception e){
            log.error("DetailServiceFacilitiesBuilder doBuild() error ", e);
            return result;
        }
    }

    /**
     * 构造服务设施详情
     * @param productAttr
     * @param detailServiceFacilities
     */
    public void buildFacilitiesDetail(ProductAttr productAttr, List<FacilitiesVO> detailServiceFacilities){
        if ( Objects.isNull(productAttr)){
            return;
        }
        String serviceMaterialAndTool = productAttr.getSkuAttrFirstValue("serviceMaterialAndTool");
        if (StringUtils.isBlank(serviceMaterialAndTool)) {
            return;
        }
        // 服务设施
        buildFacilities(productAttr, detailServiceFacilities, configs);
    }

    public void buildFacilityTitle(AttrConfig config, List<FacilitiesVO> detailServiceFacilities){
        FacilitiesVO facilityTitle = FacilitiesVO.builder()
                .type(FACILITY_TYPE_1.getType())
                .title(config.getDisplayName())
                .build();
        detailServiceFacilities.add(facilityTitle);
    }

    public void buildFacilities(ProductAttr productAttr, List<FacilitiesVO> detailServiceFacilities, Configs configs){
        List<AttrConfig> configsList = configs.getConfigs();
        // 构造服务设施标题
        AttrConfig titleConfig = configs.getUpperAttr();
        buildFacilityTitle(titleConfig, detailServiceFacilities);

        configsList.forEach(config -> {
            // 构造服务设施项目
            String attrValue = productAttr.getSkuAttrFirstValue(config.getAttrName());
            if (StringUtils.isNotBlank(attrValue)){
                FacilitiesVO facilityItemTitle = FacilitiesVO.builder()
                        .type(FACILITY_TYPE_2.getType())
                        .title(config.getDisplayName())
                        .build();
                detailServiceFacilities.add(facilityItemTitle);

                FacilitiesVO facilityItemDetail = FacilitiesVO.builder()
                        .type(FACILITY_TYPE_3.getType())
                        .contents(DealAttrHelper.buildContents(attrValue))
                        .build();
                detailServiceFacilities.add(facilityItemDetail);
            }
        });
    }
}
