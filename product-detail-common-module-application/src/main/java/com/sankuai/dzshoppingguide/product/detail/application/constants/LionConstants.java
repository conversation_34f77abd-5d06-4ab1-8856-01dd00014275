package com.sankuai.dzshoppingguide.product.detail.application.constants;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 15:45
 */
public interface LionConstants {
    String DZTGDETAIL_APPKEY = "com.sankuai.dzu.tpbase.dztgdetailweb";
    String RESERVE_APPKEY = "com.sankuai.dzviewscene.reserve";
    String COMPRESS_VIDEO_COVER_PIC = "com.sankuai.dzu.tpbase.dztgdetailweb.compress.video.cover.pic";

    // 是否下发3:4尺寸类目ID白名单
    String DISTRIBUTE_IMAGE_SCALE_WHITELIST = "com.sankuai.dzu.tpbase.dztgdetailweb.distribute.image.scale.whitelist";

    String PICSIZE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.picaspectratio.picsize.config";

    String STOCK_GRANULARITY = "com.sankuai.dzviewscene.reserve.mid.stock.granularity.config";

    String APP_KEY = "com.sankuai.dzshoppingguide.detail.commonmodule";
    String NAV_BAR_ITEM_LIST = "com.sankuai.dzshoppingguide.detail.commonmodule.nav.bar.item.list";
    String DETAIL_FACILITY_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.detail.facility.config";
    String DEAL_TIMES_CARD_REFUND_TABLE = "deal.times.card.refund.table.switch";
    String DETAIL_FREE_FOOD_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.detail.free.food.config";
    String DETAIL_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.detail.config";
    String TAGID_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.tagid.config";

    /**
     * 强预订团单类目
     */
    String PRE_ORDER_CATEGORY_IDS = "pre.order.category.ids";

    String DEAL_TIMES_CARD_REFUND_CUSTOMER_WHITELIST = "deal.timescard.refund.info.customer.whiteList";
    /**
     * 须知条定制逻辑类目配置
     */
    String CUSTOM_REMINDER_INFO_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.reminder.info.config";
    String FORCE_BOOKING_PUBLISH_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.force.booking.publish.category";
    /**
     * 【使用场景】在指定的团单二级类目下，预约信息为空的团单，不显示预约信息
     * 【值描述】团单二级类目
     */
    String RESERVATION_EMPTY_NOT_DISPLAY_TYPE = "reservation.empty.not.display.type";
    String AVAILABLE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.available.category.ids";
    // 自定义退改条款类目白名单
    String CUSTOM_REFUND_CATGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.refund.category.config";
    //价保浮层
    String LAYER_CONFIGS_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.mt";
    String LAYER_CONFIGS_MT_UNRETURN = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.mt.unreturn";
    //价保浮层
    String LAYER_CONFIGS_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.dp";
    String LAYER_CONFIGS_DP_UNRETURN = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.dp.unreturn";

    /**
     * 收藏商品跳转链接
     */
    String COLLECTED_PRODUCT_URL = "collected.product.url";

    String FLOW_ENTRY_WX_MATERIAL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.flowEntryWxMaterial.config";

    String FREE_DEAL_INFO_CONFIG_LIST_LION_KEY = "free.deal.category.list";

    String EDU_ONLINE_DEAL_SERVICE_LEAF_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.online.serviceleafids";

    /**
     * 安心学适用类目
     */
    String GUARANTEE_AN_XIN_XUE_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue";

    /**
     * 定制化可用时间适用行业类目ID
     * 根据属性key：times_available_all 定制化可用时间
     */
    String CUSTOM_AVAILABLE_TIMES_OF_DAYS = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.available.times.of.days";

    String SUPPORT_ADDITIONAL_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.supportAdditionalCategory";

    // 无忧通商品属性value配置，默认key：
    String WU_YOU_TONG_ATTR_VALUES = "com.sankuai.dzu.tpbase.dztgdetailweb.wuyoutong.attr.values";

    // LE预付款团购
    String TRADE_ASSURANCE_LAYER_CONFIGS = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.layerConfigs.trade.assurance";

    String GLASS_GENUINE_GUARANTEE_CONFIG = "glass.genuine.guarantee.tag.config";

    /**
     * 展示安心植牙标签类目
     */
    String SHOW_SAFE_IMPLANT_TAG_CATEGORY_IDS = "show.safe.implant.tag.category.ids";

    /*
     * 医疗zdc标签
     * */
    String MEDICAL_ZDC_TAG_ID = "com.sankuai.dzu.tpbase.dztgdetailweb.medical.safe.treat.zdc.tagid";

    //价保浮层categoryId
    String PRICE_PROTECTION_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.priceprotection.categoryIds";

    /**
     * 履约保障标签配置-保障标签和浮层
     */
    String PERFORMANCE_GUARANTEE_FEATURE_DETAIL_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.performance.guarantee.feature.detail.config";
    /**
     * 安心学落地页
     */
    String GUARANTEE_AN_XIN_XUE_DETAIL_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.detail.mt";
    String GUARANTEE_AN_XIN_XUE_DETAIL_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.xue.detail.dp";

    /**
     * 安心练落地页
     */
    String GUARANTEE_AN_XIN_EXERCISE_DETAIL_MT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.detail.mt";
    String GUARANTEE_AN_XIN_EXERCISE_DETAIL_DP = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.detail.dp";
    String GUARANTEE_AN_XIN_EXERCISE_ICON = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.icon";
    String GUARANTEE_AN_XIN_EXERCISE_DISPLAY_TEXT = "com.sankuai.dzu.tpbase.dztgdetailweb.guarantee.an.xin.exercise.display.text";



    // 团单保障浮层配置
    String DEAL_GUARANTEE_LAYER_CONFIG = "deal.guarantee.layer.config";

    /**
     *限回头客榜单及复购信息透传--限制条适用行业类目ID
     */
    String LIMIT_INFO_SUIT_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.limitinfo.category.config";

    /**
     * 通用浮层配置
     */
    String GENERAL_LAYER_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.general.layer.config";

    String DENTAL_MATERIAL_DETAIL = "dental.standardization.material.detail";
    String GLASSES_MATERIAL_DETAIL = "glasses.material.detail";
    String MATERIAL_COMPARISON_PIC = "material.comparison.pic.url";
    // 单值数组属性列表
    String SINGLE_VALUE_ARRAY_ATTR_LIST = "single.value.array.attr.list";

    /**
     * 足疗按摩、茶馆、棋牌室、游戏厅、台球团详及上单改造版本控制
     */
    String COMMON_MODULE_CALL_VERSION_CONFIG = "common.module.service.call.version.config";

    /**
     * 商品结构（CPV）变更自动联动 影响分析 配置
     */
    String CPV_IMPACT_ANALYSE_CONFIG = "cpv.impact.analyse.config";

    String PRODUCT_ATTR_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.product.attr.config";

    /**
     * 结构化团详数据处理层配置
     */
    String STRUCTURED_DETAIL_DATA_PROCESS_LAYER_CONFIG = "structured.detail.data.process.layer.config";

    /**
     * 新团详standardServiceProject取值中的多值属性列表
     */
    String MULTI_VALUE_ARRAY_SERVICE_PROJECT_LIST = "multi.value.array.service.project.list";
}