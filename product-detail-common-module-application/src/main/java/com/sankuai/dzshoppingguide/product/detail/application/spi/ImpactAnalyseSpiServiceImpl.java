package com.sankuai.dzshoppingguide.product.detail.application.spi;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CpvImpactAnalyseConfig;
import com.sankuai.nibscp.cpv.api.remote.dto.DiffDTO;
import com.sankuai.spt.ark.api.dto.lineage.SubjectChangeInfoDTO;
import com.sankuai.spt.ark.api.dto.lineage.UsageImpactDTO;
import com.sankuai.spt.ark.api.dto.lineage.UsageImpactFieldDetailDTO;
import com.sankuai.spt.ark.api.dto.lineage.UsageInfoDTO;
import com.sankuai.spt.ark.common.enums.lineage.CascadeActionTypeEnum;
import com.sankuai.spt.ark.common.enums.lineage.LineageSubjectTypeEnum;
import com.sankuai.spt.ark.common.enums.lineage.SubjectChangeOperateTypeEnum;
import com.sankuai.spt.ark.spi.lineage.ImpactAnalyseSpiService;
import com.sankuai.spt.ark.spi.lineage.dto.ImpactAnalyseResultSpiDTO;
import com.sankuai.spt.ark.spi.lineage.request.ImpactAnalyseSpiRequest;
import com.sankuai.spt.ark.spi.lineage.response.ImpactAnalyseSpiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/05/22
 */
@Slf4j
@MdpPigeonServer(url = "com.sankuai.dzshoppingguide.product.detail.application.spi.ImpactAnalyseSpiServiceImpl")
public class ImpactAnalyseSpiServiceImpl implements ImpactAnalyseSpiService {

    private static CpvImpactAnalyseConfig config;
    private static List attrConfigList;
    @Override
    public ImpactAnalyseSpiResponse analyseImpact(ImpactAnalyseSpiRequest request) {
        log.info("ImpactAnalyseSpiServiceImpl.analyseImpact star");
        if (request == null || request.getSubjectChangeInfo() == null) {
            return null;
        }
        SubjectChangeInfoDTO subjectChangeInfoDTO = request.getSubjectChangeInfo();
        ImpactAnalyseResultSpiDTO resultSpiDTO = null;
        try {
            config = LionConfigUtils.getCpvImpactAnalyseConfig();
            resultSpiDTO = buildImpactAnalyseResultSpiDTO(subjectChangeInfoDTO);
        } catch (Exception e) {
            log.error("ImpactAnalyseSpiServiceImpl.analyseImpact error", e);
        }
        log.info("ImpactAnalyseSpiServiceImpl.analyseImpact end");
        return ImpactAnalyseSpiResponse.buildSuccessResp(resultSpiDTO, ImpactAnalyseSpiResponse.class);
    }

    private ImpactAnalyseResultSpiDTO buildImpactAnalyseResultSpiDTO(SubjectChangeInfoDTO subjectChangeInfoDTO) {

        ImpactAnalyseResultSpiDTO resultSpiDTO = new ImpactAnalyseResultSpiDTO();
        // 设置分析结果未完成
        resultSpiDTO.setCompleted(false);
        // 设置影响提示文案
        resultSpiDTO.setImpactTip(buildImpactTip(subjectChangeInfoDTO.getOperateType()));
        // 设置影响分析结果
        resultSpiDTO.setUsageImpactList(impactAnalyse(subjectChangeInfoDTO));
        // 设置分析结果已完成
        resultSpiDTO.setCompleted(true);
        return resultSpiDTO;
    }

    /**
     * 影响分析
     * @param subjectChangeInfoDTO
     * @return
     */
    private List<UsageImpactDTO> impactAnalyse(SubjectChangeInfoDTO subjectChangeInfoDTO) {
        String subjectDiffDataStr = subjectChangeInfoDTO.getSubjectDiffData();
        List<UsageImpactDTO> usageImpactDTOList = Lists.newArrayList();
        Map<String, String> extMap = subjectChangeInfoDTO.getExtMap();
        String bpCategoryId = extMap.get(Constants.BP_CATEGORY_ID);
        String lionKey = buildLionKey(bpCategoryId);
        // 获取商品属性配置
        Map<String, List> productAttrConfigMap = LionConfigUtils.getProductAttrConfigMap();
        attrConfigList = CollectionUtils.isNotEmpty(productAttrConfigMap.get(lionKey)) ? productAttrConfigMap.get(lionKey) : Collections.EMPTY_LIST;
        // 分析阻断结果
        if(StringUtils.isBlank(subjectDiffDataStr)){
            return usageImpactDTOList;
        }
        DiffDTO diffDTO = JSON.parseObject(subjectDiffDataStr, DiffDTO.class);
        if(diffDTO == null){
            return usageImpactDTOList;
        }
        usageImpactDTOList.add(buildUsageImpactDTO(diffDTO,extMap));

        return usageImpactDTOList;
    }

    private String buildLionKey(String secondProductCategory) {
        return String.format("%s-%s", ProductTypeEnum.DEAL.getCode(), secondProductCategory);
    }

    /**
     * 构建影响提示文案
     * @param operateType
     * @return
     */
    private String buildImpactTip(Integer operateType) {
        if(SubjectChangeOperateTypeEnum.ADD.getCode() == operateType.intValue()){
            return config.getImpactTipAdd();
        }
        if(SubjectChangeOperateTypeEnum.DELETE.getCode() == operateType.intValue()){
            return config.getImpactTipDelete();
        }
        if(SubjectChangeOperateTypeEnum.UPDATE.getCode() == operateType.intValue()){
            return config.getImpactTipUpdate();
        }
        return "";
    }

    /**
     * 构建影响分析结果
     * @return
     */
    private UsageImpactDTO buildUsageImpactDTO(DiffDTO diffDTO,Map<String,String> extMap) {
        UsageImpactDTO usageImpactDTO = new UsageImpactDTO();
        usageImpactDTO.setUsageInfoDTO(buildUsageInfoDTO(extMap));
        List<UsageImpactFieldDetailDTO> usageImpactFieldDetailDTOList = buildUsageImpactFieldDetailDTO(diffDTO);
        usageImpactDTO.setFieldImpactDetails(usageImpactFieldDetailDTOList);
        boolean hasImpact = isHasImpact(usageImpactFieldDetailDTOList);
        usageImpactDTO.setHasImpact(hasImpact);
        usageImpactDTO.setImpact(buildUsageImpactDTOImpact(hasImpact));
        usageImpactDTO.setCascadeAction(buildCascadeAction(usageImpactFieldDetailDTOList).getCode());
        usageImpactDTO.setCascadeExecutionData("");
        return usageImpactDTO;
    }

    public CascadeActionTypeEnum buildCascadeAction(List<UsageImpactFieldDetailDTO> usageImpactFieldDetailDTOList) {
        if (CollectionUtils.isEmpty(usageImpactFieldDetailDTOList)) {
            return CascadeActionTypeEnum.NOT_ABORT;
        }
        // 检查是否存在 ABORT
        boolean hasAbort = usageImpactFieldDetailDTOList.stream()
                .anyMatch(dto -> Objects.equals(dto.getUsageCascadeAction(), CascadeActionTypeEnum.ABORT.getCode()));
        if (hasAbort) {
            return CascadeActionTypeEnum.ABORT;
        }
        // 检查是否存在 NOT_ABORT
        boolean hasNotAbort = usageImpactFieldDetailDTOList.stream()
                .anyMatch(dto -> Objects.equals(dto.getUsageCascadeAction(), CascadeActionTypeEnum.NOT_ABORT.getCode()));
        if (hasNotAbort) {
            return CascadeActionTypeEnum.NOT_ABORT;
        }
        // 检查是否存在 CASCADE_EXECUTE
        boolean hasCascadeExecute = usageImpactFieldDetailDTOList.stream()
                .anyMatch(dto -> Objects.equals(dto.getUsageCascadeAction(), CascadeActionTypeEnum.CASCADE_EXECUTE.getCode()));
        if (hasCascadeExecute) {
            return CascadeActionTypeEnum.CASCADE_EXECUTE;
        }
        return CascadeActionTypeEnum.NOT_ABORT;
    }

    /**
     * 构建影响提示文案
     * @param hasImpact
     * @return
     */
    private String buildUsageImpactDTOImpact(boolean hasImpact){
        if(hasImpact){
            return config.getHasImpactTip();
        }
        return "";
    }

    /**
     * 构建实体基础信息
     * @return
     */
    private UsageInfoDTO buildUsageInfoDTO(Map<String,String> extMap) {
        UsageInfoDTO usageInfoDTO = new UsageInfoDTO();
        usageInfoDTO.setSubjectType(LineageSubjectTypeEnum.CUSTOM_PRODUCT_DETAIL.getSubjectType());
        String bpServiceTypeId = extMap.get(Constants.BP_SERVICE_TYPE_ID);
        String bpServiceType = extMap.get(Constants.BP_SERVICE_TYPE);
        usageInfoDTO.setSubjectId(bpServiceTypeId);
        usageInfoDTO.setSubjectName(bpServiceType);
        usageInfoDTO.setCreatorMis(config.getCreatorMis());
        usageInfoDTO.setModifierMis(config.getModifierMis());
        usageInfoDTO.setCreatorId(config.getCreatorId());
        usageInfoDTO.setModifierId(config.getModifierId());
        return usageInfoDTO;
    }

    /**
     * 判断是否有影响
     * @return
     */
    private boolean isHasImpact(List<UsageImpactFieldDetailDTO> usageImpactFieldDetailDTOList) {
        if(CollectionUtils.isNotEmpty(usageImpactFieldDetailDTOList)){
            return true;
        }
        return false;
    }

    /**
     * 构建影响提示文案
     * @return
     */
    private String buildImpact(CascadeActionTypeEnum cascadeActionTypeEnum,Integer operateType) {
        String changeType = "";
        switch (operateType) {
            case 1:
                changeType = "新增";
                break;
            case 2:
                changeType = "修改";
                break;
            case 3:
                changeType = "删除";
                break;
        }

        switch (cascadeActionTypeEnum.getCode()) {
            case 1:
                return String.format(config.getAbortTip(),changeType);
            case 2:
                return String.format(config.getNotAbortTip(),changeType);
            case 3:
                return String.format(config.getCascadeExecuteTip(),changeType);
        }
        return "";
    }

    private List<UsageImpactFieldDetailDTO> buildUsageImpactFieldDetailDTO(DiffDTO diffDTO) {
        return diffDTO.getDiffList().stream()
                .map(diffDetailDTO -> {
                    CascadeActionTypeEnum cascadeActionTypeEnum = getCascadeActionTypeEnum(diffDetailDTO.getDepKey(), diffDetailDTO.getChangeType());
                    if (CascadeActionTypeEnum.CASCADE_EXECUTE.getCode() == cascadeActionTypeEnum.getCode()  ) {
                        return null;
                    }
                    UsageImpactFieldDetailDTO usageImpactFieldDetailDTO = new UsageImpactFieldDetailDTO();
                    usageImpactFieldDetailDTO.setSubjectFieldType(String.valueOf(diffDTO.getSubjectType()));
                    usageImpactFieldDetailDTO.setSubjectFieldKey(diffDetailDTO.getDepKey());
                    usageImpactFieldDetailDTO.setSubjectFieldName(diffDetailDTO.getDepName());
                    usageImpactFieldDetailDTO.setSubjectFieldOperateType(diffDetailDTO.getChangeType());
                    usageImpactFieldDetailDTO.setSubjectFieldNewData(diffDetailDTO.getNewData());
                    usageImpactFieldDetailDTO.setSubjectFieldOldData(diffDetailDTO.getOldData());
                    usageImpactFieldDetailDTO.setUsageCascadeAction(cascadeActionTypeEnum.getCode());
                    usageImpactFieldDetailDTO.setImpact(buildImpact(cascadeActionTypeEnum, diffDetailDTO.getChangeType()));

                    return usageImpactFieldDetailDTO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取联动类型
     * @param attrName
     * @param operateType
     * 1=阻断=红灯
     * 2=不阻断=黄灯
     * 3=联动变更=绿灯
     * @return
     */
    private CascadeActionTypeEnum getCascadeActionTypeEnum(String attrName,Integer operateType) {
        if(SubjectChangeOperateTypeEnum.ADD.getCode().equals(operateType) && !attrConfigList.contains(attrName)){
            return CascadeActionTypeEnum.NOT_ABORT;
        }
        if(SubjectChangeOperateTypeEnum.UPDATE.getCode().equals(operateType) && attrConfigList.contains(attrName)){
                return CascadeActionTypeEnum.NOT_ABORT;
        }
        if(SubjectChangeOperateTypeEnum.DELETE.getCode().equals(operateType) && attrConfigList.contains(attrName)){
            return CascadeActionTypeEnum.NOT_ABORT;
        }
        return  CascadeActionTypeEnum.CASCADE_EXECUTE;
    }
}
