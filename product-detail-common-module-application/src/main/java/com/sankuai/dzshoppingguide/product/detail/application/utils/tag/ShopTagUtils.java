package com.sankuai.dzshoppingguide.product.detail.application.utils.tag;

import com.meituan.mdp.boot.starter.pigeon.util.MdpEnvUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GlassProductTagRuleConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.LEInsuranceAgreementEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 门店标签工具类
 */
public class ShopTagUtils {
    //安心配镜tagId
    private static final Long SAFE_OPTOMETRY_TAG_ID = 21151L;
    private static final Long SAFE_OPTOMETRY_TAG_ID_TEST = 7405L;


    //正品保障tagId
    public static final Long GENUINE_GUARANTEE_TAG_ID = 15676L;

    public static final Long GENUINE_GUARANTEE_TAG_ID_TEST = 7406L;

    /**
     * 获取LE门店保险协议枚举
     * @param shopDisplayTagList 门店标签列表
     * @return LE门店保险协议枚举
     */
    public static LEInsuranceAgreementEnum getLEInsuranceAgreementEnum(List<DisplayTagDto> shopDisplayTagList) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return null;
        }
        for (DisplayTagDto displayTagDto : shopDisplayTagList) {
            for (LEInsuranceAgreementEnum agreementEnum : LEInsuranceAgreementEnum.values()) {
                if (agreementEnum.getTagId() == displayTagDto.getTagId()) {
                    return agreementEnum;
                }
            }
        }
        return null;
    }

    /**
     * 判断门店是否展示安心配镜标签
     * @param shopDisplayTagList 门店标签列表
     * @return true-展示，false-不展示
     */
    public static boolean isSafeOptometry(List<DisplayTagDto> shopDisplayTagList) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return false;
        }
        Long safeOptometryTagId = MdpEnvUtils.isTestEnv() ? SAFE_OPTOMETRY_TAG_ID_TEST : SAFE_OPTOMETRY_TAG_ID;
        return hasDisplayTagId(shopDisplayTagList, safeOptometryTagId);
    }

    /**
     * 判断门店是否展示正品保障标签
     * @param shopDisplayTagList 门店标签列表
     * @param productBaseInfo 商品基础信息
     * @return true-展示，false-不展示
     */
    public static boolean isGenuineGuarantee(List<DisplayTagDto> shopDisplayTagList, ProductBaseInfo productBaseInfo) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return false;
        }
        Long genuineTagId = MdpEnvUtils.isTestEnv() ? GENUINE_GUARANTEE_TAG_ID_TEST : GENUINE_GUARANTEE_TAG_ID;
        boolean hasGenuineTag = hasDisplayTagId(shopDisplayTagList, genuineTagId);
        return hasGenuineTag && hasAuthorizedShopTag(shopDisplayTagList, productBaseInfo);
    }

    /**
     * 判断门店是否展示安心补牙标签
     * @param shopDisplayTagList 门店标签列表
     * @param safeMedicalGuaranteeTagDTO 安心医标签信息
     * @return true-展示，false-不展示
     */
    public static boolean isSafeDenture(List<DisplayTagDto> shopDisplayTagList, ObjectGuaranteeTagDTO safeMedicalGuaranteeTagDTO) {
        Long safeDentureTag = LionConfigUtils.getMedicalZdcTagId("safeDenture");
        if (Objects.isNull(safeDentureTag) || safeDentureTag <= 0L) {
            return false;
        }
        return hasDisplayTagId(shopDisplayTagList, safeDentureTag) && hasSafeMedicalFillTag(safeMedicalGuaranteeTagDTO);
    }

    /**
     * 判断安心医标签是否包含安心补牙标签
     * @param safeMedicalGuaranteeTagDTO 安心医标签信息
     * @return true-包含，false-不包含
     */
    private static boolean hasSafeMedicalFillTag(ObjectGuaranteeTagDTO safeMedicalGuaranteeTagDTO) {
        if (Objects.isNull(safeMedicalGuaranteeTagDTO) || CollectionUtils.isEmpty(safeMedicalGuaranteeTagDTO.getTags())) {
            return false;
        }
        return safeMedicalGuaranteeTagDTO.getTags().stream()
                .anyMatch(tag -> Objects.equals(tag.getCode(), GuaranteeTagNameEnum.ANXIN_MEDICAL_FILL_GUARANTEE.getCode()));
    }

    private static boolean hasAuthorizedShopTag(List<DisplayTagDto> shopDisplayTagList, ProductBaseInfo productBaseInfo) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return false;
        }
        Set<Long> shopTagIds = getShopTagIds(shopDisplayTagList);
        if (CollectionUtils.isEmpty(shopTagIds)) {
            return false;
        }
        Set<Long> productTagIds = getProductTagIds(productBaseInfo.getTags());
        if (CollectionUtils.isEmpty(productTagIds)) {
            return false;
        }
        List<GlassProductTagRuleConfig> glassProductTagRuleConfigs = LionConfigUtils.getGlassProductTagRuleConfigs();
        if (CollectionUtils.isEmpty(glassProductTagRuleConfigs)) {
            return false;
        }
        return glassProductTagRuleConfigs.stream().anyMatch(config ->
                shopTagIds.contains(config.getShopTag()) &&
                        productTagIds.contains(config.getHitProductTag()) &&
                        !productTagIds.contains(config.getMissProductTag()));
    }


    private static Set<Long> getShopTagIds(List<DisplayTagDto> shopDisplayTagList) {
        if (CollectionUtils.isEmpty(shopDisplayTagList)) {
            return null;
        }
        return shopDisplayTagList.stream().map(DisplayTagDto::getTagId).collect(Collectors.toSet());
    }

    private static Set<Long> getProductTagIds(List<DealGroupTagDTO> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        return tags.stream().map(DealGroupTagDTO::getId).collect(Collectors.toSet());
    }

    private static boolean hasDisplayTagId(List<DisplayTagDto> shopDisplayTagList, Long tagId) {
        if (CollectionUtils.isEmpty(shopDisplayTagList) || Objects.isNull(tagId)) {
            return false;
        }
        return shopDisplayTagList.stream().anyMatch(tag -> tagId.equals(tag.getTagId()));
    }
}
