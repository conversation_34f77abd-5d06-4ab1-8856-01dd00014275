package com.sankuai.dzshoppingguide.product.detail.application.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/9 15:34
 */
@Component
public class ApplicationContextGetBeanHelper implements ApplicationContextAware {
    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextGetBeanHelper.context = applicationContext;
    }

    /**
     * 根据类型获取bean
     *
     * @param clazz bean类型
     * @param <T> bean类型
     * @return bean对象
     */
    public static <T> T getBean(Class<T> clazz) {
        return context.getBean(clazz);
    }
}