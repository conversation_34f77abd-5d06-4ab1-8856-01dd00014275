package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.DetailComponentKeyConstant;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.DetailType12ViewComponent;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025-05-30
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Service_Project_Item,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "服务项目详情",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class ServiceProjectItemComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "服务项目名称")
    private String itemName;
    @DigitalArchField(desc = "服务项目份数或价格")
    private String itemExtra;
    @DigitalArchField(desc = "服务项目描述")
    private String itemDesc;

    @Override
    protected ComponentVO doBuildVO(int recursionDepth) {
        BaseViewComponent viewComponent = DetailType12ViewComponent.builder()
                .content(itemName)
                .unit(itemExtra)
                .subContent(itemDesc)
                .build();
        return new BizComponentVO(Collections.singletonList(viewComponent));
    }

}