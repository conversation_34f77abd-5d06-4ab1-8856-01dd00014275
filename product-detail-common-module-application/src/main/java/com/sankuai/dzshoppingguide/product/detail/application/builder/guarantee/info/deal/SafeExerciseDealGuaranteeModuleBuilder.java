package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.LayerConfigTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FloatingLayerOpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.FeaturesLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/05/10
 * @desc 安心练
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
        }
)
public class SafeExerciseDealGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

        @Override
        public ProductDetailGuaranteeVO doBuild() {
                // 1. 保障条
                List<GuaranteeInstructionsContentVO> contents = buildSafeExerciseContents();
                // 2. 保障浮层
                FeaturesLayer floatingLayer = buildSafeExerciseFloatingLayer();
                return new ProductDetailGuaranteeVO("保障", contents, floatingLayer, FloatingLayerOpenTypeEnum.JUMP_TO_DETAIL);
        }

        private FeaturesLayer buildSafeExerciseFloatingLayer() {
                LayerConfig layerConfig = new LayerConfig();
                layerConfig.setJumpUrl(LionConfigUtils.getAnXinExerciseDetailPage(request.getClientTypeEnum().isMtClientType()));
                FeaturesLayer floatingLayer = new FeaturesLayer();
                // 复用安心学的type,前端硬编码 type == 14 的时候是直接跳转到落地页,否则会出现一个浮层
                layerConfig.setType(LayerConfigTypeEnum.TYPE_14.getType());
                floatingLayer.setLayerConfigs(Collections.singletonList(layerConfig));
                return floatingLayer;
        }

        private List<GuaranteeInstructionsContentVO> buildSafeExerciseContents() {
                GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
                content.setText(LionConfigUtils.getAnXinExerciseDisplayText());
                Icon icon = new Icon();
                icon.setIcon(LionConfigUtils.getAnXinExerciseIcon());
                icon.setIconHeight(12);
                icon.setIconWidth(36);
                content.setStyle(1);
                content.setType("#8E3C12");
                content.setPrefixIcon(icon);
                content.setFontColor("#8E3C12");
                return Collections.singletonList(content);
        }
}
