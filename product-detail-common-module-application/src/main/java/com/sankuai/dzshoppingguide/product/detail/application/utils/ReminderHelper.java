package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Sets;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ReminderHelper {

    public static final Set<Integer> MON_TO_THUR = Sets.newHashSet(1, 2, 3, 4);

    private static final Integer CYCLE_AVAILABLE_TYPE = 0;

    private static final Integer SPECIFIED_DURATION_AVAILABLE_TYPE = 1;

    /**
     * 获取购买须知概要
     *
     * @param dealCtx
     */
    public static String getReminderSummary(DealCtx dealCtx) {
        try {
            if (dealCtx == null) {
                return "购买须知详情";
            }
            Map<String, String> categoryId2ReminderSummaryMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                    "com.sankuai.dzu.tpbase.dztgdetailweb.categoryId2ReminderSummaryMap", String.class, new HashMap<>());
            String key = String.valueOf(dealCtx.getCategoryId());
            if (categoryId2ReminderSummaryMap.containsKey(key)) {
                return categoryId2ReminderSummaryMap.get(key);
            }

            Optional<DealGroupDTO> dealGroupDTOOptional = Optional.ofNullable(dealCtx).map(DealCtx::getDealGroupDTO);
            Optional<DealGroupRuleDTO> dealGroupRuleDTOOptional = dealGroupDTOOptional.map(DealGroupDTO::getRule);
            Optional<DealGroupUseRuleDTO> dealGroupUseRuleDTOOptional = dealGroupRuleDTOOptional.map(DealGroupRuleDTO::getUseRule);
            Optional<DisableDateDTO> disableDateDTOOptional = dealGroupUseRuleDTOOptional.map(DealGroupUseRuleDTO::getDisableDate);
            Optional<AvailableDateDTO> availableDateDTOOptional = dealGroupUseRuleDTOOptional.map(DealGroupUseRuleDTO::getAvailableDate);

            String partA = StringUtils.EMPTY;
            String partB = StringUtils.EMPTY;
            String partC;

            List<Integer> disableDays = disableDateDTOOptional.map(DisableDateDTO::getDisableDays).orElse(null);
            List<DateRangeDTO> dateRangeDTOS = disableDateDTOOptional.map(DisableDateDTO::getDisableDateRangeDTOS).orElse(null);
            if (CollectionUtils.isNotEmpty(disableDays)) {
                if (disableDays.stream().noneMatch(MON_TO_THUR::contains) && disableDays.contains(6) && disableDays.contains(7)) {
                    partA = disableDays.contains(5) ? "周一至周四" : "周一至周五";
                } else if (disableDays.stream().anyMatch(disableDay -> disableDay > 100)) {
                    partA = "部分时间";
                } else if (CollectionUtils.isNotEmpty(dateRangeDTOS)) {
                    partA = "部分时间";
                }
            }
            if (CollectionUtils.isEmpty(disableDays) && CollectionUtils.isEmpty(dateRangeDTOS)) {
                partA = "周一至周日";
            }

            List<Integer> useAvailableDateCategoryList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                    "com.sankuai.dzu.tpbase.dztgdetailweb.useAvailableDateCategoryList", Integer.class, new ArrayList<>());
            if (useAvailableDateCategoryList.contains(dealCtx.getCategoryId())) {
                partA = "";
                Integer availableType = availableDateDTOOptional.map(AvailableDateDTO::getAvailableType).orElse(null);
                if (Objects.equals(availableType, CYCLE_AVAILABLE_TYPE)) {
                    // 是循环日期可用
                    List<CycleAvailableDateDTO> cycleAvailableDateList = availableDateDTOOptional.map(AvailableDateDTO::getCycleAvailableDateList).orElse(null);
                    if (CollectionUtils.isNotEmpty(disableDays)) {
                        partA = "部分时间";
                    } else if (cycleAvailableDateList != null) {
                        List<Integer> weekdayList = cycleAvailableDateList.stream()
                                .flatMap(date -> (date.getAvailableDays() != null ? date.getAvailableDays().stream() : Stream.empty()))
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        if (weekdayList.containsAll(Arrays.asList(1, 2, 3, 4, 5, 6, 7))) {
                            partA = "周一至周日";
                        } else if (weekdayList.containsAll(Arrays.asList(1, 2, 3, 4, 5, 6))) {
                            partA = "周一至周六";
                        } else if (weekdayList.containsAll(Arrays.asList(1, 2, 3, 4, 5))) {
                            partA = "周一至周五";
                        } else if (weekdayList.containsAll(Arrays.asList(1, 2, 3, 4))) {
                            partA = "周一至周四";
                        } else {
                            partA = "部分时间";
                        }
                    }
                } else if (Objects.equals(availableType, SPECIFIED_DURATION_AVAILABLE_TYPE)) {
                    partA = "部分时间";
                }
            }
            List<String> availableTimes = null;
//            try {
//                availableTimes = DealAttrHelper.getAttributeValues(dealCtx.getProductAttr(), DealAttrKeys.AVAILABLE_TIME);
//            } catch (Exception e) {
//                log.error("getReminderSummary error, ", e);
//            }
//            if (CollectionUtils.isNotEmpty(availableTimes) &&
//                    !"00:00-23:59".equals(availableTimes.get(0)) && !"00:01-23:59".equals(availableTimes.get(0))) {
//                partB = "部分时段";
//            }
            //partC = DealAttrHelper.needReservation(dealCtx != null ? dealCtx.getAttrs() : null) ? "需预约" : "免预约";
            if (StringUtils.isNotEmpty(partA) || StringUtils.isNotEmpty(partB)) {
                return partA + partB + "可用";
            }
            return "购买须知详情";
        } catch (Exception e) {
            log.error("getReminderSummary error, ", e);
        }
        return "购买须知详情";
    }
}
