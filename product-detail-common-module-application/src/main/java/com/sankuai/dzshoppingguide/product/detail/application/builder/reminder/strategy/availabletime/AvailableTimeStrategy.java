package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FetcherResultEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;

import java.util.Map;

public interface AvailableTimeStrategy {
    String getAvailableTime(FetcherResultDTO fetcherResults);

    AvailableTimeStrategyEnum getStrategyType();
}