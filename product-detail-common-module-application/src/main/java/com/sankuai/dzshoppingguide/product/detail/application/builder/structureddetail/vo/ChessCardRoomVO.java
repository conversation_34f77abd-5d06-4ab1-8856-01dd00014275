package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/5 21:53
 * @Description: 二级类目: 棋牌室
 */
@Data
@Builder
@TypeDoc(description = "棋牌室结构化详情")
// TODO
@MobileDo(id = 0x1600)
public class ChessCardRoomVO implements Serializable {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "内容")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "副内容")
    @MobileDo.MobileField(key = 0x667e)
    private String subContent;

    @FieldDoc(description = "对应前端展示类型")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;
}
