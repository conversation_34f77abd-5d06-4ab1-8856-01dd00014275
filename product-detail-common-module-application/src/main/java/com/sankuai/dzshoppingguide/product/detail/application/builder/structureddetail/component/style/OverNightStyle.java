package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule.ProductReadjustPriceRule;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimeUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.standardPriceRule.StandardPriceRuleItemDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/6/9 21:22
 */
@Component
@Slf4j
public class OverNightStyle implements Style<DealDetailStructuredDetailVO> {

    /**
     * 过夜规则
     */
    private static final String OVER_NIGHT_RULES = "overnightRules";
    private static final String OVER_NIGHT_SERVICE_TYPE = "OvernightServices";
    private static final String OVER_NIGHT_SERVICE_TYPE_PAY = "可付费过夜";
    private static final String OVER_NIGHT_MODULE_NAME = "过夜服务";
    private static final String OVER_NIGHT_MODULE_ICON = "https://p0.meituan.net/ingee/39345524d0f1ead891dccaefdef798b86694.png";

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {

        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        long skuId = Optional.ofNullable(context).map(DealDetailBuildContext::getSkuDefaultSelect).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        Map<Long, List<ReadjustPriceRuleDTO>> resultMap = Optional.ofNullable(context).map(DealDetailBuildContext::getProductReadjustPriceRule).map(ProductReadjustPriceRule::getReadjustPriceRuleMap).orElse(Maps.newHashMap());

        if (productAttr == null || skuId <= 0 || MapUtils.isEmpty(resultMap)) {
            return Collections.emptyList();
        }

        String overNightServices = productAttr.getSkuAttrFirstValue(OVER_NIGHT_SERVICE_TYPE);
        if (StringUtils.isEmpty(overNightServices) || !overNightServices.contains("免费")) {
            return Collections.emptyList();
        }

        List<ReadjustPriceRuleDTO> readjustPriceRuleDTOS = resultMap.getOrDefault(skuId, null);
        if (CollectionUtils.isEmpty(readjustPriceRuleDTOS)) {
            return Collections.emptyList();
        }

        StandardPriceRuleItemDTO standardPriceRuleItemDTO = readjustPriceRuleDTOS.stream().filter(Objects::nonNull).findFirst().map(ReadjustPriceRuleDTO::getStandardPriceRule).map(StandardPriceRuleDTO::getStandardPriceRuleItems).filter(Objects::nonNull).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).findFirst().orElse(null);

        if (standardPriceRuleItemDTO == null || MapUtils.isEmpty(standardPriceRuleItemDTO.getAttributeValue())) {
            return Collections.emptyList();
        }


        Map<String, String> attributeValue = standardPriceRuleItemDTO.getAttributeValue();
        String stayOverEndTime = attributeValue.getOrDefault("stayOverEndTime", StringUtils.EMPTY);
        String freeBreakfast = attributeValue.getOrDefault("freeBreakfast", StringUtils.EMPTY);
        String stayOverStartTime = attributeValue.getOrDefault("stayOverStartTime", StringUtils.EMPTY);
        String serviceTitle = attributeValue.getOrDefault("serviceTitle", StringUtils.EMPTY);
        String stayOverDesc = attributeValue.getOrDefault("stayOverDesc", StringUtils.EMPTY);
        OverNightStyle.OvernightRule overnightRule = new OvernightRule();
        overnightRule.setStayOverDesc(stayOverDesc);
        overnightRule.setFreeBreakfast(freeBreakfast);
        overnightRule.setStayOverEndTime(stayOverEndTime);
        overnightRule.setStayOverStartTime(stayOverStartTime);
        overnightRule.setServiceTitle(serviceTitle);
        return buildOvernightModule(overnightRule);
    }

    private List<DealDetailStructuredDetailVO> buildOvernightModule(OverNightStyle.OvernightRule overnightRule) {
        if (Objects.isNull(overnightRule)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        DealDetailStructuredDetailVO overnightModuleTitle = buildOvernightModuleTitle();
        result.add(overnightModuleTitle);
        DealDetailStructuredDetailVO item = buildOvernightModuleItem(overnightRule);
        result.add(item);
        return result;

    }

    private DealDetailStructuredDetailVO buildOvernightModuleItem(OverNightStyle.OvernightRule overnightRule) {
        return DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                .title(buildServiceTime(overnightRule))
                .content(overnightRule.getStayOverDesc())
                .icon(OVER_NIGHT_MODULE_ICON)
                .build();
    }

    private DealDetailStructuredDetailVO buildOvernightModuleTitle() {
        return DealDetailStructuredDetailVO.builder()
                .title(OVER_NIGHT_MODULE_NAME)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .detail("免费")
                .build();
    }

    private String buildServiceTime(OverNightStyle.OvernightRule overnightRule) {
        String serviceTime = formatServiceTime(overnightRule);
        if (StringUtils.isBlank(serviceTime)) {
            return null;
        }
        String serviceTimeAndBreakfast = appendBreakfastInfo(serviceTime, overnightRule);
        return "起止时间：" + serviceTimeAndBreakfast;
    }

    private String formatServiceTime(OverNightStyle.OvernightRule overnightRule) {
        try {
            String startTime = TimeUtils.formatOvernightTime(overnightRule.getStayOverStartTime());
            String endTime = TimeUtils.formatOvernightTime(overnightRule.getStayOverEndTime());
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return StringUtils.EMPTY;
            }
            return String.format("%s-%s",
                    startTime,
                    endTime);
        } catch (Exception e) {
            log.error("formatServiceTime error, overnightRule={}", overnightRule, e);
            return StringUtils.EMPTY;
        }
    }

    private String appendBreakfastInfo(String serviceTime, OverNightStyle.OvernightRule overnightRule) {
        return isFreeBreakfast(overnightRule) ? serviceTime + " 含早餐" : serviceTime;
    }

    private boolean isFreeBreakfast(OverNightStyle.OvernightRule overnightRule) {
        return Objects.equals(overnightRule.getFreeBreakfast(), "含早餐");
    }

    @Data
    private static class OvernightRule {
        /**
         * 过夜服务标题
         */
        private String serviceTitle;

        /**
         * 过夜规则：23点后可过夜·需预约
         */
        private String stayOverDesc;
        /**
         *
         */
        private String freeBreakfast;
        /**
         * 过夜开始时间：23:00
         */
        private String stayOverStartTime;
        /**
         * 过夜结束时间：07:00
         */
        private String stayOverEndTime;
    }
}
