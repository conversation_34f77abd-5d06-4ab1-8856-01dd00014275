package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note;

import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.*;

import java.util.List;


/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductPurchaseNote extends FetcherReturnValueDTO {

    /**
     * 购买须知
     */
    private final PurchaseNoteDTO purchaseNote;

    private final List<PurchaseNoteDTO> purchaseNotes;;

    public ProductPurchaseNote(PurchaseNoteDTO purchaseNote, List<PurchaseNoteDTO> purchaseNotes) {
        this.purchaseNote = purchaseNote;
        this.purchaseNotes = purchaseNotes;
    }

}
