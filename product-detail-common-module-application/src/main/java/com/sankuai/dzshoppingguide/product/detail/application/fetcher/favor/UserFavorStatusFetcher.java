package com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor;

import com.dianping.userremote.service.collection.FavorService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.coll.idl.CollectionService;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.user.collection.client.CollTypeEnum;
import com.sankuai.user.collection.client.UserCollectionClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.InitializingBean;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class},
        timeout = 500
)
@Slf4j
public class UserFavorStatusFetcher extends NormalFetcherContext<UserFavorStatusReturnValue> implements InitializingBean {
    @RpcClient(url = "http://service.dianping.com/userService/favorService_1.0.0")
    private FavorService favorService;

    @RpcClient(remoteAppkey = "com.sankuai.wpt.user.collection")
    private CollectionService.Iface iface;

    private final UserCollectionClient userCollectionClient = new UserCollectionClient();

    protected CompletableFuture<UserFavorStatusReturnValue> doFetch() {
        long productId = request.getProductId();
        long userId = request.getUserId();
        if (PlatformEnum.DP.equals(request.getPlatformEnum())) {
            return AthenaInf.getRpcCompletableFuture(favorService.findFavorStatus(Lists.newArrayList(String.valueOf(productId)), Constants.DP_FAVOR_BIZ_TYPE, userId))
                    .thenApply(r -> {
                        UserFavorStatusReturnValue userFavorStatusReturnValue = new UserFavorStatusReturnValue();
                        if (MapUtils.isEmpty(r)) {
                            return userFavorStatusReturnValue;
                        }
                        Boolean favored = Optional.ofNullable(r.entrySet().iterator().next().getValue()).orElse(false);
                        userFavorStatusReturnValue.setFavored(favored);
                        return userFavorStatusReturnValue;
                    })
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "DpUserFavorStatusFetcher")
                                .putTag("method", "findFavorStatus")
                                .message(String.format("findFavorStatus error, request : %s, biztype:%d, userid:%d", JsonCodec.encode(request), Constants.DP_FAVOR_BIZ_TYPE, userId)));
                        return null;
                    });
        } else {
            try {
                return AthenaInf.getRpcCompletableFuture(userCollectionClient.getCollectedFromGivenSetsV2(userId, CollTypeEnum.DEAL_COLL, Lists.newArrayList(productId), Constants.MT_FAVOR_SOURCE_TYPE))
                        .thenApply(r -> {
                            UserFavorStatusReturnValue userFavorStatusReturnValue = new UserFavorStatusReturnValue();
                            userFavorStatusReturnValue.setFavored(CollectionUtils.isNotEmpty(r));
                            return userFavorStatusReturnValue;
                        }).exceptionally(e -> {
                            log.error(XMDLogFormat.build()
                                    .putTag("scene", "userCollectionClient")
                                    .putTag("method", "getCollectedFromGivenSetsV2")
                                    .message(String.format("getCollectedFromGivenSetsV2 error, request : %s, sourceType:%d, userid:%d", JsonCodec.encode(request), Constants.MT_FAVOR_SOURCE_TYPE, userId)));
                            return null;
                        });
            } catch (TException e) {
                log.error("userCollectionClient error");
            }
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        userCollectionClient.setIface(iface);
    }
}