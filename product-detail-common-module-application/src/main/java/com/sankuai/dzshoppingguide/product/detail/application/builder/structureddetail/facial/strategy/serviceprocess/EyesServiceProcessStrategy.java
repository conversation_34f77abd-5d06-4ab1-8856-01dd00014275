package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.InspectionInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.dto.EyesProcessDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.ServiceProcessUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/17 11:27
 */
@Component
@Slf4j
public class EyesServiceProcessStrategy extends AbstractSubModuleBuildStrategy {

    @Autowired
    private InspectionInfoBuilder inspectionInfoBuilder;

    @Override
    public String getSubModuleName() {
        return SubModuleKey.SERVICE_PROCESS;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        return categoryId == SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId();
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = doBuild(context);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        // 增加分隔符
        DealDetailStructuredDetailVO separator = DealDetailStructuredUtils.buildLimiter();
        result.add(separator);
        return result;
    }

    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return Collections.emptyList();
        }
        // 服务流程信息
        List<String> servicesProcessStr = productAttr.getSkuAttrValue("serviceProcess");
        List<EyesProcessDTO> processDTOS = ServiceProcessUtils.parseProcess(servicesProcessStr);
        if (CollectionUtils.isEmpty(processDTOS)) {
            return Collections.emptyList();
        }

        List<DealDetailStructuredDetailVO> result = processDTOS.stream().filter(Objects::nonNull).map(process -> {
            String processName = process.getOphthalmic_services_process();
            String duration = process.getDuration();
            if (StringUtils.isBlank(processName)) {
                return null;
            }
            switch (processName) {
                case "视力检查":
                    return buildVisualCheckContent(context, productAttr, duration);
                case "眼科检查":
                    return buildEyeCheckContent(context, productAttr, duration);
                case "整体健康评估":
                    return buildHealthAssessmentContent(context, productAttr, duration);
                case "干眼理疗":
                    return buildEyeTherapyContent(context, productAttr, duration);
                default:
                    return buildNormalContent(processName, duration);
            }
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());


        // 标题模块
        int totalDuration = processDTOS.stream().map(EyesProcessDTO::getDuration).map(ServiceProcessUtils::parseDuration).reduce(Integer::sum).orElse(0);
        if (CollectionUtils.isNotEmpty(result)) {
            DealDetailStructuredUtils.buildServiceProcessTitle(totalDuration).ifPresent(item -> result.add(0,item));
        }

        // 最后统一处理填充order
        ServiceProcessUtils.setOrder(result);

        // 底部的注释
        DealDetailStructuredUtils.buildAnnotation().ifPresent(result::add);

        return result;
    }
    
    private String buildPopupData(DealDetailBuildContext context) {
        String popupDataTitle = "服务流程详情";
        List<DealDetailStructuredDetailVO> popupData = inspectionInfoBuilder.getEyeInspectionPopUp(context);
        if (CollectionUtils.isEmpty(popupData)) {
            return null;
        }

        DealDetailStructuredDetailVO result = DealDetailStructuredDetailVO.builder().title(popupDataTitle).type(ViewComponentTypeEnum.POPUP_STRUCTURED.getType()).content(JSON.toJSONString(popupData)).build();

        return JSON.toJSONString(result);
    }

    private List<DealDetailStructuredDetailVO> buildNormalContent(String processName,String duration) {
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredUtils.buildProcessInfo(processName, duration).ifPresent(result::add);
        return result;
    }

    /**
     * 视力检查
     */
    private List<DealDetailStructuredDetailVO> buildVisualCheckContent(DealDetailBuildContext context,ProductAttr productAttr,String duration) {
        if (productAttr == null) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredUtils.buildProcessInfo("视力检查", duration).ifPresent(result::add);
        List<String> visualCheckStr = productAttr.getSkuAttrValue("VisualCheck");
        String content = buildVisualCheckSubContent(context.getRequest(),visualCheckStr);
        String popupData = buildPopupData(context);
        DealDetailStructuredUtils.buildSubProcessInfo(content, popupData).ifPresent(result::add);
        return result;
    }

    private String buildVisualCheckSubContent(ProductDetailPageRequest request,List<String> visualCheckStr) {
        if (CollectionUtils.isEmpty(visualCheckStr)) {
            return null;
        }

        try {
            Set<String> processList = visualCheckStr.stream().filter(StringUtils::isNotBlank).map(JSON::parseObject).map(item -> item.getString("VisionCheckProcess")).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(processList)) {
                return null;
            }

            Set<String> basicChecks = Sets.newHashSet("普通视力检查", "电脑验光", "综合验光");

            if (!processList.containsAll(basicChecks)) {
                return String.join("、", processList);
            }

            boolean isBasicItems = basicChecks.containsAll(processList);


            if (isBasicItems) {
                return "包含基础视力检查项目";
            }

            processList.removeAll(basicChecks);

            return "包含基础视力检查项目、" + String.join("、", processList) + "等";
        } catch (Exception e) {
            log.error("Parse visual check content error, request: {}", JsonCodec.encode(request), e);
            return null;
        }
    }

    /**
     * 眼科检查
     */
    private List<DealDetailStructuredDetailVO> buildEyeCheckContent(DealDetailBuildContext context,ProductAttr productAttr,String duration){
        if (productAttr == null) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredUtils.buildProcessInfo("眼科检查", duration).ifPresent(result::add);
        List<String> skuAttrValues = productAttr.getSkuAttrValue("EyeCheck");
        String content = buildEyeCheckSubContent(context.getRequest(),skuAttrValues);
        String popupData = buildPopupData(context);
        DealDetailStructuredUtils.buildSubProcessInfo(content, popupData).ifPresent(result::add);
        return result;
    }

    private String buildEyeCheckSubContent(ProductDetailPageRequest request,List<String> skuAttrValues) {
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return null;
        }
        try {
            Set<String> processList = skuAttrValues.stream().filter(StringUtils::isNotBlank).map(JSON::parseObject).map(item -> item.getString("EyeCheckProcess")).collect(Collectors.toSet());
            
            if (CollectionUtils.isEmpty(processList)) {
                return null;
            }

            Set<String> basicChecks = Sets.newHashSet("眼压测量", "裂隙灯显微镜检查");

            // 处理不包含基础医学项目的情况
            if (!processList.containsAll(basicChecks)) {
                return String.format("包含%s等", processList.stream().limit(3).collect(Collectors.joining("、")));
            }

            // 检查是否只包含基础项目
            boolean isBasicItems = basicChecks.containsAll(processList);
            if (isBasicItems) {
                return "包含基础医学验光项目";
            }

            // 移除基础项目，获取额外项目
            processList.removeAll(basicChecks);
            
            // 最多展示3个额外项目
            List<String> extraItems = processList.stream()
                .limit(3)
                .collect(Collectors.toList());

            return "包含基础医学验光项目、" + String.join("、", extraItems) + "等";
        } catch (Exception e) {
            log.error("Parse eye check content error, request: {}", JsonCodec.encode(request), e);
            return null;
        }
    }

    /**
     * 整体健康评估
     */
    private List<DealDetailStructuredDetailVO> buildHealthAssessmentContent(DealDetailBuildContext context,ProductAttr productAttr,String duration) {
        if (productAttr == null) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredUtils.buildProcessInfo("整体健康评估", duration).ifPresent(result::add);
        List<String> healthAssessmentStr = productAttr.getSkuAttrValue("HealthAssessment");
        String content = buildHealthAssessmentSubContent(context.getRequest(),healthAssessmentStr);
        String popupData = buildPopupData(context);
        DealDetailStructuredUtils.buildSubProcessInfo(content, popupData).ifPresent(result::add);
        return result;
    }

    private String buildHealthAssessmentSubContent(ProductDetailPageRequest request,List<String> healthAssessmentStr) {
        if (CollectionUtils.isEmpty(healthAssessmentStr)) {
            return null;
        }
        try {
            Set<String> processList = healthAssessmentStr.stream().filter(StringUtils::isNotBlank).map(JSON::parseObject).map(item -> item.getString("HealthAssessmentProcess")).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(processList)) {
                return null;
            }
            // 最多展示3个项目
            List<String> displayItems = processList.stream()
                .limit(3)
                .collect(Collectors.toList());

            return "包含" + String.join("、", displayItems) + "等";
        } catch (Exception e) {
            log.error("Parse health assessment content error, request: {}", request, e);
            return null;
        }
    }

    /**
     * 干眼理疗
     */
    private List<DealDetailStructuredDetailVO> buildEyeTherapyContent(DealDetailBuildContext context,ProductAttr productAttr,String duration) {
        if (productAttr == null) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredUtils.buildProcessInfo("干眼理疗", duration).ifPresent(result::add);
        List<String> eyeTherapy = productAttr.getSkuAttrValue("EyeTherapy");
        String content = buildEyeTherapySubContent(context.getRequest(),eyeTherapy);
        String popupData = buildPopupData(context);
        DealDetailStructuredUtils.buildSubProcessInfo(content, popupData).ifPresent(result::add);
        return result;
    }

    private String buildEyeTherapySubContent(ProductDetailPageRequest request,List<String> eyeTherapy) {
        if (CollectionUtils.isEmpty(eyeTherapy)) {
            return null;
        }
        try {
            Set<String> processList = eyeTherapy.stream().filter(StringUtils::isNotBlank).map(JSON::parseObject).map(item -> item.getString("EyeTherapyProcess")).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(processList)) {
                return null;
            }

            // 最多展示3个项目
            List<String> displayItems = processList.stream()
                .limit(3)
                .collect(Collectors.toList());

            return "包含" + String.join("、", displayItems) + "等";
        } catch (Exception e) {
            log.error("Parse eye therapy content error, request: {}", JsonCodec.encode(request), e);
            return null;
        }
    }
}
