package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimeUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 在线教育类团购
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/10 20:23
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {ProductBaseInfoFetcher.class}
)
@Slf4j
public class EduOnlineReminderInfoBuilder extends AbstractReminderInfoBuilder {


    @Override
    public ProductDetailReminderVO preBuild() {
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if  (baseInfo == null) {
            return null;
        }
        ProductDetailReminderVO bookingDetailsReminderInfoVO = new ProductDetailReminderVO();
        GuaranteeInstructionsBarLayerVO reminderInfoLayerVO = new GuaranteeInstructionsBarLayerVO();
        reminderInfoLayerVO.setType(2);
        reminderInfoLayerVO.setModulekey(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        bookingDetailsReminderInfoVO.setLayer(reminderInfoLayerVO);
        List<GuaranteeInstructionsContentVO> contents = Lists.newArrayList();
        String effectiveDateForOnlineCourse = getEffectiveDateForOnlineCourse(baseInfo);
        if (StringUtils.isEmpty(effectiveDateForOnlineCourse)) {
            return null;
        }
        ReminderInfoUtils.buildReminderInfo(effectiveDateForOnlineCourse).ifPresent(contents::add);
        bookingDetailsReminderInfoVO.setContents(contents);
        return bookingDetailsReminderInfoVO;
    }


    /**
     * 在线课的有效期
     * @param baseInfo
     * @return
     */
    public String getEffectiveDateForOnlineCourse(ProductBaseInfo baseInfo) {
        if (baseInfo == null) {
            return StringUtils.EMPTY;
        }
        ReceiptEffectiveDateDTO receiptEffectiveDateDTO = getReceiptEffectiveDateDTO(baseInfo);
        if (receiptEffectiveDateDTO == null){
            return StringUtils.EMPTY;
        }
        if (receiptEffectiveDateDTO.getReceiptDateType() != null) {
            if (receiptEffectiveDateDTO.getReceiptDateType() == 0
                    && receiptEffectiveDateDTO.getReceiptBeginDate() != null && receiptEffectiveDateDTO.getReceiptEndDate() != null) {
                String beginDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptBeginDate()));
                String endDate = TimeUtils.convertDate2DayString(DealGroupUtils.convertString2Date(receiptEffectiveDateDTO.getReceiptEndDate()));
                return (beginDate == null && endDate == null) ? "" : String.format("有效期至%s", endDate);
            }
            if (receiptEffectiveDateDTO.getReceiptDateType() == 1
                    && receiptEffectiveDateDTO.getReceiptValidDays() != null) {
                return "开课后" + receiptEffectiveDateDTO.getReceiptValidDays() + "天内有效";
            }
        }
        return StringUtils.EMPTY;
    }

    private ReceiptEffectiveDateDTO getReceiptEffectiveDateDTO(ProductBaseInfo baseInfo) {
        return Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getRule)
                .map(DealGroupRuleDTO::getUseRule)
                .map(DealGroupUseRuleDTO::getReceiptEffectiveDate).orElse(null);
    }
}
