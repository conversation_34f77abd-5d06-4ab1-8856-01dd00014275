package com.sankuai.dzshoppingguide.product.detail.application.fetcher.review;

import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tuangou.dztg.bjwrapper.api.UserWrapperService;
import com.dianping.tuangou.dztg.bjwrapper.api.dto.MtUserDto;
import com.dianping.tuangou.dztg.bjwrapper.api.enums.UserFieldsTypeEnum;
import com.dianping.ugc.pic.remote.dto.MtReviewPicInfo;
import com.dianping.ugc.pic.remote.dto.VideoData;
import com.dianping.ugc.pic.remote.service.MtReviewPicService;
import com.dianping.ugc.pic.remote.service.VideoService;
import com.dianping.ugc.review.remote.dto.*;
import com.dianping.ugc.review.remote.enums.ReviewPlatFormEnum;
import com.dianping.ugc.review.remote.enums.ReviewSortType;
import com.dianping.ugc.review.remote.mt.MTReviewQueryService;

import com.dp.arts.client.SearchService;
import com.dp.arts.client.request.Request;
import com.dp.arts.client.request.SortItem;
import com.dp.arts.client.request.StatItem;
import com.dp.arts.client.request.TermQuery;
import com.dp.arts.client.response.Record;
import com.dp.arts.client.response.Response;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.enums.PicVideoStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.enums.ThreadPoolNameEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopReviewHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.thread.ThreadPoolUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @Author: litengfei04
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {
                CommonModuleStarter.class,
                ShopIdMapperFetcher.class,
        },
        timeout = 500
)
@Slf4j
public class MtDealReviewFetcher extends NormalFetcherContext<ProductReviewReturnValue> {


    @MdpPigeonClient(url = "search.arts.biz.shopreview", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private SearchService shopReviewSearchServiceFuture;

    @MdpPigeonClient(url = "UGCReviewService.MTReviewQueryService", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private MTReviewQueryService mtReviewQueryServiceFuture;

    @MdpPigeonClient(url = "UGCPicService.MtReviewPicService", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private MtReviewPicService mtReviewPicServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/tuangou/bjwrapper/userWrapperService_1.0.0", callType = CallMethod.SYNC, timeout = 1000, testTimeout = 50000)
    private UserWrapperService userWrapperService;

    @MdpPigeonClient(url = "UGCPicService.VideoService", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private VideoService mtReviewVideoServiceFuture;

    @Resource
    private MapperCacheWrapper mapperCacheWrapper;

    private static final String MT_REVIEW_TAG_URL = "imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s&selecttagname=%s&tagtype=%d";
    private static final String MT_REVIEW_LIST_URL = "imeituan://www.meituan.com/reviewlist?refertype=1&referid=%s&filterid=800";

    @Override
    protected CompletableFuture<ProductReviewReturnValue> doFetch() {
        // 点评侧直接返回
        if (this.request.getClientTypeEnum().isDpClientType()) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(
                () -> getMtReviewDetailList(buildShopReviewCtx(), 2),
                ThreadPoolUtils.getExecutor(ThreadPoolNameEnum.REVIEW_EXECUTOR)
        );
    }

    private ShopReviewCtx buildShopReviewCtx() {
        ShopReviewCtx shopReviewCtx = new ShopReviewCtx();
        shopReviewCtx.setDpId(getDpDealGroupId());
        shopReviewCtx.setMtId(getMtDealGroupId());
        shopReviewCtx.setDpLongShopId(getDpShopId());
        shopReviewCtx.setMtLongShopId(getMtShopId());
        shopReviewCtx.setUnionId(request.getShepherdGatewayParam().getUnionid());
        shopReviewCtx.setMtUserId(request.getMtUserId());
        shopReviewCtx.setClientTypeEnum(request.getClientTypeEnum());
        return shopReviewCtx;
    }

    private long getMtShopId() {
        return request.getClientTypeEnum().isMtClientType() ? request.getPoiId() : mapperCacheWrapper.fetchMtShopId(request.getPoiId());
    }

    private long getDpShopId() {
        return request.getClientTypeEnum().isMtClientType() ? mapperCacheWrapper.fetchDpShopId(request.getPoiId()) : request.getPoiId();
    }

    private long getMtDealGroupId() {
        return request.getClientTypeEnum().isMtClientType() ? request.getProductId() : mapperCacheWrapper.getMtDealGroupId(request.getProductId());
    }

    private long getDpDealGroupId() {
        return request.getClientTypeEnum().isMtClientType() ? mapperCacheWrapper.getDpDealGroupId(request.getProductId()) : request.getProductId();
    }

    private ProductReviewReturnValue getMtReviewDetailList(ShopReviewCtx shopReviewCtx, int displayReviewCount) {
        ProductReviewReturnValue productReviewReturnValue = new ProductReviewReturnValue();
        Future mtShopReviewTagFuture = getMTAllShopReviewTagFuture(shopReviewCtx);
        Future mtReviewCountFuture = preReviewCountByDealIds((int) shopReviewCtx.getMtId());
        Future mtQueryResultFuture = getMtReviewByDeal(shopReviewCtx, displayReviewCount);

        MTQueryResult mtQueryResult = getFutureResult(mtQueryResultFuture);
        ReviewCount mtReviewCount = getReviewCount((int) shopReviewCtx.getMtId(), mtReviewCountFuture);

        List<ProductReviewDetail> reviewDetailDOList = buildMtReviewDOList(shopReviewCtx, mtQueryResult); //美团数据
        productReviewReturnValue.setTotalCount(mtReviewCount == null ? 0 : mtReviewCount.getAll());

        List<ProductReviewTagFilter> reviewTagDOList = createReviewTag(mtShopReviewTagFuture, shopReviewCtx);
        productReviewReturnValue.setTagList(reviewTagDOList);

        productReviewReturnValue.setReviewList(reviewDetailDOList);
        productReviewReturnValue.setMoreUrl(getReviewListUrl());
        return productReviewReturnValue;
    }

    private String getReviewListUrl() {
        return String.format(MT_REVIEW_LIST_URL, request.getProductId());
    }


    public Future getMTAllShopReviewTagFuture(ShopReviewCtx shopReviewCtx) {
        try {
            Request request = new Request(Request.Platform.WWW, "reviewsummary");
            request.addQuery(new TermQuery("referid", String.valueOf(shopReviewCtx.getMtId())));
            request.addQuery(new TermQuery("source", "2"));
            request.addQuery(new TermQuery("power", "2"));
            request.addQuery(new TermQuery("refertype", "2"));
            request.addStatItem(new StatItem("reviewtagsentiment"));
            request.addInfo("userid", String.valueOf(shopReviewCtx.getMtUserId()));//已确认判断平台后再使用
            request.addInfo("mtid", shopReviewCtx.getUnionId());
            request.addSortItem(new SortItem("abstract", Request.SortOrder.DESC));

            shopReviewSearchServiceFuture.search(request);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getGoodShopReviews error", e);
        }
        return null;
    }

    public Future preReviewCountByDealIds(int mtDealId) {
        if (mtDealId <= 0) {
            return null;
        }
        try {
            mtReviewQueryServiceFuture.getReviewCountByDealIds(Lists.newArrayList(mtDealId), ReviewPlatFormEnum.MT.value);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("preReviewCountByDealIds error", e);
        }
        return null;
    }

    public Future getMtReviewByDeal(ShopReviewCtx shopReviewCtx, int displayReviewCount) {
        try {
            mtReviewQueryServiceFuture.getReviewByDeal((int) shopReviewCtx.getMtId(), buildFilterParam(), 0, displayReviewCount, null);
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getMtReviewByDeal error", e);
        }
        return null;
    }

    private static FilterParam buildFilterParam() {
        FilterParam filterParam = new FilterParam();
        filterParam.setDealShopIds(new ArrayList<>());
        filterParam.setHasPic(false);
        filterParam.setStarRange(Lists.newArrayList(10, 20, 30, 40, 50));
        filterParam.setSortType(ReviewSortType.WEIGHT_DESC.value);
        return filterParam;
    }

//    public ReviewStarDistributionDTO getReviewStar(Future future) {
//        List<ReviewStarDistributionDTO> dtoList = getFutureResult(future);
//        return CollectionUtils.isEmpty(dtoList) ? null : dtoList.get(0);
//    }


    private List<ProductReviewDetail> buildMtReviewDOList(ShopReviewCtx shopReviewCtx, MTQueryResult mtQueryResult) {
        if (mtQueryResult == null || CollectionUtils.isEmpty(mtQueryResult.getMtReviewDataList())) {
            return Collections.emptyList();
        }
        Set<Long> userIds = Sets.newHashSet();
        Set<Long> picIds = Sets.newHashSet();
        Set<Long> videoIds = Sets.newHashSet();
        for (MTReviewData mtReviewData : mtQueryResult.getMtReviewDataList()) {
            if (mtReviewData.getUserId() > 0) {
                userIds.add(mtReviewData.getUserId());
            }
            if (CollectionUtils.isNotEmpty(mtReviewData.getReviewPics())) {
                for (ReviewPic reviewPic : mtReviewData.getReviewPics()) {
                    // 过滤掉被删除或屏蔽的照片
                    if (filterPicStatusAndOwner(shopReviewCtx, mtReviewData.getUserId(), reviewPic.getStatus())) {
                        continue;
                    }
                    picIds.add(reviewPic.getPicId());
                }
            }

            if (CollectionUtils.isNotEmpty(mtReviewData.getReviewVideoList())) {
                for (ReviewVideo reviewVideo : mtReviewData.getReviewVideoList()) {
                    // 过滤掉被屏蔽或删除的 视频
                    if (filterPicStatusAndOwner(shopReviewCtx, mtReviewData.getUserId(), reviewVideo.getStatus())) {
                        continue;
                    }
                    videoIds.add(reviewVideo.getVideoId());
                }
            }

        }
        List<ProductReviewDetail> reviewDetailDOList = Lists.newArrayList();
        Future mtReviewPicFuture = getMtReviewPicFuture(Lists.newArrayList(picIds));
        Future mtReviewVideoFuture = getMtReviewVideoFuture(Lists.newArrayList(videoIds));

        Map<Long, MtUserDto> userModelMap = getUserModelMap(userIds);
        Map<Long, VideoData> mtReviewVideoInfoMap = getMtReviewVideoInfoMap(mtReviewVideoFuture);
        Map<Long, MtReviewPicInfo> mtReviewPicInfoMap = getMtReviewPicInfoMap(mtReviewPicFuture);

        for (MTReviewData mtReviewData : mtQueryResult.getMtReviewDataList()) {
            List<VideoData> videoDataList = Lists.newArrayList();
            if (MapUtils.isNotEmpty(mtReviewVideoInfoMap)) {
                if (CollectionUtils.isNotEmpty(mtReviewData.getReviewVideoList())) {
                    for (ReviewVideo reviewVideo : mtReviewData.getReviewVideoList()) {
                        if (Objects.nonNull(mtReviewVideoInfoMap.get(reviewVideo.getVideoId()))) {
                            videoDataList.add(mtReviewVideoInfoMap.get(reviewVideo.getVideoId()));
                        }
                    }
                }
            }
            List<MtReviewPicInfo> mtReviewPicInfoList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(mtReviewData.getReviewPics())) {
                for (ReviewPic reviewPic : mtReviewData.getReviewPics()) {
                    MtReviewPicInfo mtReviewPicInfo = mtReviewPicInfoMap.get(reviewPic.getPicId());
                    if (mtReviewPicInfo != null) {
                        mtReviewPicInfoList.add(mtReviewPicInfo);
                    }
                }
            }
            MtUserDto userModel = MapUtils.isEmpty(userModelMap) ? null : userModelMap.get(mtReviewData.getUserId());
            List<ProductReviewPicture> defaultReviewPicList = ShopReviewHelper.buildMtReviewPicList(videoDataList, mtReviewPicInfoList);
            ProductReviewDetail reviewDetailDO = ShopReviewHelper.mtReviewDataToReviewDetailDO(mtReviewData, userModel, defaultReviewPicList, shopReviewCtx);
            if (reviewDetailDO != null) {
                // 构造-客单价
                reviewDetailDO.setOrderPrice(buildPrice(mtReviewData.getExpenseList()));
                reviewDetailDOList.add(reviewDetailDO);
            }
        }
        return reviewDetailDOList;
    }

    public Future getMtReviewPicFuture(List<Long> picIds) {
        if (CollectionUtils.isEmpty(picIds)) {
            return null;
        }
        try {
            mtReviewPicServiceFuture.getMtReviewPicInfoList(Lists.newArrayList(picIds));
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getMtReviewPicInfoList error", e);
        }
        return null;
    }

    public Future getMtReviewVideoFuture(List<Long> videos) {
        if (CollectionUtils.isEmpty(videos)) {
            return null;
        }
        try {
            mtReviewVideoServiceFuture.batchLoadVideoData(Lists.newArrayList(videos));
            return FutureFactory.getFuture();
        } catch (Exception e) {
            log.error("getMtReviewPicInfoList error", e);
        }
        return null;
    }

    public ReviewCount getReviewCount(int mtDealId, Future future) {
        Map<Integer, ReviewCount> reviewCountMap = getFutureResult(future);
        if (MapUtils.isNotEmpty(reviewCountMap)) {
            return reviewCountMap.get(mtDealId);
        }
        return null;
    }

    public Map<Long, MtUserDto> getUserModelMap(Set<Long> userIdSet) {
        if (CollectionUtils.isEmpty(userIdSet)) {
            return null;
        }
        try {
            return userWrapperService.getUserModelMap(userIdSet, UserFieldsTypeEnum.SAMPLE_INFO.getUserFieldsType());
        } catch (Exception e) {
            log.error("UserWrapper.getUserModelMap error", e);
        }
        return null;
    }

    public <T> T getFutureResult(Future serviceFuture) {
        return getFutureResult(serviceFuture, Strings.EMPTY, Strings.EMPTY);
    }

    public Map<Long, MtReviewPicInfo> getMtReviewPicInfoMap(Future mtReviewPicFuture) {
        Map<Long, MtReviewPicInfo> result = Maps.newHashMap();
        List<MtReviewPicInfo> mtReviewPicInfoList = getFutureResult(mtReviewPicFuture);
        if (CollectionUtils.isNotEmpty(mtReviewPicInfoList)) {
            for (MtReviewPicInfo mtReviewPicInfo : mtReviewPicInfoList) {
                result.put(mtReviewPicInfo.getPicId(), mtReviewPicInfo);
            }
        }
        return result;
    }


    public <T> T getFutureResult(Future serviceFuture, String className, String info) {
        if (serviceFuture == null) {
            return null;
        }
        try {
            return (T) serviceFuture.get(1000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("getFutureResult error, className:{}, info:{}", className, info, e);
        }
        return null;
    }

    public Map<Long, VideoData> getMtReviewVideoInfoMap(Future mtReviewVideoFuture) {
        return getFutureResult(mtReviewVideoFuture);
    }

    /**
     * 从评论内容中获取动态标签
     * 包括非正面评价标签
     */
    private List<ProductReviewTagFilter> createReviewTag(Future allShopReviewTagFuture, ShopReviewCtx shopReviewCtx) {
        List<ProductReviewTagFilter> reviewTagDOList = Lists.newArrayList();
        createBaseReviewTag(allShopReviewTagFuture, reviewTagDOList, shopReviewCtx);
        return reviewTagDOList;
    }

    /**
     * 检测图片、视频状态，过滤掉被屏蔽、删除的照片
     * 视频，图片状态集合，PicVideoStatusEnum
     * 返回值：
     * true：需要过滤
     * false：不用过滤
     *
     * @param shopReviewCtx
     * @param userid
     * @param status
     * @return
     */
    private Boolean filterPicStatusAndOwner(ShopReviewCtx shopReviewCtx, Long userid, Integer status) {
        if (Objects.isNull(status)) {
            return Boolean.TRUE;
        }
        // 可正常展示
        if (PicVideoStatusEnum.NORMAL.code == status.intValue()) {
            return Boolean.FALSE;
        }
        // 主态展示，客态不展示
        if (PicVideoStatusEnum.AUDIT.code == status.intValue() && (Objects.nonNull(userid) && userid.longValue() == shopReviewCtx.getMtUserId())) {//已确认判断平台后再使用
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 从评论内容中获取动态标签
     */
    private void createBaseReviewTag(Future mtShopReviewTagFuture, List<ProductReviewTagFilter> reviewTagDOList, ShopReviewCtx shopReviewCtx) {
        Response response = getFutureResult(mtShopReviewTagFuture);
        if (response == null) {
            return;
        }
        createDynamicAbstract(response, reviewTagDOList, shopReviewCtx);
    }

    private void createDynamicAbstract(Response response, List<ProductReviewTagFilter> reviewAbstracts, ShopReviewCtx shopReviewCtx) {

        if (!response.getStatus().equals(Response.OK) || CollectionUtils.isEmpty(response.getRecordList())) {
            return;
        }

        List<Record> recordList = response.getRecordList();
        List<ProductReviewTagFilter> reviewAbstractList = Lists.newArrayList();
        int count = 1;
        for (Record record : recordList) {
            Integer reviewCount = Integer.parseInt(record.get("hit"));
            String key = record.get("tag");
            String[] abstractWithAffective = key.split("_");
            if (abstractWithAffective.length < 2) {
                continue;
            }
            ProductReviewTagFilter reviewAbstract = new ProductReviewTagFilter();
            reviewAbstract.setTitle(abstractWithAffective[0].trim());
            reviewAbstract.setReviewCount(reviewCount);
            reviewAbstract.setType(1);
            reviewAbstract.setAffection(Integer.parseInt(abstractWithAffective[1])); // 不做值的校验
            reviewAbstract.setUrl(buildReviewTagUrl(shopReviewCtx, reviewAbstract));
            reviewAbstractList.add(reviewAbstract);
            count++;
        }
        if (CollectionUtils.isNotEmpty(reviewAbstractList)) {
            // 按评价标签的正向含义到负向含义排序 情感：喜欢：1，不喜欢：-1，中立：0
            reviewAbstractList.sort(Comparator.comparing(ProductReviewTagFilter::getAffection).reversed());
            reviewAbstracts.addAll(reviewAbstractList);
        }
    }

    private String buildReviewTagUrl(ShopReviewCtx shopReviewCtx, ProductReviewTagFilter reviewAbstract) {
        return String.format(MT_REVIEW_TAG_URL, shopReviewCtx.getMtId(), ShopUrlUtils.urlEncode(reviewAbstract.getTitle()), 700);
    }

    private static String buildPrice(List<Expense> expenseInfoList) {
        String price = "";
        if (CollectionUtils.isEmpty(expenseInfoList)) {
            return price;
        }
        String title = expenseInfoList.get(0).getTitle();
        int avgPrice = expenseInfoList.get(0).getExpense().intValue();
        if ("人均".equals(title) && avgPrice > 0) {
            price = "￥" + avgPrice + "/人";
        } else {
            price = (avgPrice > 0) ? "￥" + avgPrice : StringUtils.EMPTY;
        }
        return price;
    }
}