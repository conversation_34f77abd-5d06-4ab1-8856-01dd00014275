package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

/**
 * <AUTHOR>
 * @date 2025/4/16 15:44
 */
public interface GlassesThirdCategory {
    // 定义不同的categoryId常量
    int REPAIR_GLASS = 148002; // 维修保养
    int OTHER_GLASS = 152000; // 眼镜（其他）
    int INTELLIGENT_GLASS = 149001; // 智能眼镜
    int OPTICAL = 147001; // 验光
    int CHILD_OPTICAL = 148000; // 儿童验光
    int MYOPIA_GLASSES = 149000; // 近视配镜
    int CHILD_GLASSES = 131013; // 儿童配镜
    int LENS_ONLY = 148001; // 仅镜片
    int READING_GLASSES = 150000; // 老花眼镜
    int CONTACT_LENS = 110011; // 隐形眼镜
    int SUNGLASSES = 147002; // 太阳眼镜
    int FRAME_ONLY = 150001; // 仅镜框
}
