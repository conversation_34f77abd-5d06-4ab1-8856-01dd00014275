package com.sankuai.dzshoppingguide.product.detail.application.builder.instructions;


import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.enums.MetaVersionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomer;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomerFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnPurchaseNoteDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayItemDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayValueDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PurchaseNoteModuleDTO;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Builder(
        moduleKey = ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductPurchaseNoteFetcher.class,
                ProductCustomerFetcher.class,
                ProductAttrFetcher.class,
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class
        }
)
/**
 * 须知条浮层-展开详情
 */
public class InstructionsModuleBuilder extends BaseBuilder<PnPurchaseNoteDTO> {

    private static final String OLD_REFUND_INFO = "有效期内可申请全额退款";
    private static final String NEW_REFUND_INFO = "若次数未使用，可随时退全部实付金额。若发生核销后再申请退款，剩余所有未核销次数将一起退款，本商品为阶梯定价品，退款金额=团购实付金额-已核销次数的单次价格之和。";
    private static final String NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS = "购买后若全部未使用，在有效期内可申请全额退款；过期自动将未使用部分退款；若发生核销后再申请退款，剩余所有未核销次数将一起退款";


    @Override
    public PnPurchaseNoteDTO doBuild() {
        ProductPurchaseNote productPurchaseNote = null;
        PnPurchaseNoteDTO pnPurchaseNoteDTO = null;
        productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
        ProductCustomer productCustomer = getDependencyResult(ProductCustomerFetcher.class);
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        MetaVersionEnum metaVersionEnum = DealVersionUtils.isOldMetaVersion(baseInfo, productCategory, LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);

        switch (request.getProductTypeEnum()) {
            case RESERVE:
                if (productPurchaseNote == null || productPurchaseNote.getPurchaseNote() == null || productCustomer == null || baseInfo == null) {
                    return pnPurchaseNoteDTO;
                }
                pnPurchaseNoteDTO = fillPurchaseNoteStructureInfo(productPurchaseNote.getPurchaseNote(), productCustomer, baseInfo);
                break;
            case DEAL:
                if (Objects.equals(MetaVersionEnum.OLD_VERSION,metaVersionEnum) && productCategory != null && productCategory.getProductSecondCategoryId() == 318) {
                    // 茶馆老团单卡控
                    return null;
                }
                //声明基础信息
                DealCtx ctx = new DealCtx();
                ctx.setCategoryId(Math.toIntExact(productCategory.getProductSecondCategoryId()));
                ctx.setCustomerId(productCustomer.getOriginCustomerId());
                ctx.setIsApp(true);
                ctx.setDealTimesCard(TimesDealUtil.isDealTimesCard(baseInfo));
                ctx.setMiniProgram(request.getClientTypeEnum().isInWxXCX());

                productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
                // PurchaseNoteDTO purchaseNoteDTO = PurchaseNotesConvertUtils.buildInstruction(productPurchaseNote, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
                PurchaseNoteDTO purchaseNote = productPurchaseNote.getPurchaseNote();
                if (purchaseNote == null) {
                    return null;
                }

                // 团购次卡是否有阶梯退款规则
                boolean hasPurchaseNoteTable = TimesDealUtil.hasPurchaseNoteTable(purchaseNote, baseInfo);
                ctx.setHasPurchaseNoteTable(hasPurchaseNoteTable);

                pnPurchaseNoteDTO = assembleStructurePurchaseNoteDTOForDeal(purchaseNote, new PnPurchaseNoteDTO(), ctx);

                // 处理团购次卡购买须知
                DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(pnPurchaseNoteDTO,ctx);
        }
        return pnPurchaseNoteDTO;
    }

    /**
     * 拼装PnPurchaseNoteDTO
     * @param purchaseNoteDTO
     * @param pnPurchaseNoteDTO
     * @return
     */
    private PnPurchaseNoteDTO assembleStructurePurchaseNoteDTOForDeal(PurchaseNoteDTO purchaseNoteDTO, PnPurchaseNoteDTO pnPurchaseNoteDTO, DealCtx ctx) {
        pnPurchaseNoteDTO.setPnTitle(purchaseNoteDTO.getTitle());
        pnPurchaseNoteDTO.setPnModules(assemblePNModuleList(purchaseNoteDTO.getModules()));
        pnPurchaseNoteDTO.setSubTitle(ReminderHelper.getReminderSummary(ctx));
        return pnPurchaseNoteDTO;
    }

    /**
     * 拼装List<PurchaseNoteModuleDTO>
     * @param modules
     * @return
     */
    private List<PurchaseNoteModuleDTO> assemblePNModuleList(List<StandardDisplayModuleDTO> modules) {
        return ListUtils.emptyIfNull(modules).stream().map(moduleDTO -> assemblePNModule(moduleDTO)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayModuleDTO
     * @param moduleDTO
     * @return
     */
    private PurchaseNoteModuleDTO assemblePNModule(StandardDisplayModuleDTO moduleDTO) {
        PurchaseNoteModuleDTO purchaseNoteModuleDTO = new PurchaseNoteModuleDTO();
        purchaseNoteModuleDTO.setPnModuleName(moduleDTO.getModuleName());
        purchaseNoteModuleDTO.setPnIcon(moduleDTO.getIcon());
        purchaseNoteModuleDTO.setPnItems(assemblePNItemList(moduleDTO.getItems()));
        return purchaseNoteModuleDTO;
    }

    /**
     * 拼装List<PnStandardDisplayItemDTO>
     * @param items
     * @return
     */
    private List<PnStandardDisplayItemDTO> assemblePNItemList(List<StandardDisplayItemDTO> items) {
        return ListUtils.emptyIfNull(items).stream().map(itemDTO -> assemblePNItem(itemDTO)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayItemDTO
     * @param itemDTO
     * @return
     */
    private PnStandardDisplayItemDTO assemblePNItem(StandardDisplayItemDTO itemDTO) {
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = new PnStandardDisplayItemDTO();
        pnStandardDisplayItemDTO.setPnItemName(itemDTO.getItemName());
        pnStandardDisplayItemDTO.setPnItemValues(assemblePNItemValueList(itemDTO.getItemValues()));
        return pnStandardDisplayItemDTO;
    }

    /**
     * 拼装List<PnStandardDisplayValueDTO>
     * @param itemValues
     * @return
     */
    private List<PnStandardDisplayValueDTO> assemblePNItemValueList(List<StandardDisplayValueDTO> itemValues) {
        return ListUtils.emptyIfNull(itemValues).stream().map(itemValueDTO -> assemblePNItemValue(itemValueDTO)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayValueDTO
     * @param itemValueDTO
     * @return
     */
    private PnStandardDisplayValueDTO assemblePNItemValue(StandardDisplayValueDTO itemValueDTO) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(itemValueDTO.getType());
        pnStandardDisplayValueDTO.setPnValue(itemValueDTO.getValue());
        return pnStandardDisplayValueDTO;
    }

    /**
     * 获取购买须知结构化信息并填充到ctx中
     */
    private PnPurchaseNoteDTO fillPurchaseNoteStructureInfo(PurchaseNoteDTO purchaseNote, ProductCustomer productCustomer, ProductBaseInfo baseInfo) {
        //声明基础信息
        DealCtx ctx = new DealCtx();
        if (request.getProductTypeEnum().equals(ProductTypeEnum.RESERVE)) {
            ctx.setRequestSource("pre_order_deal");
        }
        ctx.setCategoryId(Math.toIntExact(baseInfo.getBasic().getCategoryId()));
        ctx.setCustomerId(productCustomer.getOriginCustomerId());
        ctx.setIsApp(true);

        if (Objects.nonNull(purchaseNote) || ctx.getCustomerId() == 0) {
            PnPurchaseNoteDTO pnPurchaseNoteDTO = new PnPurchaseNoteDTO();
            // 新版退款表格控制开关
            setHasPurchaseNoteTable(purchaseNote, ctx);
            // 小程序无法展示新版本的次卡退款表格信息,只需要展示退款信息文案即可
            PnPurchaseNoteDTO purchaseNoteDTO = assembleStructurePurchaseNoteDTO(purchaseNote, pnPurchaseNoteDTO, ctx);
            handleRefundDetail(purchaseNoteDTO, ctx);
            return purchaseNoteDTO;
        }
        return null;
    }


    private static void setHasPurchaseNoteTable(PurchaseNoteDTO sourcePurchaseNoteDTO, DealCtx ctx) {
        // todo 非团购次卡  !TimesDealUtil.isDealTimesCard()
        if (!TimesDealUtil.isMultiTimesCard(ctx)) {
            return;
        }
        if (Objects.isNull(sourcePurchaseNoteDTO)) {
            return;
        }
        List<StandardDisplayModuleDTO> modules = sourcePurchaseNoteDTO.getModules();

        if (CollectionUtils.isEmpty(modules)) {
            return;
        }

        // 说否含有新增的退款规则模块
        boolean isContainRefundModule = modules.stream().filter(Objects::nonNull)
                .anyMatch(module -> Objects.equals("退款规则", module.getModuleName()));

        // 是否含有退款表格信息
        boolean isContainRefundTable = modules.stream().filter(Objects::nonNull).map(StandardDisplayModuleDTO::getItems)
                .filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull)
                .map(StandardDisplayItemDTO::getItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .anyMatch(item -> item != null && item.getType() == 4);

        // type为4 或者 含有退款规则的module 则说明含有退款表格信息
        ctx.setHasPurchaseNoteTable(
                (isContainRefundModule || isContainRefundTable) && LionConfigUtils.hasPurchaseNoteTableSwitch());
    }

    /**
     * 拼装PnPurchaseNoteDTO
     *
     * @param purchaseNoteDTO
     * @param pnPurchaseNoteDTO
     * @return
     */
    private static PnPurchaseNoteDTO assembleStructurePurchaseNoteDTO(PurchaseNoteDTO purchaseNoteDTO, PnPurchaseNoteDTO pnPurchaseNoteDTO, DealCtx ctx) {
        pnPurchaseNoteDTO.setPnTitle(replaceAppointTitleToPreOrder(purchaseNoteDTO.getTitle(), ctx));
        pnPurchaseNoteDTO.setPnModules(assemblePNModuleList(purchaseNoteDTO.getModules(), ctx));
        pnPurchaseNoteDTO.setSubTitle(ReminderHelper.getReminderSummary(ctx));
        return pnPurchaseNoteDTO;
    }


    /**
     * 拼装List<PurchaseNoteModuleDTO>
     *
     * @param modules
     * @return
     */
    private static List<PurchaseNoteModuleDTO> assemblePNModuleList(List<StandardDisplayModuleDTO> modules, DealCtx ctx) {
        return ListUtils.emptyIfNull(modules).stream().map(moduleDTO -> assemblePNModule(moduleDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayModuleDTO
     *
     * @param moduleDTO
     * @return
     */
    private static PurchaseNoteModuleDTO assemblePNModule(StandardDisplayModuleDTO moduleDTO, DealCtx ctx) {
        PurchaseNoteModuleDTO purchaseNoteModuleDTO = new PurchaseNoteModuleDTO();
        purchaseNoteModuleDTO.setPnModuleName(replaceAppointToPreOrder(moduleDTO.getModuleName(), ctx));
        purchaseNoteModuleDTO.setPnIcon(moduleDTO.getIcon());
        purchaseNoteModuleDTO.setPnItems(assemblePNItemList(moduleDTO.getItems(), ctx));
        return purchaseNoteModuleDTO;
    }

    /**
     * 替换文字
     *
     * @param text
     * @param ctx
     * @return
     */
    private static String replaceAppointToPreOrder(String text, DealCtx ctx) {
        return replaceText(text, ctx, "预约", "预订");
    }

    private static String replaceText(String text, DealCtx ctx, String target, String replacement) {
        if (StringUtils.isEmpty(text) || !DealCtxHelper.isPreOrderDeal(ctx)) {
            return text;
        }
        return text.replace(target, replacement);
    }

    /**
     * 拼装List<PnStandardDisplayItemDTO>
     *
     * @param items
     * @return
     */
    private static List<PnStandardDisplayItemDTO> assemblePNItemList(List<StandardDisplayItemDTO> items, DealCtx ctx) {
        return ListUtils.emptyIfNull(items).stream().map(itemDTO -> assemblePNItem(itemDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayItemDTO
     *
     * @param itemDTO
     * @return
     */
    private static PnStandardDisplayItemDTO assemblePNItem(StandardDisplayItemDTO itemDTO, DealCtx ctx) {
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = new PnStandardDisplayItemDTO();
        pnStandardDisplayItemDTO.setPnItemName(replaceAppointToPreOrder(itemDTO.getItemName(), ctx));
        if ("pre_order_deal".equals(ctx.getRequestSource())) {
            pnStandardDisplayItemDTO.setPnItemValues(assemblePNItemValueListByPRE(itemDTO.getItemValues(), ctx));
        } else {
            pnStandardDisplayItemDTO.setPnItemValues(assemblePNItemValueList(itemDTO.getItemValues(), ctx));
        }
        return pnStandardDisplayItemDTO;
    }


    /**
     * 预定场景下将文本按照 按 sentenceSequence 分组，并按 wordSequence 排序后拼接
     *
     * @param itemValues
     * @param ctx
     * @return
     */
    public static List<PnStandardDisplayValueDTO> assemblePNItemValueListByPRE(List<StandardDisplayValueDTO> itemValues, DealCtx ctx) {
        if (CollectionUtils.isEmpty(itemValues)) {
            return Collections.emptyList();
        }

        // 按 sentenceSequence 分组，并按 wordSequence 排序后拼接
        Map<Integer, String> mergedSentences = itemValues.stream()
                .collect(Collectors.groupingBy(
                        StandardDisplayValueDTO::getSentenceSequence,
                        TreeMap::new, // 使用 TreeMap 保持顺序
                        Collectors.mapping(
                                itemValueDTO -> new AbstractMap.SimpleEntry<>(itemValueDTO.getWordSequence(), itemValueDTO.getValue().trim()),
                                Collectors.toList()
                        )
                ))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .sorted(Map.Entry.comparingByKey())
                                .map(Map.Entry::getValue)
                                .filter(value -> !value.isEmpty()) // 去除空串
                                .collect(Collectors.joining()) // 不直接添加空格
                ));

        // 将拼接后的结果转换为 PnStandardDisplayValueDTO 对象
        return mergedSentences.values().stream()
                .map(value -> new PnStandardDisplayValueDTO(1, value)) // 假设类型为 1
                .collect(Collectors.toList());
    }



    /**
     * 拼装List<PnStandardDisplayValueDTO>
     *
     * @param itemValues
     * @return
     */
    private static List<PnStandardDisplayValueDTO> assemblePNItemValueList(List<StandardDisplayValueDTO> itemValues, DealCtx ctx) {
        if (CollectionUtils.isEmpty(itemValues)) {
            return Collections.emptyList();
        }
        return ListUtils.emptyIfNull(itemValues).stream().map(itemValueDTO -> assemblePNItemValue(itemValueDTO, ctx)).collect(Collectors.toList());
    }

    /**
     * 拼装PnStandardDisplayValueDTO
     *
     * @param itemValueDTO
     * @return
     */
    private static PnStandardDisplayValueDTO assemblePNItemValue(StandardDisplayValueDTO itemValueDTO, DealCtx ctx) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(itemValueDTO.getType());
        pnStandardDisplayValueDTO.setPnValue(replaceAppointToPreOrder(itemValueDTO.getValue(), ctx));
        return pnStandardDisplayValueDTO;
    }

    private void handleRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        List<Long> whiteList = LionConfigUtils.getCustomerWhiteList();
        long customerId = ctx.getCustomerId();
        if (CollectionUtils.isEmpty(whiteList) || !whiteList.contains(customerId)) {
            handleMiniRefundDetail(purchaseNoteDTO, ctx);
            return;
        }
        // 增加客户白名单的能力
        handleWhiteListCustomer(purchaseNoteDTO, ctx);
    }


    private void handleMiniRefundDetail(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        boolean inWxXCX = request.getClientTypeEnum().isInWxXCX();
        if (!inWxXCX) {
            return;
        }

        if (Objects.isNull(purchaseNoteDTO)) {
            return;
        }
        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }
        // 删除新的退款规则
        deleteNewRefundInfo(purchaseNoteDTO);
        // 替换旧退款规则文字描述
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO);
    }

    private static void handleWhiteListCustomer(PnPurchaseNoteDTO purchaseNoteDTO, DealCtx ctx) {
        if (Objects.isNull(purchaseNoteDTO)) {
            return;
        }
        if (!ctx.isHasPurchaseNoteTable()) {
            return;
        }
        deleteNewRefundInfo(purchaseNoteDTO);
        replaceRefundInfo(purchaseNoteDTO, NEW_REFUND_INFO_FOR_WHITE_LIST_CUSTOMERS);
    }

    private static void deleteNewRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO) {
        List<PurchaseNoteModuleDTO> beforePnModules = purchaseNoteDTO.getPnModules();
        if (CollectionUtils.isEmpty(beforePnModules)) {
            return;
        }
        List<PurchaseNoteModuleDTO> afterPnModules = beforePnModules.stream().filter(Objects::nonNull)
                .filter(module -> !Objects.equals("退款规则", module.getPnModuleName())).collect(Collectors.toList());

        purchaseNoteDTO.setPnModules(afterPnModules);
    }

    private static void replaceRefundInfo(PnPurchaseNoteDTO purchaseNoteDTO, String newRefundInfo) {
        List<PurchaseNoteModuleDTO> pnModules = purchaseNoteDTO.getPnModules();
        if (CollectionUtils.isEmpty(pnModules)) {
            return;
        }
        Optional<PurchaseNoteModuleDTO> warmTipsOpt = pnModules.stream().filter(Objects::nonNull)
                .filter(module -> Objects.equals("温馨提示", module.getPnModuleName())).findFirst();
        if (!warmTipsOpt.isPresent()) {
            return;
        }
        PurchaseNoteModuleDTO warmTips = warmTipsOpt.get();
        List<PnStandardDisplayItemDTO> pnItems = warmTips.getPnItems();
        if (CollectionUtils.isEmpty(pnItems)) {
            return;
        }
        // 找出包含旧退款信息的模块
        Optional<PnStandardDisplayValueDTO> oldRefundInfoOpt = pnItems.stream().filter(Objects::nonNull)
                .map(PnStandardDisplayItemDTO::getPnItemValues).filter(Objects::nonNull).flatMap(List::stream)
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotBlank(item.getPnValue())
                        && item.getPnValue().contains(OLD_REFUND_INFO))
                .findFirst();

        // 有则改之
        if (oldRefundInfoOpt.isPresent()) {
            PnStandardDisplayValueDTO pnStandardDisplayValueDTO = oldRefundInfoOpt.get();
            pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
            return;
        }

        // 无则加勉
        Optional<PnStandardDisplayItemDTO> firstPnStandardDisplayItemDTOOpt = pnItems.stream().filter(Objects::nonNull)
                .findFirst();

        if (!firstPnStandardDisplayItemDTOOpt.isPresent()) {
            return;
        }
        PnStandardDisplayItemDTO pnStandardDisplayItemDTO = firstPnStandardDisplayItemDTOOpt.get();
        List<PnStandardDisplayValueDTO> pnItemValues = pnStandardDisplayItemDTO.getPnItemValues();
        if (CollectionUtils.isEmpty(pnItemValues)) {
            pnStandardDisplayItemDTO.setPnItemValues(Lists.newArrayList(buildPnStandardDisplayValueDTO(newRefundInfo)));
        } else {
            pnItemValues.add(buildPnStandardDisplayValueDTO(newRefundInfo));
        }
    }

    private static PnStandardDisplayValueDTO buildPnStandardDisplayValueDTO(String newRefundInfo) {
        PnStandardDisplayValueDTO pnStandardDisplayValueDTO = new PnStandardDisplayValueDTO();
        pnStandardDisplayValueDTO.setPnType(1);
        pnStandardDisplayValueDTO.setPnValue(newRefundInfo);
        return pnStandardDisplayValueDTO;
    }

    private static String replaceAppointTitleToPreOrder(String text, DealCtx ctx) {
        return replaceText(text, ctx, "购买须知", "预订须知");
    }
}



