package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FloatingLayerOpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.FeaturesLayer;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 默认保障模块构造器-过期退、随时退
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class,
                GuaranteeTagFetcher.class
        }
)
public class DefaultDealGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {
    @Override
    public ProductDetailGuaranteeVO doBuild() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductGuaranteeTagInfo productGuaranteeTagInfo = getDependencyResult(GuaranteeTagFetcher.class);
        // 保障条内容
        List<GuaranteeInstructionsContentVO> contents = buildStandardGuaranteeContents();
        // 最后一个标签右边加上右箭头
        addRightArrow(contents.get(contents.size() - 1));
        // 保障条浮层跳转方式
        FeaturesLayer floatingLayer = buildGuaranteeFloatingLayer(productCategory, productGuaranteeTagInfo);
        return new ProductDetailGuaranteeVO("保障", contents, floatingLayer, FloatingLayerOpenTypeEnum.OPEN_LAYER);
    }
}
