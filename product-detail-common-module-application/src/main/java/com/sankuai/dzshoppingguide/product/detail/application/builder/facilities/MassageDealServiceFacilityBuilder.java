package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums.ServiceFacilityEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums.ServiceMaterialAndToolEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.FacilitiesVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.facilities.vo.DetailServiceFacilitiesVO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-28
 * @desc 足疗按摩团单服务设施
 */
@Slf4j
@Builder(
        moduleKey = ModuleKeyConstants.DEAL_DETAIL_FACILITIES,
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                CommonModuleStarter.class,
                ProductServiceProjectFetcher.class
        }
)
public class MassageDealServiceFacilityBuilder extends BaseVariableBuilder<DetailServiceFacilitiesVO> {

    private static final String NOTHING_VALUE = "无";

    private static final String ATTR_VALUE_SEPARATE = "、";

    /**
     * 玩乐设施的key
     */
    private static final String PLAY_FACILITY_KEY = "serviceFacility";

    /**
     * 其他服务的key
     */
    private static final String OTHER_FACILITY_KEY = "OtherStoreServices";

    private static final String FACILITY_ITEM_ICON = "https://p0.meituan.net/ingee/b3e9ca0e026d1ce44b43ffaeaa12a4ac784.png";

    @Override
    public DetailServiceFacilitiesVO doBuild() {
        ProductServiceProject productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        if (Objects.isNull(productServiceProject)) {
            return null;
        }
        // 获取第一个必选组的第一个skuItemDto(足疗标准化团单特点)
        ServiceProjectDTO firstMustSkuModel = DealAttrHelper.getFirstMustSkuFromServiceProject(productServiceProject.getServiceProject());
        if (Objects.isNull(firstMustSkuModel)) {
            return null;
        }
        List<FacilitiesVO> serviceFacilities = Lists.newArrayList();
        // 1. 材料工具
        List<FacilitiesVO> materialTools = buildServiceMaterialTools(firstMustSkuModel);
        serviceFacilities.addAll(materialTools);
        // 2. 玩乐设施
        List<FacilitiesVO> playFacilities = buildPlayFacilities(firstMustSkuModel);
        serviceFacilities.addAll(playFacilities);
        // 3. 其他服务
        List<FacilitiesVO> otherServices = buildOtherServices(firstMustSkuModel);
        serviceFacilities.addAll(otherServices);
        // 过滤空值
        List<FacilitiesVO> facilities = serviceFacilities.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(facilities)) {
            FacilitiesVO moduleName = FacilitiesVO.builder()
                    .type(ViewComponentTypeEnum.FACILITY_TYPE_1.getType())
                    .title("服务设施")
                    .build();
            facilities.add(0, moduleName);
        }
        DetailServiceFacilitiesVO result = new DetailServiceFacilitiesVO();
        result.setDetailServiceFacilities(facilities);
        return result;
    }

    private List<FacilitiesVO> buildOtherServices(ServiceProjectDTO firstMustSkuModel) {
        String otherStoreServices = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), OTHER_FACILITY_KEY);
        if (StringUtils.isBlank(otherStoreServices) || NOTHING_VALUE.equals(otherStoreServices)) {
            return Lists.newArrayList();
        }
        List<Icon> otherServices = Lists.newArrayList();
        for (String otherServiceItem : otherStoreServices.split(ATTR_VALUE_SEPARATE)) {
            Icon item = new Icon(FACILITY_ITEM_ICON, otherServiceItem);
            otherServices.add(item);
        }
        List<FacilitiesVO> otherFacilities = Lists.newArrayList();
        // 其他服务标题
        otherFacilities.add(buildFacilityItemTitle(ServiceFacilityEnum.UNCLASSIFIED_SERVICES));
        // 其他服务
        FacilitiesVO otherFacilityItem = buildFacilityItem(otherServices);
        otherFacilities.add(otherFacilityItem);
        return otherFacilities;
    }

    private List<FacilitiesVO> buildPlayFacilities(ServiceProjectDTO firstMustSkuModel) {
        String serviceFacility = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), PLAY_FACILITY_KEY);
        if (StringUtils.isBlank(serviceFacility) || NOTHING_VALUE.equals(serviceFacility)) {
            return Lists.newArrayList();
        }
        List<Icon> facilities = Lists.newArrayList();
        for (String facilityValue : serviceFacility.split(ATTR_VALUE_SEPARATE)) {
            Icon item = new Icon(FACILITY_ITEM_ICON, facilityValue);
            facilities.add(item);
        }
        List<FacilitiesVO> playFacilities = Lists.newArrayList();
        // 玩乐设施标题
        playFacilities.add(buildFacilityItemTitle(ServiceFacilityEnum.PLAY_FACILITY));
        // 玩乐设施
        FacilitiesVO facilityItem = buildFacilityItem(facilities);
        playFacilities.add(facilityItem);
        return playFacilities;
    }

    private List<FacilitiesVO> buildServiceMaterialTools(ServiceProjectDTO firstMustSkuModel) {
        List<Icon> tools = Lists.newArrayList();
        for (ServiceMaterialAndToolEnum toolEnum : ServiceMaterialAndToolEnum.values()) {
            String toolValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), toolEnum.getToolCode());
            if (StringUtils.isNotBlank(toolValue)) {
                tools.add(new Icon(toolEnum.getIcon(), toolValue));
            }
        }
        if (CollectionUtils.isEmpty(tools)) {
            return Lists.newArrayList();
        }
        List<FacilitiesVO> materialTools = Lists.newArrayList();
        // 服务设施标题
        materialTools.add(buildFacilityItemTitle(ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL));
        // 材料工具
        FacilitiesVO toolItems = buildFacilityItem(tools);
        materialTools.add(toolItems);
        return materialTools;
    }

    private FacilitiesVO buildFacilityItem(List<Icon> contents) {
        List<String> textContents = Optional.ofNullable(contents)
                .orElse(Lists.newArrayList())
                .stream()
                .map(Icon::getText)
                .collect(Collectors.toList());
        return FacilitiesVO.builder()
                .type(ViewComponentTypeEnum.FACILITY_TYPE_3.getType())
                .contentsWithIcon(contents)
                .contents(textContents)
                .build();
    }

    private FacilitiesVO buildFacilityItemTitle(ServiceFacilityEnum serviceFacilityEnum) {
        return  FacilitiesVO.builder()
                .type(ViewComponentTypeEnum.FACILITY_TYPE_2.getType())
                .title(serviceFacilityEnum.getServiceFacility())
                .icon(serviceFacilityEnum.getNewIcon())
                .build();
    }
}
