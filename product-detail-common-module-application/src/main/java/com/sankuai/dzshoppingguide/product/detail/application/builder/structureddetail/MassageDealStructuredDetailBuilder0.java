package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.ServiceFlowParseModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BuffetFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageServiceTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SnackFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.TeaFoodEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimeUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.MassageCPVConstant.SERVICE_DURATION;

/**
 * <AUTHOR>
 * @date 2025-02-27
 * @desc 足疗团单结构化详情模块
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductAttrFetcher.class,
                ReserveTagQueryFetcher.class,
                ProductServiceProjectFetcher.class
        }
)
public class MassageDealStructuredDetailBuilder0 extends BaseVariableBuilder<ModuleDetailStructuredDetailVO> {
    /**
     * 团单三级类目
     */
    private static final String SERVICE_TYPE = "service_type";
    /**
     * 过夜规则
     */
    private static final String OVER_NIGHT_RULES = "overnightRules";
    private static final String OVER_NIGHT_SERVICE_TYPE = "OvernightServices";
    private static final String OVER_NIGHT_SERVICE_TYPE_PAY = "可付费过夜";
    private static final String OVER_NIGHT_MODULE_NAME = "过夜服务";
    private static final String OVER_NIGHT_MODULE_ICON = "https://p0.meituan.net/ingee/39345524d0f1ead891dccaefdef798b86694.png";

    @Override
    public ModuleDetailStructuredDetailVO doBuild() {
        ProductServiceProject productServiceProject = getDependencyResult(ProductServiceProjectFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (Objects.isNull(productServiceProject)) {
            return null;
        }
        // 获取第一个必选组的第一个skuItemDto(足疗标准化团单特点)
        ServiceProjectDTO firstMustSkuModel = DealAttrHelper.getFirstMustSkuFromServiceProject(productServiceProject.getServiceProject());
        if (Objects.isNull(firstMustSkuModel)) {
            return null;
        }
        // 构造服务详情模块
        List<DealDetailStructuredDetailVO> serviceDetailModule = buildServiceDetailModule(productAttr, firstMustSkuModel);
        ModuleDetailStructuredDetailVO result = new ModuleDetailStructuredDetailVO();
        result.setDealDetails(serviceDetailModule);
        return result;
    }

    private List<DealDetailStructuredDetailVO> buildOvernightServiceModule() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        String overnightServiceType = productAttr.getSkuAttrFirstValue(OVER_NIGHT_SERVICE_TYPE);
        // 如果是付费，展示在加价项目中，此模块不展示
        if (Objects.equals(overnightServiceType, OVER_NIGHT_SERVICE_TYPE_PAY)) {
            return Lists.newArrayList();
        }
        String overnightRules = productAttr.getSkuAttrFirstValue(OVER_NIGHT_RULES);
        if (StringUtils.isBlank(overnightRules)) {
            return Lists.newArrayList();
        }
        OvernightRule overnightRule = JsonUtils.fromJson(overnightRules, OvernightRule.class);
        return buildOvernightModule(overnightRule);
    }


    private List<DealDetailStructuredDetailVO> buildServiceDetailModule(ProductAttr productAttr, ServiceProjectDTO firstMustSkuModel) {

        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        // 1. 服务流程模块
        List<DealDetailStructuredDetailVO> serviceProcessModule = buildServiceProcessModule(productAttr, firstMustSkuModel);
        result.addAll(serviceProcessModule);
        // 2. 附赠项目
        List<DealDetailStructuredDetailVO> additionItemsModule = buildServiceAdditionItemsModule(firstMustSkuModel);
        result.addAll(additionItemsModule);
        // 3. 免费餐食标题
        List<DealDetailStructuredDetailVO> freeMealModule = buildFreeMealModule(firstMustSkuModel);
        result.addAll(freeMealModule);
        // 4. 过夜服务
        List<DealDetailStructuredDetailVO> overnightServiceModule = buildOvernightServiceModule();
        result.addAll(overnightServiceModule);
        // 过滤空结果
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<DealDetailStructuredDetailVO>  buildServiceAdditionItemsModule(ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> serviceAdditionItemsModule = Lists.newArrayList();
        // 附赠项目标题
        DealDetailStructuredDetailVO serviceAdditionItemTitle = buildServiceAdditionItemTitle(firstMustSkuModel);
        serviceAdditionItemsModule.add(serviceAdditionItemTitle);
        // 附赠项目
        List<DealDetailStructuredDetailVO> serviceAdditionItems = buildServiceAdditionItems(firstMustSkuModel);
        serviceAdditionItemsModule.addAll(serviceAdditionItems);
        return serviceAdditionItemsModule;
    }

    private List<DealDetailStructuredDetailVO> buildServiceProcessModule(ProductAttr productAttr, ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> serviceProcessModule = Lists.newArrayList();
        // 构造服务流程标题模块
        DealDetailStructuredDetailVO serviceProcessTitle = buildServiceProcessTitle(productAttr, firstMustSkuModel);
        serviceProcessModule.add(serviceProcessTitle);
        // 服务部位
        DealDetailStructuredDetailVO serviceBodyPart = buildServiceBodyPart(firstMustSkuModel);
        serviceProcessModule.add(serviceBodyPart);
        // 服务流程
        List<DealDetailStructuredDetailVO> serviceItems  = buildServiceItems(firstMustSkuModel);
        serviceProcessModule.addAll(serviceItems);
        return serviceProcessModule;
    }

    private List<DealDetailStructuredDetailVO> buildFreeMealModule(ServiceProjectDTO firstMustSkuModel) {
        String freeFoodType = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "freeFood");
        if (StringUtils.isBlank(freeFoodType)) {
            return Lists.newArrayList();
        }
        String foodValue = getFoodValue(firstMustSkuModel);
        String freeFoodTitle = getFreeFoodTitle(freeFoodType, firstMustSkuModel);
        DealDetailStructuredDetailVO freeMealTitle = DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .title(freeFoodTitle)
                .detail("免费")
                .build();
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        switch (freeFoodType) {
            case "自助餐畅吃":
                result.addAll(buildBuffetItems(firstMustSkuModel));
                break;
            case "小吃简餐畅吃":
                result.addAll(buildSnackItems(firstMustSkuModel));
                break;
            case "茶点水果":
                result.addAll(buildTeaItems(firstMustSkuModel));
                break;
        }
        // 插入免费餐食标题
        if (CollectionUtils.isNotEmpty(result)) {
            result.add(0, freeMealTitle);
        }
        return result;
    }

    private List<DealDetailStructuredDetailVO> buildTeaItems(ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> teaItems = Lists.newArrayList();
        for (TeaFoodEnum foodEnum : TeaFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                DealDetailStructuredDetailVO teaItem = buildFreeMealItem(foodEnum.getFoodName(), foodValue, foodEnum.getNewFoodIcon());
                teaItems.add(teaItem);
            }
        }
        return teaItems;
    }

    /**
     * 构造小吃模块
     * @param firstMustSkuModel 第一个必选sku
     * @return 小吃模块列表
     */
    private List<DealDetailStructuredDetailVO> buildSnackItems(ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> snackItems = new ArrayList<>();
        for (SnackFoodEnum foodEnum : SnackFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                DealDetailStructuredDetailVO snackItem = buildFreeMealItem(foodEnum.getFoodName(), foodValue, foodEnum.getNewFoodIcon());
                snackItems.add(snackItem);
            }
        }
        return snackItems;
    }

    /**
     * 构造自助餐模块
     * @param firstMustSkuModel 第一个必选sku
     * @return 自助餐模块列表
     */
    private List<DealDetailStructuredDetailVO> buildBuffetItems(ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> buffetItems = Lists.newArrayList();
        for (BuffetFoodEnum foodTypeEnum : BuffetFoodEnum.values()) {
            String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), foodTypeEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                DealDetailStructuredDetailVO buffetItem = buildFreeMealItem(foodTypeEnum.getFoodName(), foodValue, foodTypeEnum.getNewFoodIcon());
                buffetItems.add(buffetItem);
            }
        }
        return buffetItems;
    }

    private DealDetailStructuredDetailVO buildFreeMealItem(String title, String content, String icon) {
        return DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                .title(title)
                .content(content)
                .icon(icon)
                .build();
    }

    private String getFreeFoodTitle(String freeFoodType, ServiceProjectDTO firstMustSkuModel) {
        String fruit = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "Fruit");
        //茶点水果里，如果有水果，展示“茶点水果”；如果没有水果，展示“茶点”
        if ("茶点水果".equals(freeFoodType) && StringUtils.isBlank(fruit)) {
            return "茶点";
        }
        return freeFoodType;
    }

    private String getFoodValue(ServiceProjectDTO firstMustSkuModel) {
        String foodValue = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "MealValue");
        if (StringUtils.isBlank(foodValue)) {
            return null;
        }
        return String.format("价值¥%s", foodValue);
    }

    private List<DealDetailStructuredDetailVO> buildServiceAdditionItems(ServiceProjectDTO firstMustSkuModel) {
        String grantProduct = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "grantProduct");
        if (StringUtils.isBlank(grantProduct)) {
            return Lists.newArrayList();
        }
        DealDetailStructuredDetailVO additionItem = DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                .title(grantProduct)
                .icon("https://p0.meituan.net/ingee/4ea3ffc3dfd33d26592339cfcaf5e7212161.png")
                .build();
        return Lists.newArrayList(additionItem);
    }

    private DealDetailStructuredDetailVO buildServiceAdditionItemTitle(ServiceProjectDTO firstMustSkuModel) {
        String grantProduct = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "grantProduct");
        if (StringUtils.isBlank(grantProduct)) {
            return null;
        }
        return DealDetailStructuredDetailVO.builder()
                .title("附赠项目")
                .content("不计入项目总时长")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .build();
    }

    private List<DealDetailStructuredDetailVO> buildServiceItems(ServiceProjectDTO firstMustSkuModel) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        if (Objects.isNull(firstMustSkuModel)) {
            return result;
        }
        String serviceProcessArray = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "serviceProcessArrayNew");
        if (StringUtils.isBlank(serviceProcessArray)) {
            return result;
        }

        List<ServiceFlowParseModel> serviceFlowParseModels = JsonUtils.fromJson(serviceProcessArray, new TypeReference<List<ServiceFlowParseModel>>() {});
        for (int i = 0; i < serviceFlowParseModels.size(); i++) {
            ServiceFlowParseModel serviceFlow = serviceFlowParseModels.get(i);
            String stepTime = serviceFlow.getStepTime() > 0 ? String.format("%s分钟", serviceFlow.getStepTime()) : StringUtils.EMPTY;
            DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                    .type(ViewComponentTypeEnum.DETAIL_TYPE_3.getType())
                    .content(serviceFlow.getServiceMethod())
                    .subContent(serviceFlow.getServiceProcessInstructions())
                    .detail(stepTime)
                    .order(i + 1);
            if (i == 0) {
                builder.title("服务流程");
            }
            result.add(builder.build());
        }
        return result;
    }

    private DealDetailStructuredDetailVO buildServiceBodyPart(ServiceProjectDTO firstMustSkuModel) {
        if (Objects.isNull(firstMustSkuModel)) {
            return null;
        }
        String serviceBodyRange = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "serviceBodyRange");
        if (StringUtils.isBlank(serviceBodyRange)) {
            return null;
        }
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder()
                .title("服务部位")
                .type(ViewComponentTypeEnum.DETAIL_TYPE_2.getType());
        String bodyRegion = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), "bodyRegion");
        if ("全身".equals(bodyRegion)) {
            return builder.content(String.format("全身（含%s）", serviceBodyRange))
                    .build();
        }
        return builder.content(serviceBodyRange)
                .build();
    }

    /**
     * 构造服务流程标题模块
     * @param productAttr 商品属性
     * @param firstMustSkuModel 第一个必选sku
     * @return 结构化服务详情最小模型单元
     */
    private DealDetailStructuredDetailVO buildServiceProcessTitle(ProductAttr productAttr, ServiceProjectDTO firstMustSkuModel) {
        // 服务流程标题
        String serviceType = productAttr.getSkuAttrFirstValue(SERVICE_TYPE);
        String serviceProcessTitle = getServiceFlowSkuName2(productAttr, serviceType);
        if (StringUtils.isBlank(serviceProcessTitle)) {
            return null;
        }
        String serviceItemPrice = buildServiceItemPrice(firstMustSkuModel);
        // 项目时长
        String serviceItemDuration = buildServiceItemDuration(firstMustSkuModel);
        return DealDetailStructuredDetailVO.builder()
                .title(serviceProcessTitle)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .detail(serviceItemPrice)
                .content(serviceItemDuration)
                .build();
    }

    private String buildServiceItemDuration(ServiceProjectDTO firstMustSkuModel) {
        if (Objects.isNull(firstMustSkuModel) || CollectionUtils.isEmpty(firstMustSkuModel.getAttrs())) {
            return null;
        }
        String serviceDuration = DealAttrHelper.getAttrValue(firstMustSkuModel.getAttrs(), SERVICE_DURATION);
        return StringUtils.isBlank(serviceDuration) ? null : serviceDuration + "分钟";
    }

    private String buildServiceItemPrice(ServiceProjectDTO firstMustSkuModel) {
        if (Objects.isNull(firstMustSkuModel) || Objects.isNull(firstMustSkuModel.getMarketPrice())) {
            return null;
        }
        String itemPrice = firstMustSkuModel.getMarketPrice();
        return String.format("¥%s", itemPrice);
    }

    /**
     * 获取各三级分类的服务项目名称
     */
    protected String getServiceFlowSkuName2(ProductAttr productAttr, String serviceType) {
        MassageServiceTypeEnum serviceTypeEnum = MassageServiceTypeEnum.getEnumByServiceType(serviceType);
        if (serviceTypeEnum == null) {
            return null;
        }
        switch (serviceTypeEnum) {
            case FOOT_MASSAGE:
            case SCRAPING:
                // 足疗、刮痧：三级类目名称，如刮痧
                return serviceType;
            case EAR_PICKING:
                // 服务手法
                return productAttr.getSkuAttrFirstValue("serviceTechnique");
            case MASSAGE:
            case ESSENTIAL_OIL_SPA:
            case ULNA:
                // 推拿/按摩、精油SOA、推拿正骨：只有当服务部位范围为全身时拼接，为局部部位时直接展示服务手法
                String bodyRegion = productAttr.getSkuAttrFirstValue("bodyRegion");
                String serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                if ("全身".equals(bodyRegion)) {
                    return "全身" + (StringUtils.isNotBlank(serviceTechnique) ? serviceTechnique : "");
                }
                return serviceTechnique;
            case MOXIBUSTION:
                // 艾灸：三级分类+服务手法+特色灸法， 用 "|"分割。 如 艾灸｜盒灸｜三伏灸
                serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                String moxibustionMethod = productAttr.getSkuAttrFirstValue("moxibustionMethod");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "｜" + serviceTechnique) + (StringUtils.isBlank(moxibustionMethod) ? "" : "｜" + moxibustionMethod);
            case CUP:
                // 拔罐：三级类目+服务手法，如拔罐（走罐）
                serviceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                return serviceType + (StringUtils.isBlank(serviceTechnique) ? "" : "（" + serviceTechnique + "）");
            case HEAD:
                // 头疗：三级类目+ "|"+服务部位+服务手法（若服务部位有多个，只取优先级最高的第一个）
                String headServiceBodyRange = productAttr.getSkuAttrFirstValue("serviceBodyRange");
                if (StringUtils.isNotBlank(headServiceBodyRange) && headServiceBodyRange.contains("、")) {
                    headServiceBodyRange = headServiceBodyRange.split("、")[0];
                }
                String headServiceTechnique = productAttr.getSkuAttrFirstValue("serviceTechnique");
                return serviceType + "｜" + (StringUtils.isNotBlank(headServiceBodyRange) ? headServiceBodyRange : "") + (StringUtils.isNotBlank(headServiceTechnique) ? headServiceTechnique : "");
            default:
                return null;
        }
    }

    private List<DealDetailStructuredDetailVO> buildOvernightModule(OvernightRule overnightRule) {
        if (Objects.isNull(overnightRule)) {
            return null;
        }
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();
        DealDetailStructuredDetailVO overnightModuleTitle = buildOvernightModuleTitle();
        result.add(overnightModuleTitle);
        DealDetailStructuredDetailVO item = buildOvernightModuleItem(overnightRule);
        result.add(item);
        return result;

    }

    private DealDetailStructuredDetailVO buildOvernightModuleItem(OvernightRule overnightRule) {
        return DealDetailStructuredDetailVO.builder()
                .type(ViewComponentTypeEnum.DETAIL_TYPE_5.getType())
                .title(buildServiceTime(overnightRule))
                .content(overnightRule.getStayOverDesc())
                .icon(OVER_NIGHT_MODULE_ICON)
                .build();
    }

    private DealDetailStructuredDetailVO buildOvernightModuleTitle() {
        return DealDetailStructuredDetailVO.builder()
                .title(OVER_NIGHT_MODULE_NAME)
                .type(ViewComponentTypeEnum.DETAIL_TYPE_1.getType())
                .detail("免费")
                .build();
    }

    private String buildServiceTime(OvernightRule overnightRule) {
        String serviceTime = formatServiceTime(overnightRule);
        String serviceTimeAndBreakfast = appendBreakfastInfo(serviceTime, overnightRule);
        if (StringUtils.isBlank(serviceTimeAndBreakfast)) {
            return null;
        }
        return "起止时间：" + serviceTimeAndBreakfast;
    }

    private String formatServiceTime(OvernightRule overnightRule) {
        try {
            String startTime = TimeUtils.formatOvernightTime(overnightRule.getStayOverStartTime());
            String endTime = TimeUtils.formatOvernightTime(overnightRule.getStayOverEndTime());
            return String.format("%s-%s",
                    startTime,
                    endTime);
        } catch (Exception e) {
            log.error("formatServiceTime error, overnightRule={}", overnightRule, e);
            return StringUtils.EMPTY;
        }
    }

    private String appendBreakfastInfo(String serviceTime, OvernightRule overnightRule) {
        return isFreeBreakfast(overnightRule) ? serviceTime + " 含早餐" : serviceTime;
    }

    private boolean isFreeBreakfast(OvernightRule overnightRule) {
        return Objects.equals(overnightRule.getFreeBreakfast(), "含早餐");
    }

    @Data
    private static class OvernightRule {
        /**
         * 过夜规则：23点后可过夜·需预约
         */
        private String stayOverDesc;
        /**
         *
         */
        private String freeBreakfast;
        /**
         * 过夜开始时间：23:00
         */
        private String stayOverStartTime;
        /**
         * 过夜结束时间：07:00
         */
        private String stayOverEndTime;
    }
}
