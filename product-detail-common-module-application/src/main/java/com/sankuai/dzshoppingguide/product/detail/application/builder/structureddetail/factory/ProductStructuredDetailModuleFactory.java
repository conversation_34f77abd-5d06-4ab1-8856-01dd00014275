package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.DefaultStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.FacialDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.MassageDealStructuredDetailBuilder0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.MassageReserveStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.billiards.BilliardsFeesDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder.BathDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder.ChessCardRoomStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.builder.FootMassageDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.gameroom.GameRoomGameCoinDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.gameroom.GameRoomProjectExperienceDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse.TeaHouseCheeseCardsDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse.TeaHouseExperienceProjectDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse.TeaHouseTeaBreakDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse.TeaHouseTicketPackagesDealStructuredDetailBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.enums.MetaVersionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealVersionUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-17
 * @desc 结构化详情模块工厂
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class
        }
)
public class ProductStructuredDetailModuleFactory extends BaseBuilderFactory<ModuleDetailStructuredDetailVO> {

    private ProductBaseInfo baseInfo;
    private ProductCategory productCategory;

    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return MassageReserveStructuredDetailBuilder.class;
        }
        return getDealStructuredDetailModuleBuilder();
    }

    private Class<? extends BaseVariableBuilder> getDealStructuredDetailModuleBuilder() {
        // 足疗
        baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productCategory = getDependencyResult(ProductCategoryFetcher.class);

        MetaVersionEnum metaVersionEnum = DealVersionUtils.isOldMetaVersion(baseInfo, productCategory, LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);
        boolean oldMetaVersion = Objects.equals(MetaVersionEnum.OLD_VERSION,metaVersionEnum);

        int secondCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        int thirdCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);

        if (secondCategoryId == 303) {
            return getMassageBuilder(thirdCategoryId);
        } else if (secondCategoryId == 304) {
            // 洗浴需求
            return getBathBuilder(thirdCategoryId);
        } else if (secondCategoryId == 310 && thirdCategoryId == 700) {
            // 台球需求 台费 TODO billiards fees
            return BilliardsFeesDealStructuredDetailBuilder.class;
        }else if(secondCategoryId == 318 && oldMetaVersion){
            // 茶馆318，且老版本卡控
            return DefaultStructuredDetailBuilder.class;
        } else if(secondCategoryId == 318 && thirdCategoryId == 628) {
            // 茶馆需求 茶歇/包间 628 tea break
            return TeaHouseTeaBreakDealStructuredDetailBuilder.class;
        }else if(secondCategoryId == 318 && thirdCategoryId == 629) {
            // 茶馆需求 棋牌套餐629 chess cards
            return TeaHouseCheeseCardsDealStructuredDetailBuilder.class;
        }else if(secondCategoryId == 318 && thirdCategoryId == 630) {
            // 茶馆需求 门票套餐 630 ticket packages
            return TeaHouseTicketPackagesDealStructuredDetailBuilder.class;
        }else if(secondCategoryId == 318 && thirdCategoryId == 631) {
            // 茶馆需求 体验项目631 experience project
            return TeaHouseExperienceProjectDealStructuredDetailBuilder.class;
        } else if (secondCategoryId == 320 && thirdCategoryId == 123003) {
            // 棋牌室需求 棋牌套餐 123003
            return ChessCardRoomStructuredDetailBuilder.class;
        } else if (secondCategoryId == 322 && thirdCategoryId == 146003) {
            // 游戏厅需求 游戏币 146003
            return GameRoomGameCoinDealStructuredDetailBuilder.class;
        } else if (secondCategoryId == 322 && thirdCategoryId == 155006) {
            // 游戏厅需求 项目体验 155006
            return GameRoomProjectExperienceDealStructuredDetailBuilder.class;
        }
        // 眼镜、眼科、口腔
        if (secondCategoryId == 406 || secondCategoryId == 1604 || secondCategoryId == 506) {
             return FacialDealStructuredDetailBuilder.class;
         }
        return DefaultStructuredDetailBuilder.class;
    }

    // TODO: 版本卡控
    private Class<? extends BaseVariableBuilder> getBathBuilder(int thirdCategoryId) {
        if (BathThirdCategoryEnum.getNewDetailCategoryIds().contains(thirdCategoryId)) {
            return BathDetailBuilder.class;
        }
        return DefaultStructuredDetailBuilder.class;
    }

    private Class<? extends BaseVariableBuilder> getMassageBuilder(int thirdCategoryId) {
        MetaVersionEnum metaVersionEnum = DealVersionUtils.isOldMetaVersion(baseInfo, productCategory, LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);

        if (Objects.equals(MetaVersionEnum.MULTI_SKU_VERSION,metaVersionEnum)) {
            return MassageDealStructuredDetailBuilder0.class;
        }

        if (Objects.equals(MetaVersionEnum.NEW_VERSION,metaVersionEnum) && MassageThirdCategoryEnum.getNewDetailCategoryIds().contains(thirdCategoryId)) {
            return FootMassageDetailBuilder.class;
        }

        return DefaultStructuredDetailBuilder.class;
    }
}
