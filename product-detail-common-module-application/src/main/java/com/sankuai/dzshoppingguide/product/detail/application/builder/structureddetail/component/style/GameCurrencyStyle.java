package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.CommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.ComponentFactory;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.vo.GameRoomVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/6 19:20
 * @Description: 二级类目: 游戏厅  三级类目: 游戏币 146003
 */
@Component
public class GameCurrencyStyle implements Style<GameRoomVO> {

    private static final String SINGLE_CURRENCY_VALUE = "SingleCurrencyValue";
    private static final String TYPE = "GameCurrencyType";
    private static final String COUNT = "GameCurrencyCount";
    private static final String USABLE_DURATION = "GameCurrencyUsableDuration";
    private static final String HAS_GUARANTEED_SERVICE = "hasBitslapService";
    private static final String GUARANTEED_SERVICE_DESCRIPTION = "MandatoryServiceDescription";
    private static final String COIN_EXCHANGE_INSTRUCTIONS = "CoinExchangeInstructions";
    private static final String STORAGE_SERVICE = "GameCurrencyStorageService2";
    private static final String SWALLOW_COIN_PROTECTION = "SwallowCoinSecurity";
    private static final String ID_CARD_EXPLANATION = "IDCardExplanation";

    private final CommonComponent0<GameRoomVO> commonComponent;

    public GameCurrencyStyle(ComponentFactory componentFactory) {
        this.commonComponent = componentFactory.createComponent();
    }

    @Override
    public List<GameRoomVO> build(DealDetailBuildContext context) {
        List<GameRoomVO> gameCurrencyStyle = Lists.newArrayList();
        // 构造标题
        buildTitle(context.getProductServiceProject());
        return gameCurrencyStyle;
    }

    public List<GameRoomVO> buildTitle(ProductServiceProject serviceProject) {
        // TODO: 等排期
        return Lists.newArrayList();
    }
}
