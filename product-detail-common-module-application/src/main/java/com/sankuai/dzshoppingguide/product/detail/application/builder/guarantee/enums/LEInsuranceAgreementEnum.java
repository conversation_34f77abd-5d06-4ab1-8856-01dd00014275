package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums;

import lombok.Getter;

@Getter
public enum LEInsuranceAgreementEnum {

    SAFE_WASHING(21267L, "安心洗", "洗坏/洗丢赔、洗不干净赔、洗超时赔"),

    SAFE_CLEANING(21268L, "安心洁", "财产损失赔"),

    SAFE_REPAIR(21269L, "安心修", "财产损失赔"),

    CLEANING_SELF_OWN_PRODUCT(21345L,"自营", "保洁自营"),

    ;

    private long tagId;
    private String text;
    private String desc;

    LEInsuranceAgreementEnum(long tagId, String text, String desc) {
        this.tagId = tagId;
        this.text = text;
        this.desc = desc;
    }

}
