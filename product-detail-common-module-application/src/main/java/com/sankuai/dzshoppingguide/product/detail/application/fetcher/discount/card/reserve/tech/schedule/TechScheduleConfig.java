package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule;

import com.sankuai.technician.trade.api.schedule.dto.TechScheduleConfigDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TechScheduleConfig extends FetcherReturnValueDTO {
    private List<TechScheduleConfigDTO> techScheduleConfigDTOList;
}
