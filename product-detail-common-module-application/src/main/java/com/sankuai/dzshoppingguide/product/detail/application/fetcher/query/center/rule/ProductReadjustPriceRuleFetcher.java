package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.rule;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.general.product.query.center.client.builder.model.DealRuleBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.DealRuleDTO;
import com.sankuai.general.product.query.center.client.dto.deal.rule.readjustPriceRule.ReadjustPriceRuleDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-09
 * @desc 商品改价规则
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class,
        fillRequestDependencies = {
                ProductCategoryFetcher.class
        }
)
public class ProductReadjustPriceRuleFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductReadjustPriceRule> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder.dealRule(DealRuleBuilder.builder().readjustPriceRule());
    }

    @Override
    protected FetcherResponse<ProductReadjustPriceRule> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Map<Long, List<ReadjustPriceRuleDTO>> readjustPriceRuleMap = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getDeals)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        sku -> SkuIdUtils.getSkuId(this.request.getProductTypeEnum(), sku),
                        sku -> {
                            return Optional.of(sku).map(DealGroupDealDTO::getRule)
                                    .map(DealRuleDTO::getReadjustPriceRules).orElse(new ArrayList<>());

                        },
                        (v1, v2) -> v1
                ));
        return FetcherResponse.succeed(new ProductReadjustPriceRule(readjustPriceRuleMap));
    }
}
