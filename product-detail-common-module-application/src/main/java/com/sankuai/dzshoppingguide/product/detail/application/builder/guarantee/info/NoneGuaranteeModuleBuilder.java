package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 无保障模块构造器
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
            CommonModuleStarter.class
        }
)
public class NoneGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {
    @Override
    public ProductDetailGuaranteeVO doBuild() {
        return null;
    }
}
