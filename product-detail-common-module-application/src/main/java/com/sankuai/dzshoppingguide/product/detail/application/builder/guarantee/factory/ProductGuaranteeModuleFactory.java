package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.factory;

import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.NoneGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal.DefaultDealGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal.MassageDealGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.StandardGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal.SafeExerciseDealGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.deal.SafeLearnDealGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info.reserve.MassageReserveGuaranteeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.GuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;

import java.util.Objects;
import java.util.Set;

import com.sankuai.general.product.query.center.client.dto.AttrDTO;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_ORDER_XCX;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_XCX;

/**
 * <AUTHOR>
 * @date 2025-03-13
 * @desc 商品保障模块构造工厂类
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.GUARANTEE_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class,
                ProductAttrFetcher.class,
                GuaranteeTagFetcher.class
        }
)
public class ProductGuaranteeModuleFactory extends BaseBuilderFactory<ProductDetailGuaranteeVO> {

    private static final Set<ClientTypeEnum> NONE_GUARANTEE_CLIENT_TYPE_SET = Sets.newHashSet(MT_LIVE_XCX, MT_LIVE_ORDER_XCX);


    private ProductCategory productCategory;
    @Override
    protected Class<? extends StandardGuaranteeModuleBuilder> selectVariableBuilder() {
        productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        // 预订目前只实现了足疗行业，后续其他行业接入后再改
        if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return MassageReserveGuaranteeModuleBuilder.class;
        } else {
            return getDealGuaranteeModuleBuilder(productAttr);
        }
    }

    private Class<? extends StandardGuaranteeModuleBuilder> getDealGuaranteeModuleBuilder(ProductAttr productAttr) {
        // 不展示保障条
        if (NONE_GUARANTEE_CLIENT_TYPE_SET.contains(request.getClientTypeEnum()) || isWuYouTongDeal(productAttr)) {
            return NoneGuaranteeModuleBuilder.class;
        }
        // 安心学
        if (hasSafeLearnGuaranteeTag()) {
            return SafeLearnDealGuaranteeModuleBuilder.class;
        }
        if (productCategory.getProductSecondCategoryId() == 303) {
            return MassageDealGuaranteeModuleBuilder.class;
        }
        // 安心练
        if (hasSafeExerciseGuaranteeTag()) {
            return SafeExerciseDealGuaranteeModuleBuilder.class;
        }
        return DefaultDealGuaranteeModuleBuilder.class;
    }

    /**
     * 判断是否为无忧通团单
     */
    private boolean isWuYouTongDeal(ProductAttr productAttr) {
        AttrDTO standardDealGroupKeyAttr = productAttr.safeGetSkuAttr("standardDealGroupKey");
        return DealAttrHelper.isWuYouTongAttribute(standardDealGroupKeyAttr);
    }

    private boolean hasSafeLearnGuaranteeTag() {
        ProductGuaranteeTagInfo productGuaranteeTag = getDependencyResult(GuaranteeTagFetcher.class);
        return Objects.nonNull(productGuaranteeTag) && Objects.nonNull(productGuaranteeTag.getSafeLearnGuaranteeInfo());
    }

    private boolean hasSafeExerciseGuaranteeTag() {
        ProductGuaranteeTagInfo productGuaranteeTag = getDependencyResult(GuaranteeTagFetcher.class);
        return Objects.nonNull(productGuaranteeTag) && Objects.nonNull(productGuaranteeTag.getSafeExerciseGuaranteeInfo());
    }
}
