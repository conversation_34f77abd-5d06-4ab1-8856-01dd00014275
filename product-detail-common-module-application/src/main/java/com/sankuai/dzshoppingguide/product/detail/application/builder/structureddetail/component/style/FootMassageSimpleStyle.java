package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.FootMassageCommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/8 11:32
 * @Description: 不包含服务流程
 */
@Deprecated
@Component
public class FootMassageSimpleStyle implements Style<DealDetailStructuredDetailVO> {

    @Resource
    private FootMassageCommonComponent0<DealDetailStructuredDetailVO> component;

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();

        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        ProductServiceProject serviceProject = Optional.ofNullable(context).map(DealDetailBuildContext::getProductServiceProject).orElse(null);
        ProductCategory productCategory = Optional.ofNullable(context).map(DealDetailBuildContext::getProductCategory).orElse(null);

        if (Objects.isNull(productAttr) || Objects.isNull(serviceProject)) {
            return Collections.emptyList();
        }

        // ServiceProjectDTO firstMustSkuModel = FootMassageUtils.getFirstMustSkuModel(serviceProject);
        // if (Objects.isNull(firstMustSkuModel)) {
        //     return Collections.emptyList();
        // }
        component.buildTitleForFootMassage(productCategory, productAttr).ifPresent(result::add);
        component.buildServicePosition(productCategory, productAttr).ifPresent(result::add);

        return result;
    }

    @Override
    public boolean needNewLine() {
        return true;
    }
}
