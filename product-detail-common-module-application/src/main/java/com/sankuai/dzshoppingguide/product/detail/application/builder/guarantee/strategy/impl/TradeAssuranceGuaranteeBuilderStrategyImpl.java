package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import com.dianping.cat.Cat;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.GuaranteeStrategyEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.GuaranteeBuilderStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 交易保障标签构造策略
 */
public class TradeAssuranceGuaranteeBuilderStrategyImpl implements GuaranteeBuilderStrategy {
    @Override
    public GuaranteeStrategyEnum getStrategyEnum() {
        return GuaranteeStrategyEnum.TRADE_ASSURANCE;
    }

    @Override
    public ProductGuaranteeDTO build(GuaranteeParam param) {
        ProductDetailPageRequest pageRequest = param.getPageRequest();
        Map<Long, GuaranteeInstructionsBarLayerVO> tagId2ConfigMap = LionConfigUtils.getTradeAssuranceLayerConfig(pageRequest.getClientTypeEnum().isMtClientType());
        //从商品bp拿标签
        ProductBaseInfo productBaseInfo = param.getProductBaseInfo();
        Map<Long, String> tagMap = Optional.ofNullable(productBaseInfo.getTags()).map(tags ->
                tags.stream().collect(Collectors.toMap(DealGroupTagDTO::getId, DealGroupTagDTO::getTagName))).orElse(new HashMap<>());

//        return tagId2ConfigMap.keySet().stream().filter(tagMap::containsKey).
//                map(tagId -> new GuaranteeInstructionsContentVO(tagMap.get(tagId))).collect(Collectors.toList());
        return null;
    }
}
