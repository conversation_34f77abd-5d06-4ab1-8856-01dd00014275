package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;

import java.util.Optional;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/29 00:38
 * @Description: 新团详服务项目Fetcher
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductStandardServiceProjectFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductStandardServiceProject> {

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .serviceProject(ServiceProjectBuilder.builder().all())//服务项目
        ;
    }

    @Override
    protected FetcherResponse<ProductStandardServiceProject> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        StandardServiceProjectDTO standardServiceProjectDTO = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getStandardServiceProject)
                .orElse(null);
        return FetcherResponse.succeed(new ProductStandardServiceProject(standardServiceProjectDTO));
    }

}
