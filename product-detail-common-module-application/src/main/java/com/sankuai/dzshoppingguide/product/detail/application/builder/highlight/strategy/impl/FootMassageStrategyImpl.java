package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 足疗
 *
 * @author: created by hang.yu on 2023/8/17 11:03
 */
@Component("footMassageStrategyImpl")
public class FootMassageStrategyImpl extends AbstractMassageStrategy {

    private static final String ELECTRIC_BUCKET = "电动按摩洗脚桶";

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 电动按摩洗脚桶 > 热敷工具 > 按摩工具 > 泡脚包 > 木桶
        // 电动按摩洗脚桶
        String footBathBasin = getAttrValue(serviceProjectAttrs, FOOT_BATH_BASIN);
        if (ELECTRIC_BUCKET.equals(footBathBasin)) {
            return footBathBasin;
        }
        // 热敷工具
        String hotpackTool = getAttrValue(serviceProjectAttrs, HOTPACK_TOOL);
        if (StringUtils.isNotBlank(hotpackTool)) {
            return hotpackTool;
        }
        // 按摩工具
        String massageTool = getAttrValue(serviceProjectAttrs, MASSAGE_TOOL);
        if (StringUtils.isNotBlank(massageTool)) {
            return massageTool;
        }
        // 泡脚包
        String footBathBag = getAttrValue(serviceProjectAttrs, FOOT_BATH_BAG);
        if (StringUtils.isNotBlank(footBathBag)) {
            return getFootBathBagValue(footBathBag);
        }
        // 木桶
        return getAttrValue(serviceProjectAttrs, FOOT_BATH_BASIN);
    }

    private String getFootBathBagValue(String footBathBag) {
        footBathBag = footBathBag.replace("牛奶", "牛奶包");
        if (!footBathBag.contains(SPERATOR)) {
            return footBathBag;
        }
        List<String> bags = Arrays.asList(footBathBag.split(SPERATOR));
        if (bags.size() == TWO) {
            return String.format("%s任选", String.join(SPERATOR, bags));
        }
        List<String> showBags = bags.stream().limit(TWO).collect(Collectors.toList());
        return String.format("%s等%d种任选", String.join(SPERATOR, showBags), bags.size());
    }

}
