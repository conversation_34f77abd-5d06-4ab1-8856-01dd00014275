package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.base.Style;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.FootMassageCommonComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.ServiceProcessComponent0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 包含服务部位和服务流程以及标题
 * <AUTHOR>
 * @date 2025/5/6 15:34
 */
@Component
@Slf4j
public class FootMassageStyle implements Style<DealDetailStructuredDetailVO> {

    @Autowired
    private FootMassageCommonComponent0<DealDetailStructuredDetailVO> component;

    @Autowired
    private ServiceProcessComponent0<DealDetailStructuredDetailVO> serviceProcessComponent;

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        List<DealDetailStructuredDetailVO> result = Lists.newArrayList();

        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        ProductCategory productCategory = Optional.ofNullable(context).map(DealDetailBuildContext::getProductCategory).orElse(null);

        if (productAttr == null) {
            return Collections.emptyList();
        }

        // 标题
        component.buildTitleForFootMassage(productCategory,productAttr).ifPresent(result::add);

        // 服务项目
        component.buildServiceItems(productCategory,productAttr).ifPresent(result::add);

        // 服务部位
        component.buildServicePosition(productCategory,productAttr).ifPresent(result::add);

        // 服务流程
        serviceProcessComponent.buildServiceItems(productAttr).ifPresent(result::addAll);

        // 适用人群
        component.buildApplicablePopulation(productCategory, productAttr).ifPresent(result::add);

        // 头疗、精油SPA特有的附加服务 ADDITIONAL_SERVICES
        component.buildAdditionalService(productCategory, productAttr).ifPresent(result::add);

        // 小儿推拿特有的项目功效 PROJECT_EFFICACY
        component.buildProjectEfficacy(productAttr).ifPresent(result::add);

        // 运动复健特有的对应症状 CORRESPONDING_SYMPTOMS
        component.buildCorrespondingSymptoms(productAttr).ifPresent(result::add);

        // 运动复健特有的不适用人群 INAPPLICABLE_POPULATION
        component.buildInapplicablePopulation(productAttr).ifPresent(result::add);
        return result;
    }
}
