package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.base.BOStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.FacilityItemComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.TitleComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.BathCPVConstant.*;

@Component
@Slf4j
public class ServiceFacilityBOStyle implements BOStyle<ComponentBO> {

    @Override
    public boolean needNewLine() {
        return true;
    }

    @Override
    public List<ComponentBO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = Optional.ofNullable(context).map(DealDetailBuildContext::getProductAttr).orElse(null);
        if (Objects.isNull(productAttr)) {
            return null;
        }
        List<ComponentBO> serviceFacilities = Lists.newArrayList();

        // 桑拿汗蒸
        List<ComponentBO> teaAndFruitItems = buildCommonFacility(productAttr, SAUNA, "桑拿汗蒸");
        serviceFacilities.addAll(teaAndFruitItems);

        // 汤池
        List<ComponentBO> footSoakItems = buildCommonFacility(productAttr, BATHING_POOL, "汤池");
        serviceFacilities.addAll(footSoakItems);

        // 玩乐设施
        List<ComponentBO> freeOil = buildCommonFacility(productAttr, LEISURE, "玩乐设施");
        serviceFacilities.addAll(freeOil);

        // 休息区域
        List<ComponentBO> hotCompressItems = buildCommonFacility(productAttr, REST_AREA, "休息区域");
        serviceFacilities.addAll(hotCompressItems);

        // 免费洗浴用品
        List<ComponentBO> specialTools = buildCommonFacility(productAttr, FREE_BATHING_SUPPLIES, "免费洗浴用品");
        serviceFacilities.addAll(specialTools);

        // 一次性卫生用品
        List<ComponentBO> massageItems = buildCommonFacility(productAttr, DISPOSABLE_MATERIALS, "一次性卫生用品");
        serviceFacilities.addAll(massageItems);

        // 洗护用品
        List<ComponentBO> specialMaterial = buildCommonFacility(productAttr, BATHING_SUPPLIES, "洗护用品");
        serviceFacilities.addAll(specialMaterial);

        // 过滤空值
        List<ComponentBO> facilities = serviceFacilities.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(facilities)) {
            facilities.add(0, TitleComponentBO.builder().title("服务设施").build());
        }
        return facilities;
    }

    private List<ComponentBO> buildCommonFacility(ProductAttr productAttr, String attrKey, String title) {
        if (StringUtils.isBlank(attrKey)) {
            return Lists.newArrayList();
        }
        List<String> skuAttrValues = productAttr.getSkuAttrValues(attrKey);
        if (CollectionUtils.isEmpty(skuAttrValues)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isNotEmpty(skuAttrValues)) {
            FacilityItemComponentBO facilityItemComponent = FacilityItemComponentBO.builder().build();
            facilityItemComponent.setItemDesc(skuAttrValues);
            facilityItemComponent.setItemName(title);
            return Lists.newArrayList(facilityItemComponent);
        }
        return Lists.newArrayList();
    }

}
