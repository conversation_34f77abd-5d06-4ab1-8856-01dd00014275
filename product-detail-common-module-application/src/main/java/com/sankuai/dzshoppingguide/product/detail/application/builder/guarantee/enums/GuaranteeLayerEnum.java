package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-14
 * @desc 保障浮层枚举
 */
@AllArgsConstructor
@Getter
public enum GuaranteeLayerEnum {
    NONE("none", "无分类"),
    PRICE_PROTECTION("price_protection", "价保"),
    BEST_PRICE("best_price", "买贵必赔"),
    DEFAULT("default", "随时退·过期退"),
    SAFE_OPTOMETRY("safe_optometry", "安心配镜"),
    ORIGINAL_GUARANTEE("original_guarantee", "正品保障"),
    SAFE_MEDICAL("safe_medical", "安心医"),
    SAFE_LEARN("safe_learn", "安心学"),
    ;

    final String key;
    final String desc;
}
