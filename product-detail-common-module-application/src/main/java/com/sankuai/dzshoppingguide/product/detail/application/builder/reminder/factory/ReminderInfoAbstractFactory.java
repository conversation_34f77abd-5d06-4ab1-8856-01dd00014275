package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.factory;

import com.google.common.collect.Lists;
import com.meituan.nibscp.common.api.enums.TradeTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.MassageThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FreeDealEnum;
import com.sankuai.dzshoppingguide.product.detail.application.enums.MetaVersionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.DealTagQueryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.DealTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealVersionUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class, ProductBaseInfoFetcher.class, DealTagQueryFetcher.class
        }
)
public class ReminderInfoAbstractFactory extends BaseBuilderFactory<ProductDetailReminderVO> {

    private ProductBaseInfo baseInfo;
    private ProductCategory productCategory;

    // 美团美播小程序/提单小程序
    private final List<ClientTypeEnum> mtLiveClients =  Lists.newArrayList(ClientTypeEnum.MT_LIVE_XCX, ClientTypeEnum.MT_LIVE_ORDER_XCX);

    @Override
    protected Class<? extends AbstractReminderInfoBuilder> selectVariableBuilder() {
        int thirdCategoryId = getServiceTypeId();
        int secondCategoryId = getSecondCategoryId();
        baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        productCategory = getDependencyResult(ProductCategoryFetcher.class);

        MetaVersionEnum metaVersionEnum = DealVersionUtils.isOldMetaVersion(baseInfo, productCategory, LionConstants.COMMON_MODULE_CALL_VERSION_CONFIG);
        boolean oldMetaVersion = Objects.equals(MetaVersionEnum.OLD_VERSION,metaVersionEnum);

        if (Objects.equals(request.getProductTypeEnum(), ProductTypeEnum.RESERVE)) {
            return ReserveReminderInfoBuilder.class;
        }

        // 足疗行业,非团购次卡场景
        if (identifyMassageDeal()) {
            return selectMassageReminderBuilder(metaVersionEnum);
        }

        // 足疗行业兜底
        if (oldMetaVersion && secondCategoryId == 318){
            // 茶馆318，老团单卡控
            return BlankReminderInfoBuilder.class;
        }
        if (secondCategoryId == 318 && thirdCategoryId == 629) {
            return ChessAndCardReminderInfoBuilder.class;
        }

        if (identifyBathDeal()) {
            return BathReminderInfoBuilder.class;
        }

        if (identifyMtLiveMiniApp()) {
            return MtLiveMiniAppReminderInfoBuilder.class;
        }

        if (isTagPresent()) {
            return ReassuredRepairReminderInfoBuilder.class;
        }

        if (isFreeVaccine()) {
            return FreeVaccineReminderInfoBuilder.class;
        }

        if (isFreeDeal()) {
            return FreeDealReminderInfoBuilder.class;
        }

        if (isEduOnline()) {
           return EduOnlineReminderInfoBuilder.class;
        }

        if (isVocationalEdu()) {
            return VocationalEduPlanReminderInfoBuilder.class;
        }

        if (isDealTimesCard()) {
            return DealTimesCardReminderInfoBuilder.class;
        }

        return DefaultReminderInfoBuilder.class;
    }

    private Class<? extends AbstractReminderInfoBuilder> selectMassageReminderBuilder(MetaVersionEnum metaVersionEnum) {
        if (Objects.equals(MetaVersionEnum.MULTI_SKU_VERSION,metaVersionEnum)) {
            return MassageReminderInfoBuilder.class;
        }

        if (Objects.equals(MetaVersionEnum.NEW_VERSION,metaVersionEnum) && MassageThirdCategoryEnum.getNewReminderCategoryIds().contains(getServiceTypeId())) {
            return FootMassageReminderBuilder.class;
        }

        return DefaultReminderInfoBuilder.class;
    }


    private boolean identifyMassageDeal() {
        int secondCategoryId = getSecondCategoryId();
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        // 足疗非团购次卡的场景
        return secondCategoryId == 303 && !TimesDealUtil.isDealTimesCard(baseInfo);
    }

    private boolean identifyBathDeal() {
        int secondCategoryId = getSecondCategoryId();
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        return secondCategoryId == 304 && !TimesDealUtil.isDealTimesCard(baseInfo);
    }

    private boolean identifyMtLiveMiniApp() {
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        return mtLiveClients.contains(clientTypeEnum);
    }

    //判断当前团单是否为美团安心改
    private boolean isTagPresent() {
        DealTagQueryResult dealTagQueryResult = getDependencyResult(DealTagQueryFetcher.class);
        return Optional.ofNullable(dealTagQueryResult).map(DealTagQueryResult::getHasTag).orElse(false);
    }

    private boolean isFreeVaccine() {
        int secondCategoryId = getSecondCategoryId();
        if ( secondCategoryId != 1611) {
            return false;
        }
        if (!LionConfigUtils.inCategoryList((long) secondCategoryId)) {
            return false;
        }
        return getTradeType() == TradeTypeEnum.RESERVATION.getCode();
    }

    private boolean isFreeDeal() {
        int categoryId = getSecondCategoryId();
        int tradeType = getTradeType();
        if (!LionConfigUtils.inCategoryList((long) categoryId)) {
            return false;
        }
        if (tradeType != TradeTypeEnum.RESERVATION.getCode()) {
            return false;
        }
        FreeDealEnum freeDealType = FreeDealEnum.fromDealCategory(String.valueOf(categoryId));
        return FreeDealEnum.HOME_DESIGN_BOOKING == freeDealType || FreeDealEnum.LIFE_HOUSEKEEPING_BOOKING == freeDealType || FreeDealEnum.RECYCLE == freeDealType;
    }

    private boolean isEduOnline() {
        int serviceTypeId = getServiceTypeId();
        if (serviceTypeId <= 0) {
            return false;
        }
        return LionConfigUtils.getEduOnlineDealServiceLeafIds().contains((long)serviceTypeId);
    }

    /**
     * 0元规划
     */
    public static final List<Long> SERVICE_ZERO_PLAN_TYPE_ID_LIST = Lists.newArrayList(123034L);
    public boolean isVocationalEdu() {
        int serviceTypeId = getServiceTypeId();
        return SERVICE_ZERO_PLAN_TYPE_ID_LIST.contains((long)serviceTypeId);
    }

    public boolean isDealTimesCard() {
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        return TimesDealUtil.isDealTimesCard(baseInfo);
    }


    private int getTradeType() {
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        return Optional.ofNullable(baseInfo).map(ProductBaseInfo::getBasic).map(DealGroupBasicDTO::getTradeType).orElse(0);
    }

    private int getSecondCategoryId() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
    }

    private int getServiceTypeId() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
    }
}
