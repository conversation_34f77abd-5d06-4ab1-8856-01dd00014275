package com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-31
 * @desc 图文详情内容类型
 */
@Getter
public enum ImageTextContentTypeEnum {
    TEXT(0, "文本"),
    IMAGE(1, "图片"),
    ;

    final int type;
    final String desc;

    ImageTextContentTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
