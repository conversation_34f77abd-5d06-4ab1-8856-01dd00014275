package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 平台商品id映射
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class PlatformProductIdMapper extends FetcherReturnValueDTO {
    /**
     * 平台商品id
     */
    private long platformProductId;
}
