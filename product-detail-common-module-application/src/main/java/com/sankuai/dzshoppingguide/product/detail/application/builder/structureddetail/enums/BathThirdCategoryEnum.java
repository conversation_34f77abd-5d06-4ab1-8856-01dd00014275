package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/23 10:31
 * @Description: 洗浴三级类目枚举
 */
@Getter
@AllArgsConstructor
public enum BathThirdCategoryEnum {
    BATH_TICKET(181, "浴资票"),
    IN_STORE_SERVICE(691, "店内服务"),
    BATH_TICKET_AND_IN_STORE_SERVICE(692, "浴资票和店内服务"),
    DEFAULT(Integer.MAX_VALUE, "其他");

    /**
     * 三级类目ID
     */
    private final int categoryId;

    /**
     * 三级类目名称
     */
    private final String categoryName;

    /**
     * 根据类目ID获取枚举值
     *
     * @param categoryId 三级类目ID
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static BathThirdCategoryEnum getByCategoryId(int categoryId) {
        for (BathThirdCategoryEnum value : values()) {
            if (value.getCategoryId() == categoryId) {
                return value;
            }
        }
        return DEFAULT;
    }

    /**
     * 洗浴行业需要走新版团购详情的三级类目id集合
     *
     * @return
     */
    public static List<Integer> getNewDetailCategoryIds() {
        return Lists.newArrayList(
                BATH_TICKET.getCategoryId(),
                IN_STORE_SERVICE.getCategoryId(),
                BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()
        );
    }

}
