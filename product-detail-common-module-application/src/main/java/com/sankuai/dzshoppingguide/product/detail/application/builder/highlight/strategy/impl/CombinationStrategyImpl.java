package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组合套餐
 *
 * @author: created by hang.yu on 2023/8/17 11:04
 */
@Component("combinationStrategyImpl")
public class CombinationStrategyImpl extends AbstractMassageStrategy {
    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 无
        return null;
    }
}
