package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 刮痧
 *
 * @author: created by hang.yu on 2023/8/17 11:03
 */
@Component("scrapingStrategyImpl")
public class ScrapingStrategyImpl extends AbstractMassageStrategy {

    @Override
    public String getToolValue(List<ServiceProjectAttrDTO> serviceProjectAttrs) {
        // 刮痧工具 > 刮痧材料
        // 刮痧工具
        String scrapingTool = getAttrValue(serviceProjectAttrs, SCRAPING_TOOL);
        if (StringUtils.isNotBlank(scrapingTool)) {
            return scrapingTool;
        }
        // 刮痧材料
        return getAttrValue(serviceProjectAttrs, SCRAPING_MATERIAL);
    }
}
