package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNoteFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PurchaseNotesConvertUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.REMINDER_INFO,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductPurchaseNoteFetcher.class
        }
)
@Slf4j
public class ReserveReminderInfoBuilder extends AbstractReminderInfoBuilder {

    @Override
    public ProductDetailReminderVO preBuild() {
        // 预订须知条构建逻辑
        List<String> reminderInfo = new ArrayList<>();
        ProductPurchaseNote productPurchaseNote = getDependencyResult(ProductPurchaseNoteFetcher.class);
        PurchaseNotesConvertUtils.buildReminderInfo(productPurchaseNote,
                PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_INFORMATION, reminderInfo);
        // 构建跳转浮层
        GuaranteeInstructionsBarLayerVO layer = ReminderInfoUtils.buildLayer(2, "",
                ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS);
        // 构建展示模块
        return ReminderInfoUtils.buildModel("须知", reminderInfo, layer, fromOldDetail(request));
    }

    private boolean fromOldDetail(ProductDetailPageRequest request){
        return "olddetail".equals(request.getCustomParam().getCustomParams().get("productdetail"));
    }
}
