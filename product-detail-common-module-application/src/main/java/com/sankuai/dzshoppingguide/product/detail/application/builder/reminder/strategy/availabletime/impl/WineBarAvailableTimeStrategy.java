package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.enums.FetcherResultEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.*;

@Service
public class WineBarAvailableTimeStrategy implements AvailableTimeStrategy {
    @Override
    public String getAvailableTime(FetcherResultDTO fetcherResults) {

        ProductAttr attr = fetcherResults.getProductAttr();
        List<AttrDTO> attrDTOS = Optional.ofNullable(attr).map(ProductAttr::getSkuAttrList).orElse(Lists.newArrayList());

        List<String> wineBarAvailableTime = DealAttrHelper.getAttributeValues(attrDTOS, DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        return getTimeOfDayFormAvailableTime(wineBarAvailableTime);
    }

    private String getTimeOfDayFormAvailableTime(List<String> availableTimeOfDays) {
        if (CollectionUtils.isEmpty(availableTimeOfDays)) {
            return ALL_DAY;
        }
        List<String[]> startEndTimes = new ArrayList<>();
        for (String availableTimes : availableTimeOfDays) {
            String timeStr = getTimeOfDayFromStr(availableTimes);
            if (StringUtils.isBlank(timeStr)) {
                continue;
            }
            if (!checkTimeOfDayFormat(timeStr)) {
                return PARTIAL_TIME;
            }
            startEndTimes.add(timeStr.split("-"));
        }
        if (CollectionUtils.isEmpty(startEndTimes)) {
            return ALL_DAY;
        }
        return getTimeDoc(getFinalStartEndTime(startEndTimes));
    }

    @Override
    public AvailableTimeStrategyEnum getStrategyType() {
        return AvailableTimeStrategyEnum.WIN_BAR_STRATEGY;
    }
}