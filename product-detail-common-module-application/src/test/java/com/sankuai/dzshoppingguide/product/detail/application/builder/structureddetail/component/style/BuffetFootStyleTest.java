package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style;

import com.dianping.lion.common.util.JsonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.RestaurantTimeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.utils.massage.FootMassageUtils;
import org.junit.Test;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/14 14:50
 */
public class BuffetFootStyleTest {

    @Test
    public void test() {
        List<String> decode = JsonCodec.decode(JsonData, List.class);
        List<RestaurantTimeDTO> serviceFlowParseModels = decode.stream().filter(Objects::nonNull).map(item -> JsonUtils.fromJson(item, new TypeReference<RestaurantTimeDTO>() {
        })).collect(Collectors.toList());
        // serviceFlowParseModels = [RestaurantTime(startTime=00:00, endTime=25:30, timeFrame=1.0)]
        System.out.println("serviceFlowParseModels = " + serviceFlowParseModels);

        List<RestaurantTimeDTO> restaurantTimes = FootMassageUtils.parseProcess(decode, RestaurantTimeDTO.class);
        // restaurantTimes = [RestaurantTime(startTime=null, endTime=null, timeFrame=null)]
        System.out.println("restaurantTimes = " + restaurantTimes);


        List<RestaurantTimeDTO> serviceFlowParseModels1 = decode.stream().filter(Objects::nonNull).map(item -> JsonUtils.fromJson(item, RestaurantTimeDTO.class)).collect(Collectors.toList());
        // serviceFlowParseModels = [RestaurantTime(startTime=00:00, endTime=25:30, timeFrame=1.0)]
        System.out.println("serviceFlowParseModels1 = " + serviceFlowParseModels1);

        assert !serviceFlowParseModels1.isEmpty();
    }

    private static final String JsonData = "[\"{\\\"selfservicerestaurantclosingtime\\\":\\\"25:30\\\",\\\"selfservicerestaurantstarttime\\\":\\\"00:00\\\",\\\"timeframe1\\\":1.0}\"]";

}