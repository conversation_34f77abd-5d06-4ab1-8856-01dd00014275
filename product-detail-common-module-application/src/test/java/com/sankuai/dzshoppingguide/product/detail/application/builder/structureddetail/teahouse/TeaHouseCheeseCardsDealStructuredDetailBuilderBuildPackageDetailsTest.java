package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class TeaHouseCheeseCardsDealStructuredDetailBuilderBuildPackageDetailsTest {

    private TeaHouseCheeseCardsDealStructuredDetailBuilder builder = new TeaHouseCheeseCardsDealStructuredDetailBuilder();

    /**
     * Test when input ProductAttr is null
     */
    @Test
    public void testBuildPackageDetailsNullInput() throws Throwable {
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(null);
        // assert
        assertNull(result);
    }

    /**
     * Test when all attributes are missing
     */
    @Test
    public void testBuildPackageDetailsAllAttributesMissing() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("rooms")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("ApplicableDuration")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("TimeoutInformation2")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("supportService")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("Teaservice")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("AdditionalServices")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * Test when only space type attribute is present
     */
    @Test
    public void testBuildPackageDetailsOnlySpaceTypePresent() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("rooms")).thenReturn("VIP Room");
        when(productAttr.getSkuAttrFirstValue("ApplicableDuration")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("supportService")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("Teaservice")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("AdditionalServices")).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("空间类型", vo.getTitle());
        assertEquals("VIP Room", vo.getContent());
        assertNull(vo.getSubContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    /**
     * Test with real ProductAttr object (not mocked) for integration-style testing
     */
    @Test
    public void testBuildPackageDetailsWithRealProductAttr() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        // Create rooms attribute
        AttrDTO roomsAttr = new AttrDTO();
        roomsAttr.setName("rooms");
        roomsAttr.setValue(Collections.singletonList("Standard Room"));
        attrMap.put("rooms", roomsAttr);
        // Create tea service attribute
        AttrDTO teaServiceAttr = new AttrDTO();
        teaServiceAttr.setName("Teaservice");
        teaServiceAttr.setValue(Collections.singletonList("Basic Tea"));
        attrMap.put("Teaservice", teaServiceAttr);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(vo -> "空间类型".equals(vo.getTitle())));
        assertTrue(result.stream().anyMatch(vo -> "茶水服务".equals(vo.getTitle())));
    }
}
