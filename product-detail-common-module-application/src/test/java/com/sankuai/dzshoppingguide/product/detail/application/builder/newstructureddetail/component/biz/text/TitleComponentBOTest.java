package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType1ViewComponent;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TitleComponentBOTest {

    @Mock
    private TitleComponentBO titleComponentBO;

    /**
     * 测试构建器异常场景
     */
    @Test
    public void testDoBuildVOWhenBuilderThrowsException() throws Throwable {
        // arrange
        String expectedTitle = "Exception Title";
        doCallRealMethod().when(titleComponentBO).doBuildVO(anyInt());
        // Mock FacilityType1ViewComponent to throw exception
        when(titleComponentBO.doBuildVO(anyInt())).thenThrow(new RuntimeException("Builder failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> titleComponentBO.doBuildVO(1));
        verify(titleComponentBO).doBuildVO(1);
    }
}
