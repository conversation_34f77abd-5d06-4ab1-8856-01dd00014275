// package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.anyString;
// import static org.mockito.Mockito.*;
// import com.alibaba.fastjson.JSONObject;
// import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
// import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.ServiceProcessDTO;
// import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import org.apache.commons.collections4.CollectionUtils;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.MockitoAnnotations;
// import org.mockito.junit.*;
//
// class MassageReserveStructuredDetailBuilderBuildServiceProcessTest {
//
// @Mock
// private ProductDetailPageRequest request;
//
// private MassageReserveStructuredDetailBuilder builder;
//
// @BeforeEach
// void setUp() {
// MockitoAnnotations.initMocks(this);
// builder = new MassageReserveStructuredDetailBuilder();
// // Initialize builder using public method
// builder.extraInitBuilder(request, new HashMap<>(), null);
// when(request.getProductId()).thenReturn(123L);
// }
//
// /**
//  * Test when input serviceProcessStr is null
//  */
// @Test
// public void testBuildServiceProcessWithNullInput() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// // act
// builder.buildServiceProcess(null, dealDetails);
// // assert
// assertTrue(dealDetails.isEmpty());
// }
//
// /**
//  * Test when input serviceProcessStr is blank
//  */
// @Test
// public void testBuildServiceProcessWithBlankInput() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// // act
// builder.buildServiceProcess("   ", dealDetails);
// // assert
// assertTrue(dealDetails.isEmpty());
// }
//
// /**
//  * Test with empty JSON array
//  */
// @Test
// public void testBuildServiceProcessWithEmptyArray() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// String emptyArrayJson = "[]";
// // act
// builder.buildServiceProcess(emptyArrayJson, dealDetails);
// // assert
// assertTrue(dealDetails.isEmpty());
// }
//
// /**
//  * Test with single valid service process item
//  */
// @Test
// public void testBuildServiceProcessWithSingleItem() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// String json = "[{\"no\":1,\"bodyName\":\"头部按摩\",\"serviceProduct\":[\"精油\"],\"duration\":30}]";
// // act
// builder.buildServiceProcess(json, dealDetails);
// // assert
// assertEquals(1, dealDetails.size());
// DealDetailStructuredDetailVO vo = dealDetails.get(0);
// assertEquals("服务流程", vo.getTitle());
// assertEquals("头部按摩", vo.getContent());
// assertEquals("精油", vo.getSubContent());
// assertEquals("30分钟", vo.getDetail());
// assertEquals(1, vo.getOrder());
// assertEquals(3, vo.getType());
// }
//
// /**
//  * Test with multiple items to verify sorting
//  */
// @Test
// public void testBuildServiceProcessWithMultipleItems() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// String json = "[{\"no\":2,\"bodyName\":\"肩部按摩\",\"serviceProduct\":[\"精油\"],\"duration\":20}," + "{\"no\":1,\"bodyName\":\"头部按摩\",\"serviceProduct\":[\"精油\"],\"duration\":30}]";
// // act
// builder.buildServiceProcess(json, dealDetails);
// // assert
// assertEquals(2, dealDetails.size());
// // Verify sorting by 'no' field
// assertEquals(1, dealDetails.get(0).getOrder());
// assertEquals(2, dealDetails.get(1).getOrder());
// // Only first item should have title
// assertEquals("服务流程", dealDetails.get(0).getTitle());
// assertNull(dealDetails.get(1).getTitle());
// }
//
// /**
//  * Test with item missing duration field
//  */
// @Test
// public void testBuildServiceProcessWithMissingDuration() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// String json = "[{\"no\":1,\"bodyName\":\"头部按摩\",\"serviceProduct\":[\"精油\"]}]";
// // act
// builder.buildServiceProcess(json, dealDetails);
// // assert
// assertEquals(1, dealDetails.size());
// DealDetailStructuredDetailVO vo = dealDetails.get(0);
// assertNull(vo.getDetail());
// assertEquals("头部按摩", vo.getContent());
// assertEquals("精油", vo.getSubContent());
// }
//
// /**
//  * Test with multiple service products
//  */
// @Test
// public void testBuildServiceProcessWithMultipleServiceProducts() throws Throwable {
// // arrange
// List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
// String json = "[{\"no\":1,\"bodyName\":\"全身按摩\",\"serviceProduct\":[\"精油\",\"热石\",\"艾草\"],\"duration\":60}]";
// // act
// builder.buildServiceProcess(json, dealDetails);
// // assert
// assertEquals(1, dealDetails.size());
// DealDetailStructuredDetailVO vo = dealDetails.get(0);
// assertEquals("精油、热石、艾草", vo.getSubContent());
// assertEquals("60分钟", vo.getDetail());
// }
// }
