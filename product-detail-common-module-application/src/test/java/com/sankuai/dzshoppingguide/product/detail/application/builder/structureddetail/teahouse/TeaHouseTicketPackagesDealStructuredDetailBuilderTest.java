package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TeaHouseTicketPackagesDealStructuredDetailBuilderTest {

    @Mock
    private ProductAttr productAttr;

    private TeaHouseTicketPackagesDealStructuredDetailBuilder builder;

    /**
     * 测试buildPackageDetailsTitle方法返回的对象标题和类型是否正确
     */
    @Test
    public void testBuildPackageDetailsTitle_ReturnsCorrectTitleAndType() {
        // arrange
        TeaHouseTicketPackagesDealStructuredDetailBuilder builder = new TeaHouseTicketPackagesDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertAll(() -> assertNotNull(result, "返回结果不应为null"), () -> assertEquals("套餐信息", result.getTitle(), "标题应为'套餐信息'"), () -> assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应为DETAIL_TYPE_11"));
    }

    /**
     * 测试buildPackageDetailsTitle方法在productAttr为null时的行为
     */
    @Test
    public void testBuildPackageDetailsTitle_WithNullProductAttr_ReturnsValidObject() {
        // arrange
        TeaHouseTicketPackagesDealStructuredDetailBuilder builder = new TeaHouseTicketPackagesDealStructuredDetailBuilder();
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(null);
        // assert
        assertAll(() -> assertNotNull(result, "返回结果不应为null"), () -> assertEquals("套餐信息", result.getTitle(), "标题应为'套餐信息'"), () -> assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应为DETAIL_TYPE_11"));
    }

    @BeforeEach
    void setUp() {
        builder = new TeaHouseTicketPackagesDealStructuredDetailBuilder();
        // Set default return values for all possible method calls
        lenient().when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        lenient().when(productAttr.getSkuAttrValues(anyString())).thenReturn(null);
    }

    @Test
    void testBuildPackageDetails_WhenProductAttrIsNull_ShouldReturnNull() throws Throwable {
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(null);
        // assert
        assertNull(result);
    }

    @Test
    void testBuildPackageDetails_WhenNoAttributes_ShouldReturnEmptyList() throws Throwable {
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildPackageDetails_WithProgramType_ShouldReturnCorrectVO() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("ProgramType")).thenReturn("相声表演");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("节目类型", vo.getTitle());
        assertEquals("相声表演", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
        assertNull(vo.getSubContent());
    }

    @Test
    void testBuildPackageDetails_WithFixedPersonCount_ShouldReturnFormattedString() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("指定人数");
        when(productAttr.getSkuAttrFirstValue("fixed_people_amount_gate")).thenReturn("4");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("使用人数", vo.getTitle());
        assertEquals("4人", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    @Test
    void testBuildPackageDetails_WithPersonRange_ShouldReturnRangeString() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("人数范围");
        when(productAttr.getSkuAttrFirstValue("minimum_capacity")).thenReturn("2");
        when(productAttr.getSkuAttrFirstValue("maximum_capacity")).thenReturn("6");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("使用人数", vo.getTitle());
        assertEquals("2-6人", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    @Test
    void testBuildPackageDetails_WithSeatType_ShouldReturnCorrectVO() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("seatType")).thenReturn("VIP座位");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("座位类型", vo.getTitle());
        assertEquals("VIP座位", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    @Test
    void testBuildPackageDetails_WithMultipleFreeFood_ShouldReturnJoinedString() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrValues("freeFood")).thenReturn(Arrays.asList("茶水", "点心", "水果"));
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("免费餐食", vo.getTitle());
        assertEquals("茶水、点心、水果", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    @Test
    void testBuildPackageDetails_WithAdditionalServices_ShouldReturnCorrectVO() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("免费停车");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO vo = result.get(0);
        assertEquals("附加服务", vo.getTitle());
        assertEquals("免费停车", vo.getContent());
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_15.getType(), vo.getType());
    }

    @Test
    void testBuildPackageDetails_WithIncompletePersonRange_ShouldNotAddToResult() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("人数范围");
        when(productAttr.getSkuAttrFirstValue("minimum_capacity")).thenReturn("2");
        // maximum_capacity is null by default setup
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildPackageDetails_WithAllAttributes_ShouldReturnAllItems() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("ProgramType")).thenReturn("京剧表演");
        when(productAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("人数范围");
        when(productAttr.getSkuAttrFirstValue("minimum_capacity")).thenReturn("4");
        when(productAttr.getSkuAttrFirstValue("maximum_capacity")).thenReturn("10");
        when(productAttr.getSkuAttrFirstValue("seatType")).thenReturn("VIP座位");
        when(productAttr.getSkuAttrValues("freeFood")).thenReturn(Arrays.asList("茶水", "水果"));
        when(productAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("专属服务员");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertEquals(5, result.size());
        assertTrue(result.stream().anyMatch(vo -> "节目类型".equals(vo.getTitle()) && "京剧表演".equals(vo.getContent())));
        assertTrue(result.stream().anyMatch(vo -> "使用人数".equals(vo.getTitle()) && "4-10人".equals(vo.getContent())));
        assertTrue(result.stream().anyMatch(vo -> "座位类型".equals(vo.getTitle()) && "VIP座位".equals(vo.getContent())));
        assertTrue(result.stream().anyMatch(vo -> "免费餐食".equals(vo.getTitle()) && "茶水、水果".equals(vo.getContent())));
        assertTrue(result.stream().anyMatch(vo -> "附加服务".equals(vo.getTitle()) && "专属服务员".equals(vo.getContent())));
    }

    @Test
    void testBuildPackageDetails_WithBlankValues_ShouldNotAddToResult() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("ProgramType")).thenReturn("");
        when(productAttr.getSkuAttrFirstValue("seatType")).thenReturn(" ");
        when(productAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn(null);
        when(productAttr.getSkuAttrValues("freeFood")).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildPackageDetails_WithInvalidPersonCountType_ShouldNotAddToResult() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("无效类型");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(productAttr);
        // assert
        assertTrue(result.isEmpty());
    }
}
