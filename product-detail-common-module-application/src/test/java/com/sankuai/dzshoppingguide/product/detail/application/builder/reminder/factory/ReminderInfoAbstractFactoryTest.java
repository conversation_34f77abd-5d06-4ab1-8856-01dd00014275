package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.factory;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ReminderInfoAbstractFactoryTest {

    @Mock
    private ProductBaseInfoFetcher productBaseInfoFetcher;

    @InjectMocks
    private ReminderInfoAbstractFactory factory;

    /**
     * Tests when baseInfo is null
     */
    @Test
    public void testIsDealTimesCardWhenBaseInfoIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = null;
        // act
        boolean result = TimesDealUtil.isDealTimesCard(baseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Tests when baseInfo.basic is null
     */
    @Test
    public void testIsDealTimesCardWhenBasicIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        when(baseInfo.getBasic()).thenReturn(null);
        // act
        boolean result = TimesDealUtil.isDealTimesCard(baseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Tests when tradeType is null
     */
    @Test
    public void testIsDealTimesCardWhenTradeTypeIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupBasicDTO basic = mock(DealGroupBasicDTO.class);
        when(baseInfo.getBasic()).thenReturn(basic);
        when(basic.getTradeType()).thenReturn(null);
        // act
        boolean result = TimesDealUtil.isDealTimesCard(baseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Tests when tradeType is 19 (should return true)
     */
    @Test
    public void testIsDealTimesCardWhenTradeTypeIs19() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupBasicDTO basic = mock(DealGroupBasicDTO.class);
        when(baseInfo.getBasic()).thenReturn(basic);
        when(basic.getTradeType()).thenReturn(19);
        // act
        boolean result = TimesDealUtil.isDealTimesCard(baseInfo);
        // assert
        assertTrue(result);
    }

    /**
     * Tests when tradeType is not 19 (should return false)
     */
    @Test
    public void testIsDealTimesCardWhenTradeTypeIsNot19() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupBasicDTO basic = mock(DealGroupBasicDTO.class);
        when(baseInfo.getBasic()).thenReturn(basic);
        when(basic.getTradeType()).thenReturn(20);
        // act
        boolean result = TimesDealUtil.isDealTimesCard(baseInfo);
        // assert
        assertFalse(result);
    }
}
