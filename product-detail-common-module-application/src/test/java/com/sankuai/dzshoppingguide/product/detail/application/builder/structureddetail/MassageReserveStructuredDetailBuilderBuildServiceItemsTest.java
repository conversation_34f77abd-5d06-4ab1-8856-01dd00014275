package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MassageReserveStructuredDetailBuilderBuildServiceItemsTest {

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ReserveTagQueryResult reserveTagQueryResult;

    private TestMassageReserveStructuredDetailBuilder builder;

    private List<DealDetailStructuredDetailVO> dealDetails;

    // Create a test subclass to set protected field
    private static class TestMassageReserveStructuredDetailBuilder extends MassageReserveStructuredDetailBuilder {

        public void setRequest(ProductDetailPageRequest request) {
            this.request = request;
        }
    }

    @BeforeEach
    void setUp() {
        builder = new TestMassageReserveStructuredDetailBuilder();
        dealDetails = new ArrayList<>();
        // Initialize request with a mock
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductId(123L);
        builder.setRequest(request);
    }

    /**
     * Test when productAttr is null, should not add any items to dealDetails
     */
    @Test
    public void testBuildServiceItemsWhenProductAttrIsNull() throws Throwable {
        // act
        builder.buildServiceItems(null, reserveTagQueryResult, dealDetails);
        // assert
        assertTrue(dealDetails.isEmpty());
    }

    /**
     * Test when all attributes are null/empty, should add service title with empty content
     */
    @Test
    public void testBuildServiceItemsWhenAllAttributesAreEmpty() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("bodyPart")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("serviceProcess")).thenReturn(null);
        // act
        builder.buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(2, dealDetails.size());
        assertEquals(1, dealDetails.get(0).getType());
        assertNull(dealDetails.get(0).getContent());
    }

    /**
     * Test when only duration is valid, should add service title with duration
     */
    @Test
    public void testBuildServiceItemsWhenOnlyDurationIsValid() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("60");
        when(productAttr.getSkuAttrFirstValue("bodyPart")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("serviceProcess")).thenReturn(null);
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue("按摩");
        when(reserveTagQueryResult.getMetaTagDTO()).thenReturn(metaTagDTO);
        // act
        builder.buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(2, dealDetails.size());
        DealDetailStructuredDetailVO detail = dealDetails.get(0);
        assertEquals(1, detail.getType());
        assertEquals("60分钟", detail.getContent());
        assertEquals("按摩", detail.getTitle());
    }

    /**
     * Test when only bodyPart is valid, should add service title and body part
     */
    @Test
    public void testBuildServiceItemsWhenOnlyBodyPartIsValid() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("bodyPart")).thenReturn("[{\"name\":\"back\"},{\"name\":\"legs\"}]");
        when(productAttr.getSkuAttrFirstValue("serviceProcess")).thenReturn(null);
        // act
        builder.buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(2, dealDetails.size());
        assertEquals(1, dealDetails.get(0).getType());
        assertEquals(2, dealDetails.get(1).getType());
        assertEquals("back、legs", dealDetails.get(1).getContent());
        assertEquals("服务部位", dealDetails.get(1).getTitle());
    }

    /**
     * Test when all attributes are valid, should add all items
     */
    @Test
    public void testBuildServiceItemsWhenAllAttributesAreValid() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("90");
        when(productAttr.getSkuAttrFirstValue("bodyPart")).thenReturn("[{\"name\":\"back\"}]");
        when(productAttr.getSkuAttrFirstValue("serviceProcess")).thenReturn("[{\"no\":1,\"bodyName\":\"Step 1\",\"serviceProduct\":[\"product1\"],\"duration\":10}]");
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue("按摩");
        when(reserveTagQueryResult.getMetaTagDTO()).thenReturn(metaTagDTO);
        // act
        builder.buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(3, dealDetails.size());
        // Service title
        assertEquals(1, dealDetails.get(0).getType());
        assertEquals("90分钟", dealDetails.get(0).getContent());
        assertEquals("按摩", dealDetails.get(0).getTitle());
        // Body part
        assertEquals(2, dealDetails.get(1).getType());
        assertEquals("back", dealDetails.get(1).getContent());
        // Service process
        assertEquals(3, dealDetails.get(2).getType());
        assertEquals("Step 1", dealDetails.get(2).getContent());
    }

    /**
     * Test when serviceProcess is invalid JSON, should skip service process
     */
    @Test
    public void testBuildServiceItemsWhenServiceProcessIsInvalid() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("90");
        when(productAttr.getSkuAttrFirstValue("bodyPart")).thenReturn("[{\"name\":\"back\"}]");
        when(productAttr.getSkuAttrFirstValue("serviceProcess")).thenReturn("invalid json");
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue("按摩");
        when(reserveTagQueryResult.getMetaTagDTO()).thenReturn(metaTagDTO);
        // act
        builder.buildServiceItems(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(2, dealDetails.size());
        // Service title
        assertEquals(1, dealDetails.get(0).getType());
        assertEquals("90分钟", dealDetails.get(0).getContent());
        assertEquals("按摩", dealDetails.get(0).getTitle());
        // Body part
        assertEquals(2, dealDetails.get(1).getType());
        assertEquals("back", dealDetails.get(1).getContent());
    }
}
