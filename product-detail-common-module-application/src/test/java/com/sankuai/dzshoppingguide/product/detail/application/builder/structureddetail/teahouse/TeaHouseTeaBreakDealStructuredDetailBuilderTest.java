package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TeaHouseTeaBreakDealStructuredDetailBuilderTest {

    @Mock
    private ProductAttr mockProductAttr;

    /**
     * 测试buildPackageDetailsTitle方法返回正确的标题和类型
     */
    @Test
    public void testBuildPackageDetailsTitleReturnsCorrectVO() {
        // arrange
        TeaHouseTeaBreakDealStructuredDetailBuilder builder = new TeaHouseTeaBreakDealStructuredDetailBuilder();
        // 虽然方法不依赖productAttr，但为了演示Mock使用，我们模拟一个场景
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertAll(() -> assertNotNull(result, "返回对象不应为null"), () -> assertEquals("套餐信息", result.getTitle(), "标题应为'套餐信息'"), () -> assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应为DETAIL_TYPE_11"));
    }

    /**
     * 测试buildPackageDetailsTitle方法在productAttr为null时的行为
     */
    @Test
    public void testBuildPackageDetailsTitleWithNullParam() {
        // arrange
        TeaHouseTeaBreakDealStructuredDetailBuilder builder = new TeaHouseTeaBreakDealStructuredDetailBuilder();
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(null);
        // assert
        assertAll(() -> assertNotNull(result, "即使参数为null也应返回非null对象"), () -> assertEquals("套餐信息", result.getTitle(), "标题应为'套餐信息'"), () -> assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_11.getType(), result.getType(), "类型应为DETAIL_TYPE_11"));
    }

    /**
     * 测试buildPackageDetailsTitle方法返回的对象builder模式正确
     */
    @Test
    public void testBuildPackageDetailsTitleBuilderPattern() {
        // arrange
        TeaHouseTeaBreakDealStructuredDetailBuilder builder = new TeaHouseTeaBreakDealStructuredDetailBuilder();
        // act
        DealDetailStructuredDetailVO result = builder.buildPackageDetailsTitle(mockProductAttr);
        // assert
        assertEquals(DealDetailStructuredDetailVO.builder().title("套餐信息").type(ViewComponentTypeEnum.DETAIL_TYPE_11.getType()).build(), result, "应使用builder模式创建对象");
    }
}
