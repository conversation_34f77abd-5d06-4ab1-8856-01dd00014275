package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.DetailType11ViewComponent;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TitleWithPrefixDotComponentBOTest {

    /**
     * 测试title为null时的doBuildVO方法
     */
    @Test
    public void testDoBuildVOWithNullTitle() throws Throwable {
        // arrange
        TitleWithPrefixDotComponentBO component = mock(TitleWithPrefixDotComponentBO.class);
        when(component.doBuildVO(anyInt())).thenCallRealMethod();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result, "返回的ComponentVO不应为null");
        assertInstanceOf(BizComponentVO.class, result, "返回的VO类型应为BizComponentVO");
        DetailType11ViewComponent viewComponent = DetailType11ViewComponent.builder().title(null).build();
        assertEquals(new BizComponentVO(viewComponent), result, "返回的BizComponentVO应与预期相符");
        verify(component, times(1)).doBuildVO(1);
    }
}
