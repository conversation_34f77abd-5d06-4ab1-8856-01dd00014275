package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ChessAndCardReminderInfoBuilderTest {

    @Spy
    private TestableBuilder builder;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ProductBaseInfo baseInfo;

    private static class TestableBuilder extends ChessAndCardReminderInfoBuilder {

        private ProductAttr mockProductAttr;

        private ProductBaseInfo mockBaseInfo;

        public void setMockDependencies(ProductAttr productAttr, ProductBaseInfo baseInfo) {
            this.mockProductAttr = productAttr;
            this.mockBaseInfo = baseInfo;
        }

        @Override
        public ProductDetailReminderVO getBaseReminderInfo() {
            return super.getBaseReminderInfo();
        }

        private boolean hasAvailableTimePeriod() {
            return mockProductAttr != null && "部分时间可用".equals(mockProductAttr.getSkuAttrFirstValue("AvailableTimePeriod3"));
        }

        @Override
        public ProductDetailReminderVO preBuild() {
            ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
            if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
                return null;
            }
            if (mockProductAttr == null) {
                return null;
            }
            List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();
            if (hasAvailableTimePeriod()) {
                AvailableTimeHelper.partialTimePeriodReminder(contents, mockProductAttr, mockBaseInfo);
            } else {
                AvailableTimeHelper.allTimePeriodReminder(contents, mockBaseInfo);
            }
            ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(mockBaseInfo)).ifPresent(contents::add);
            return baseReminderInfo;
        }
    }

    @BeforeEach
    void setUp() {
        builder = spy(new TestableBuilder());
    }

    /**
     * Test when baseReminderInfo is null
     */
    @Test
    public void testPreBuildWhenBaseReminderInfoIsNull() throws Throwable {
        // arrange
        doReturn(null).when(builder).getBaseReminderInfo();
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when baseReminderInfo contents are empty
     */
    @Test
    public void testPreBuildWhenContentsAreEmpty() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        mockReminder.setContents(new ArrayList<>());
        doReturn(mockReminder).when(builder).getBaseReminderInfo();
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when productAttr is null
     */
    @Test
    public void testPreBuildWhenProductAttrIsNull() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        contents.add(new GuaranteeInstructionsContentVO("test"));
        mockReminder.setContents(contents);
        doReturn(mockReminder).when(builder).getBaseReminderInfo();
        builder.setMockDependencies(null, baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when hasAvailableTimePeriod is true (partial time)
     */
    @Test
    public void testPreBuildWhenHasPartialTimePeriod() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        contents.add(new GuaranteeInstructionsContentVO("test"));
        mockReminder.setContents(contents);
        doReturn(mockReminder).when(builder).getBaseReminderInfo();
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("部分时间可用");
        builder.setMockDependencies(productAttr, baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        assertTrue(CollectionUtils.isNotEmpty(result.getContents()));
        verify(productAttr).getSkuAttrFirstValue("AvailableTimePeriod3");
    }

    /**
     * Test when hasAvailableTimePeriod is false (all time)
     */
    @Test
    public void testPreBuildWhenHasAllTimePeriod() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        contents.add(new GuaranteeInstructionsContentVO("test"));
        mockReminder.setContents(contents);
        doReturn(mockReminder).when(builder).getBaseReminderInfo();
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("全部时间可用");
        builder.setMockDependencies(productAttr, baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        assertTrue(CollectionUtils.isNotEmpty(result.getContents()));
        verify(productAttr).getSkuAttrFirstValue("AvailableTimePeriod3");
    }

    /**
     * Test effective date is added to contents
     */
    @Test
    public void testPreBuildEffectiveDateIsAdded() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        contents.add(new GuaranteeInstructionsContentVO("test"));
        mockReminder.setContents(contents);
        doReturn(mockReminder).when(builder).getBaseReminderInfo();
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("全部时间可用");
        builder.setMockDependencies(productAttr, baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        // original content + effective date
        assertTrue(result.getContents().size() > 1);
    }
}
