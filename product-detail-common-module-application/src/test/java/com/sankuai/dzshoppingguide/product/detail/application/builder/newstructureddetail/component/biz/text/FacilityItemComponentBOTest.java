package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType2ViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType3ViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component.FacilityType5ViewComponent;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FacilityItemComponentBOTest {

    private static ComponentDefinition mockComponentDefinition;

    private static MockedStatic<ComponentDefinitionStorage> mockedStorage;

    @BeforeAll
    public static void init() {
        // Create mock ComponentDefinition
        mockComponentDefinition = mock(ComponentDefinition.class);
        when(mockComponentDefinition.getComponentGroup()).thenReturn(ComponentGroupEnum.Business);
        when(mockComponentDefinition.getComponentType()).thenReturn(ComponentTypeEnum.Text_Biz);
        // Mock static ComponentDefinitionStorage
        mockedStorage = mockStatic(ComponentDefinitionStorage.class);
        mockedStorage.when(() -> ComponentDefinitionStorage.getComponentMetadata(any(Class.class))).thenReturn(mockComponentDefinition);
    }

    /**
     * Test when itemDesc is not empty
     */
    @Test
    public void testDoBuildVO_WhenItemDescNotEmpty() throws Throwable {
        // arrange
        ArrayList<String> itemDesc = new ArrayList<>();
        itemDesc.add("desc1");
        itemDesc.add("desc2");
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("testName").itemDesc(itemDesc).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when itemDesc is empty but itemDescWithIcon is not empty
     */
    @Test
    public void testDoBuildVO_WhenItemDescEmptyAndItemDescWithIconNotEmpty() throws Throwable {
        // arrange
        ArrayList<Icon> icons = new ArrayList<>();
        icons.add(new Icon("icon1", "text1"));
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("testName").itemDesc(Collections.emptyList()).itemDescWithIcon(icons).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when both itemDesc and itemDescWithIcon are empty
     */
    @Test
    public void testDoBuildVO_WhenBothDescriptionsEmpty() throws Throwable {
        // arrange
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("testName").itemDesc(Collections.emptyList()).itemDescWithIcon(Collections.emptyList()).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when both itemDesc and itemDescWithIcon are null
     */
    @Test
    public void testDoBuildVO_WhenBothDescriptionsNull() throws Throwable {
        // arrange
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("testName").itemDesc(null).itemDescWithIcon(null).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when itemName is null
     */
    @Test
    public void testDoBuildVO_WhenItemNameNull() throws Throwable {
        // arrange
        ArrayList<String> itemDesc = new ArrayList<>();
        itemDesc.add("desc1");
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName(null).itemDesc(itemDesc).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when both descriptions have values (should prioritize itemDesc)
     */
    @Test
    public void testDoBuildVO_WhenBothDescriptionsHaveValues() throws Throwable {
        // arrange
        ArrayList<String> itemDesc = new ArrayList<>();
        itemDesc.add("desc1");
        ArrayList<Icon> icons = new ArrayList<>();
        icons.add(new Icon("icon1", "text1"));
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("testName").itemDesc(itemDesc).itemDescWithIcon(icons).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }

    /**
     * Test when itemName is empty string
     */
    @Test
    public void testDoBuildVO_WhenItemNameEmpty() throws Throwable {
        // arrange
        ArrayList<String> itemDesc = new ArrayList<>();
        itemDesc.add("desc1");
        FacilityItemComponentBO component = FacilityItemComponentBO.builder().itemName("").itemDesc(itemDesc).build();
        // act
        ComponentVO result = component.doBuildVO(1);
        // assert
        assertNotNull(result);
        assertTrue(result instanceof BizComponentVO);
    }
}
