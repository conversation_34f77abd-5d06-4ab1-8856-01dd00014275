package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WineBarAvailableTimeStrategyTest {

    @InjectMocks
    private WineBarAvailableTimeStrategy wineBarAvailableTimeStrategy;

    private static final String ALL_DAY = "全天";

    private static final String PARTIAL_TIME = "部分时段";

    private FetcherResultDTO fetcherResult;

    private ProductAttr productAttr;

    @BeforeEach
    void setUp() {
        fetcherResult = mock(FetcherResultDTO.class);
        productAttr = mock(ProductAttr.class);
        when(fetcherResult.getProductAttr()).thenReturn(productAttr);
    }

    /**
     * Test when ProductAttr is null in FetcherResultDTO
     */
    @Test
    public void testGetAvailableTime_NullProductAttr() throws Throwable {
        // arrange
        when(fetcherResult.getProductAttr()).thenReturn(null);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals(ALL_DAY, result);
    }

    /**
     * Test when skuAttrList is empty
     */
    @Test
    public void testGetAvailableTime_EmptySkuAttrList() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrList()).thenReturn(Lists.newArrayList());
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals(ALL_DAY, result);
    }

    /**
     * Test when wine bar available time attribute doesn't exist
     */
    @Test
    public void testGetAvailableTime_NoWineBarAvailableTimeAttribute() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("other_attr");
        attrDTO.setValue(Collections.singletonList("value"));
        List<AttrDTO> attrList = Collections.singletonList(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals(ALL_DAY, result);
    }

    /**
     * Test when wine bar available time list is empty
     */
    @Test
    public void testGetAvailableTime_EmptyAvailableTimeList() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        attrDTO.setValue(Collections.emptyList());
        List<AttrDTO> attrList = Collections.singletonList(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals(ALL_DAY, result);
    }

    /**
     * Test with single valid time range
     */
    @Test
    public void testGetAvailableTime_SingleValidTimeRange() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        attrDTO.setValue(Collections.singletonList("10:00-22:00"));
        List<AttrDTO> attrList = Collections.singletonList(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals("10:00-22:00", result);
    }

    /**
     * Test with multiple overlapping time ranges
     */
    @Test
    public void testGetAvailableTime_MultipleOverlappingTimeRanges() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        attrDTO.setValue(Arrays.asList("10:00-14:00", "12:00-16:00", "15:00-18:00"));
        List<AttrDTO> attrList = Collections.singletonList(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals("10:00-18:00", result);
    }

    /**
     * Test with multiple non-overlapping time ranges
     */
    @Test
    public void testGetAvailableTime_MultipleNonOverlappingTimeRanges() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.WINE_BAR_AVAILABLE_TIME);
        attrDTO.setValue(Arrays.asList("10:00-12:00", "14:00-16:00"));
        List<AttrDTO> attrList = Collections.singletonList(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = wineBarAvailableTimeStrategy.getAvailableTime(fetcherResult);
        // assert
        assertEquals(PARTIAL_TIME, result);
    }
}
