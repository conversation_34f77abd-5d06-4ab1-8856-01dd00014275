package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import org.apache.commons.lang.StringUtils;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/14 19:38
 */
public class MassageReminderInfoTest {

    private static final String PRICING_METHOD = "PricingMethod";
    private static final String SEGMENT_TYPE = "segmentType";
    private static final String GEAR_START_TIME = "gearStartTime";
    private static final String GEAR_END_TIME = "gearEndTime";
    private static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";
    private static final String WEEKEND_TIME = "weekendTime";

    @Ignore
    @Test
    public void testBuildReminderInfo() {
        Map<Long, Map<String, AttrDTO>> decode = Maps.newHashMap();
        Map<String, AttrDTO> map2 = Maps.newHashMap();
        AttrDTO attr3 = new AttrDTO();
        attr3.setName("gearStartTime");
        attr3.setValue(Lists.newArrayList("8:00"));
        AttrDTO attr4 = new AttrDTO();
        attr4.setName("gearEndTime");
        attr4.setValue(Lists.newArrayList("10:00"));
        map2.put("gearStartTime",attr3);
        map2.put("gearEndTime",attr4);
        decode.put(463647333L,map2);

        Map<String, AttrDTO> map1 = Maps.newHashMap();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("gearStartTime");
        attr1.setValue(Lists.newArrayList("20:00"));
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("gearEndTime");
        attr2.setValue(Lists.newArrayList("22:00"));
        map1.put("gearStartTime",attr1);
        map1.put("gearEndTime",attr2);

        decode.put(463647334L,map1);
        // System.out.println("decode = " + decode);
        SkuAttr skuAttr = new SkuAttr(decode);

        String collect = Lists.newArrayList(463647333L,463647334L).stream().map(dealId -> {
                    String startTime = skuAttr.getSkuAttrFirstValue(dealId, GEAR_START_TIME);
                    String endTime = skuAttr.getSkuAttrFirstValue(dealId, GEAR_END_TIME);
                    if ( StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) ) {
                        return StringUtils.EMPTY;
                    }
                    return startTime + "-" + endTime;
                }).filter(StringUtils::isNotBlank).collect(Collectors.joining(" "));
        System.out.println("collect = " + collect);
    }
    
    private static final String map = "{\"463647334\":{\"gearStartTime\":{\"name\":\"gearStartTime\",\"value\":[\"20:00\"],\"source\":0,\"cnName\":null,\"type\":null},\"gearEndTime\":{\"name\":\"gearEndTime\",\"value\":[\"22:00\"],\"source\":0,\"cnName\":null,\"type\":null},\"SellDifferentGrades\":{\"name\":\"SellDifferentGrades\",\"value\":[\"\\u591c\\u95f4\\u6863\"],\"source\":0,\"cnName\":null,\"type\":null}},\"463647333\":{\"gearStartTime\":{\"name\":\"gearStartTime\",\"value\":[\"08:00\"],\"source\":0,\"cnName\":null,\"type\":null},\"gearEndTime\":{\"name\":\"gearEndTime\",\"value\":[\"10:00\"],\"source\":0,\"cnName\":null,\"type\":null},\"SellDifferentGrades\":{\"name\":\"SellDifferentGrades\",\"value\":[\"\\u767d\\u5929\\u6863\"],\"source\":0,\"cnName\":null,\"type\":null}}}";

}