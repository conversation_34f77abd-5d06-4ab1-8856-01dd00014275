package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.keyinfo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GlassesKeyInfoStrategyTest {

    @InjectMocks
    private GlassesKeyInfoStrategy strategy;

    /**
     * 测试当context为null时应抛出NullPointerException
     */
    @Test
    public void testIsHitWhenContextIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.isHit(context));
    }

    /**
     * 测试当productCategory为null时返回false
     */
    @Test
    public void testIsHitWhenProductCategoryIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = DealDetailBuildContext.builder().productCategory(null).build();
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当productCategory的secondCategoryId匹配眼镜类目时返回true
     */
    @Test
    public void testIsHitWhenCategoryMatchesGlasses() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId(), 0);
        DealDetailBuildContext context = DealDetailBuildContext.builder().productCategory(productCategory).build();
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当productCategory的secondCategoryId不匹配眼镜类目时返回false
     */
    @Test
    public void testIsHitWhenCategoryDoesNotMatchGlasses() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId(), 0);
        DealDetailBuildContext context = DealDetailBuildContext.builder().productCategory(productCategory).build();
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }
}
