package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AvailableTimeStrategyFactoryAfterPropertiesSetTest {

    private AvailableTimeStrategyFactory factory;

    @Mock
    private AvailableTimeStrategy strategy1;

    @Mock
    private AvailableTimeStrategy strategy2;

    private Field strategyListField;

    private Field strategyMapField;

    @BeforeEach
    void setUp() throws Exception {
        factory = new AvailableTimeStrategyFactory();
        // 获取私有字段的访问权限
        strategyListField = AvailableTimeStrategyFactory.class.getDeclaredField("strategyList");
        strategyListField.setAccessible(true);
        strategyMapField = AvailableTimeStrategyFactory.class.getDeclaredField("AVAILABLE_TIME_STRATEGY_MAP");
        strategyMapField.setAccessible(true);
        // 清空静态Map
        ((Map<?, ?>) strategyMapField.get(null)).clear();
    }

    @AfterEach
    void tearDown() throws Exception {
        // 清空静态Map
        ((Map<?, ?>) strategyMapField.get(null)).clear();
    }

    /**
     * 测试正常场景 - 有多个策略的情况
     */
    @Test
    public void testAfterPropertiesSetWithMultipleStrategies() throws Throwable {
        // arrange
        when(strategy1.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        when(strategy2.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.KTV_STRATEGY);
        List<AvailableTimeStrategy> strategies = new ArrayList<>();
        strategies.add(strategy1);
        strategies.add(strategy2);
        strategyListField.set(factory, strategies);
        // act
        factory.afterPropertiesSet();
        // assert
        Map<AvailableTimeStrategyEnum, AvailableTimeStrategy> map = (Map<AvailableTimeStrategyEnum, AvailableTimeStrategy>) strategyMapField.get(null);
        assertEquals(2, map.size());
        assertEquals(strategy1, map.get(AvailableTimeStrategyEnum.DEFAULT_STRATEGY));
        assertEquals(strategy2, map.get(AvailableTimeStrategyEnum.KTV_STRATEGY));
    }

    /**
     * 测试边界场景 - 策略列表为空
     */
    @Test
    public void testAfterPropertiesSetWithEmptyStrategyList() throws Throwable {
        // arrange
        strategyListField.set(factory, Collections.emptyList());
        // act
        factory.afterPropertiesSet();
        // assert
        Map<?, ?> map = (Map<?, ?>) strategyMapField.get(null);
        assertTrue(map.isEmpty());
    }

    /**
     * 测试边界场景 - 策略列表为null
     */
    @Test
    public void testAfterPropertiesSetWithNullStrategyList() throws Throwable {
        // arrange
        strategyListField.set(factory, null);
        // act & assert
        assertThrows(NullPointerException.class, () -> factory.afterPropertiesSet());
    }

    /**
     * 测试异常场景 - 策略类型为null
     */
    @Test
    public void testAfterPropertiesSetWithNullStrategyType() throws Throwable {
        // arrange
        when(strategy1.getStrategyType()).thenReturn(null);
        List<AvailableTimeStrategy> strategies = new ArrayList<>();
        strategies.add(strategy1);
        strategyListField.set(factory, strategies);
        // act & assert
        assertThrows(NullPointerException.class, () -> factory.afterPropertiesSet());
    }

    /**
     * 测试异常场景 - 重复的策略类型
     */
    @Test
    public void testAfterPropertiesSetWithDuplicateStrategyTypes() throws Throwable {
        // arrange
        when(strategy1.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        when(strategy2.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        List<AvailableTimeStrategy> strategies = new ArrayList<>();
        strategies.add(strategy1);
        strategies.add(strategy2);
        strategyListField.set(factory, strategies);
        // act
        factory.afterPropertiesSet();
        // assert
        Map<AvailableTimeStrategyEnum, AvailableTimeStrategy> map = (Map<AvailableTimeStrategyEnum, AvailableTimeStrategy>) strategyMapField.get(null);
        assertEquals(1, map.size());
        // 后放入的策略会覆盖之前的策略
        assertEquals(strategy2, map.get(AvailableTimeStrategyEnum.DEFAULT_STRATEGY));
    }
}
