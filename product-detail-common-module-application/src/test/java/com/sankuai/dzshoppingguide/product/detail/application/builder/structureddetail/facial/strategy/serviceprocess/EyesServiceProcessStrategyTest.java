package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.InspectionInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.*;
import org.mockito.Mock;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class EyesServiceProcessStrategyTest {

    @InjectMocks
    private EyesServiceProcessStrategy strategy;

    @Mock
    private InspectionInfoBuilder inspectionInfoBuilder;

    /**
     * 测试当context为null时应返回false
     * 由于原方法会抛出NPE，需要在测试中捕获并验证异常
     */
    @Test
    public void testIsHitWhenContextIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.isHit(context));
    }

    /**
     * 测试当productCategory为null时返回false
     */
    @Test
    public void testIsHitWhenProductCategoryIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        when(context.getProductCategory()).thenReturn(null);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当productCategory不是眼科类别时返回false
     */
    @Test
    public void testIsHitWhenProductCategoryIsNotOphthalmology() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId(), 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当productCategory是眼科类别时返回true
     */
    @Test
    public void testIsHitWhenProductCategoryIsOphthalmology() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId(), 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertTrue(result);
    }

    /**
     * 测试边界情况：当productCategory的categoryId为0时返回false
     */
    @Test
    public void testIsHitWhenProductCategoryIdIsZero() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(0, 0, 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    @Test
    public void testDoBuildWhenProductAttrIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        when(context.getProductAttr()).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testDoBuildWhenProductAttrHasEmptySkuAttrList() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductAttr productAttr = mock(ProductAttr.class);
        when(context.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(result.isEmpty());
    }

}
