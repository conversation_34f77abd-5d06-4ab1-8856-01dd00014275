package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.AvailableTimeStrategyEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AvailableTimeStrategyFactoryTest {

    @Mock
    private AvailableTimeStrategy ktvStrategy;

    @Mock
    private AvailableTimeStrategy winBarStrategy;

    @Mock
    private AvailableTimeStrategy defaultStrategy;

    @InjectMocks
    private AvailableTimeStrategyFactory strategyFactory;

    private void injectStrategyList(AvailableTimeStrategyFactory factory, List<AvailableTimeStrategy> strategies) throws Exception {
        Field strategyListField = AvailableTimeStrategyFactory.class.getDeclaredField("strategyList");
        strategyListField.setAccessible(true);
        strategyListField.set(factory, strategies);
    }

    private void clearStrategyMap(AvailableTimeStrategyFactory factory) throws Exception {
        Field mapField = AvailableTimeStrategyFactory.class.getDeclaredField("AVAILABLE_TIME_STRATEGY_MAP");
        mapField.setAccessible(true);
        Map map = (Map) mapField.get(factory);
        map.clear();
        // 只添加默认策略
        map.put(AvailableTimeStrategyEnum.DEFAULT_STRATEGY, defaultStrategy);
    }

    @BeforeEach
    void setUp() throws Exception {
        when(ktvStrategy.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.KTV_STRATEGY);
        when(winBarStrategy.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.WIN_BAR_STRATEGY);
        when(defaultStrategy.getStrategyType()).thenReturn(AvailableTimeStrategyEnum.DEFAULT_STRATEGY);
        List<AvailableTimeStrategy> strategies = Arrays.asList(ktvStrategy, winBarStrategy, defaultStrategy);
        injectStrategyList(strategyFactory, strategies);
        strategyFactory.afterPropertiesSet();
    }

    /**
     * 测试获取KTV策略
     */
    @Test
    public void testGetStrategyForKTVCategory() throws Throwable {
        // arrange
        int ktvCategoryId = 301;
        // act
        AvailableTimeStrategy result = strategyFactory.getStrategy(ktvCategoryId);
        // assert
        assertSame(ktvStrategy, result);
    }

    /**
     * 测试获取酒吧策略
     */
    @Test
    public void testGetStrategyForWinBarCategory() throws Throwable {
        // arrange
        int winBarCategoryId = 312;
        // act
        AvailableTimeStrategy result = strategyFactory.getStrategy(winBarCategoryId);
        // assert
        assertSame(winBarStrategy, result);
    }

    /**
     * 测试获取默认策略
     */
    @Test
    public void testGetStrategyForDefaultCategory() throws Throwable {
        // arrange
        int defaultCategoryId = Integer.MAX_VALUE;
        // act
        AvailableTimeStrategy result = strategyFactory.getStrategy(defaultCategoryId);
        // assert
        assertSame(defaultStrategy, result);
    }

    /**
     * 测试获取不存在的categoryId时返回默认策略
     */
    @Test
    public void testGetStrategyForNonExistingCategory() throws Throwable {
        // arrange
        int nonExistingCategoryId = 999;
        // act
        AvailableTimeStrategy result = strategyFactory.getStrategy(nonExistingCategoryId);
        // assert
        assertSame(defaultStrategy, result);
    }

    /**
     * 测试当策略MAP未初始化时返回默认策略
     */
    @Test
    public void testGetStrategyWhenMapNotInitialized() throws Throwable {
        // arrange
        int ktvCategoryId = 301;
        AvailableTimeStrategyFactory factory = new AvailableTimeStrategyFactory();
        injectStrategyList(factory, Arrays.asList(defaultStrategy));
        clearStrategyMap(factory);
        // act
        AvailableTimeStrategy result = factory.getStrategy(ktvCategoryId);
        // assert
        assertSame(defaultStrategy, result);
    }
}
