package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.ALL_DAY;
import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.PARTIAL_TIME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DefaultAvailableTimeStrategyTest {

    @InjectMocks
    private DefaultAvailableTimeStrategy strategy;

    @Mock
    private FetcherResultDTO fetcherResults;

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private DealGroupRuleDTO ruleDTO;

    @Mock
    private DealGroupUseRuleDTO useRuleDTO;

    @Mock
    private AvailableDateDTO availableDateDTO;

    @Mock
    private CycleAvailableDateDTO cycleAvailableDateDTO;

    @Mock
    private DateRangeDTO dateRangeDTO;

    /**
     * Test when baseInfo is null
     */
    @Test
    public void testGetAvailableTimeWhenBaseInfoIsNull() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(null);
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when rule is null
     */
    @Test
    public void testGetAvailableTimeWhenRuleIsNull() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(null);
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when useRule is null
     */
    @Test
    public void testGetAvailableTimeWhenUseRuleIsNull() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(null);
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when custom available time config hits and contains "否"
     */
    @Test
    public void testGetAvailableTimeWhenCustomConfigHitsAndContainsNo() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        int categoryId = 123;
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(categoryId);
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("times_available_all");
        attrDTO.setValue(Collections.singletonList("否"));
        when(productAttr.getSkuAttrList()).thenReturn(Collections.singletonList(attrDTO));
        try (MockedStatic<LionConfigUtils> lionConfigUtilsMock = Mockito.mockStatic(LionConfigUtils.class)) {
            lionConfigUtilsMock.when(() -> LionConfigUtils.hitCustomAvailableTimeOfDays(categoryId)).thenReturn(true);
            // act
            String result = strategy.getAvailableTime(fetcherResults);
            // assert
            assertEquals(PARTIAL_TIME, result);
        }
    }

    /**
     * Test when custom available time config hits but doesn't contain "否"
     */
    @Test
    public void testGetAvailableTimeWhenCustomConfigHitsButNotContainsNo() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("2023-01-01 09:00:00");
        when(dateRangeDTO.getTo()).thenReturn("2023-01-01 18:00:00");
        int categoryId = 123;
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(categoryId);
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("times_available_all");
        attrDTO.setValue(Collections.singletonList("是"));
        when(productAttr.getSkuAttrList()).thenReturn(Collections.singletonList(attrDTO));
        try (MockedStatic<LionConfigUtils> lionConfigUtilsMock = Mockito.mockStatic(LionConfigUtils.class)) {
            lionConfigUtilsMock.when(() -> LionConfigUtils.hitCustomAvailableTimeOfDays(categoryId)).thenReturn(true);
            // act
            String result = strategy.getAvailableTime(fetcherResults);
            // assert
            assertEquals(PARTIAL_TIME, result);
        }
    }

    /**
     * Test when custom available time config doesn't hit
     */
    @Test
    public void testGetAvailableTimeWhenCustomConfigDoesNotHit() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("2023-01-01 09:00:00");
        when(dateRangeDTO.getTo()).thenReturn("2023-01-01 18:00:00");
        int categoryId = 123;
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(categoryId);
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("times_available_all");
        attrDTO.setValue(Collections.singletonList("否"));
        when(productAttr.getSkuAttrList()).thenReturn(Collections.singletonList(attrDTO));
        try (MockedStatic<LionConfigUtils> lionConfigUtilsMock = Mockito.mockStatic(LionConfigUtils.class)) {
            lionConfigUtilsMock.when(() -> LionConfigUtils.hitCustomAvailableTimeOfDays(categoryId)).thenReturn(false);
            // act
            String result = strategy.getAvailableTime(fetcherResults);
            // assert
            assertEquals(PARTIAL_TIME, result);
        }
    }

    /**
     * Test when availableDate is null
     */
    @Test
    public void testGetAvailableTimeWhenAvailableDateIsNull() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(null);
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when cycleAvailableDateList is empty
     */
    @Test
    public void testGetAvailableTimeWhenCycleAvailableDateListIsEmpty() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Collections.emptyList());
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when availableTimeRangePerDay is empty
     */
    @Test
    public void testGetAvailableTimeWhenAvailableTimeRangePerDayIsEmpty() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Collections.emptyList());
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when dateRangeDTO is invalid (null or blank from/to)
     */
    @Test
    public void testGetAvailableTimeWhenDateRangeDTOIsInvalid() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("");
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when time range is full day (00:00:00 to 23:59:00)
     */
    @Test
    public void testGetAvailableTimeWhenTimeRangeIsFullDay() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("2023-01-01 00:00:00");
        when(dateRangeDTO.getTo()).thenReturn("2023-01-01 23:59:00");
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals(ALL_DAY, result);
    }

    /**
     * Test when time range is partial day
     */
    @Test
    public void testGetAvailableTimeWhenTimeRangeIsPartialDay() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("2023-01-01 09:00:00");
        when(dateRangeDTO.getTo()).thenReturn("2023-01-01 18:00:00");
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals(PARTIAL_TIME, result);
    }

    /**
     * Test when time format is invalid
     */
    @Test
    public void testGetAvailableTimeWhenTimeFormatIsInvalid() throws Throwable {
        // arrange
        when(fetcherResults.getProductBaseInfo()).thenReturn(productBaseInfo);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getAvailableDate()).thenReturn(availableDateDTO);
        when(availableDateDTO.getCycleAvailableDateList()).thenReturn(Lists.newArrayList(cycleAvailableDateDTO));
        when(cycleAvailableDateDTO.getAvailableTimeRangePerDay()).thenReturn(Lists.newArrayList(dateRangeDTO));
        when(dateRangeDTO.getFrom()).thenReturn("invalid");
        when(dateRangeDTO.getTo()).thenReturn("format");
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }
}
