package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DentalServiceProcessStrategyDoBuildTest {

    @InjectMocks
    private DentalServiceProcessStrategy strategy;

    @Mock
    private DealDetailBuildContext context;

    @Mock
    private ProductAttr productAttr;

    /**
     * Test when productAttr is null should return empty list
     */
    @Test
    public void testDoBuildWhenProductAttrIsNull() throws Throwable {
        // arrange
        when(context.getProductAttr()).thenReturn(null);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * Test when skuAttrList is empty should return empty list
     */
    @Test
    public void testDoBuildWhenSkuAttrListIsEmpty() throws Throwable {
        // arrange
        when(context.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * Test with valid service process data should return structured detail list
     */
    @Test
    public void testDoBuildWithValidServiceProcessData() throws Throwable {
        // arrange
        when(context.getProductAttr()).thenReturn(productAttr);
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("serviceProcess");
        attrDTO.setValue(Lists.newArrayList("{\"processName\":\"检查\",\"duration\":\"10\"}"));
        attrList.add(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        when(productAttr.getSkuAttrValues("serviceProcess")).thenReturn(Lists.newArrayList("{\"processName\":\"检查\",\"duration\":\"10\"}"));
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        // title + process + annotation + popup
        assertEquals(4, result.size());
        assertEquals("服务流程", result.get(0).getTitle());
        assertEquals("检查", result.get(1).getTitle());
        assertEquals("10分钟", result.get(1).getDetail());
    }

    /**
     * Test with invalid duration should still process with 0 duration
     */
    @Test
    public void testDoBuildWithInvalidDuration() throws Throwable {
        // arrange
        when(context.getProductAttr()).thenReturn(productAttr);
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("serviceProcess");
        attrDTO.setValue(Lists.newArrayList("{\"processName\":\"检查\",\"duration\":\"invalid\"}"));
        attrList.add(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        when(productAttr.getSkuAttrValues("serviceProcess")).thenReturn(Lists.newArrayList("{\"processName\":\"检查\",\"duration\":\"invalid\"}"));
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        assertEquals(4, result.size());
        assertEquals("服务流程", result.get(0).getTitle());
        assertEquals("检查", result.get(1).getTitle());
        assertEquals("invalid分钟", result.get(1).getDetail());
    }

    /**
     * Test with parsing exception should return empty list
     */
    @Test
    public void testDoBuildWithParsingException() throws Throwable {
        // arrange
        when(context.getProductAttr()).thenReturn(productAttr);
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("serviceProcess");
        attrDTO.setValue(Lists.newArrayList("invalid json"));
        attrList.add(attrDTO);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        when(productAttr.getSkuAttrValues("serviceProcess")).thenReturn(Lists.newArrayList("invalid json"));
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }
}
