package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test for ProductAttrFetcher.mapResult method
 */
class ProductAttrFetcherTest {

    // Previous test cases remain the same...
    /**
     * Test when DealGroupDTO has valid attrs list
     */
    @Test
    public void testMapResultValidAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("color");
        attr1.setValue(Arrays.asList("red", "blue"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("size");
        attr2.setValue(Arrays.asList("S", "M", "L"));
        attrs.add(attr2);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Map<String, AttrDTO> resultAttrs = result.getReturnValue().getSkuAttr();
        assertEquals(2, resultAttrs.size());
        assertEquals(attr1, resultAttrs.get("color"));
        assertEquals(attr2, resultAttrs.get("size"));
    }

    /**
     * Test when DealGroupDTO has duplicate attr names
     */
    @Test
    public void testMapResultDuplicateAttrNames() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("color");
        attr1.setValue(Arrays.asList("red", "blue"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        // duplicate name
        attr2.setName("color");
        attr2.setValue(Arrays.asList("green", "yellow"));
        attrs.add(attr2);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Map<String, AttrDTO> resultAttrs = result.getReturnValue().getSkuAttr();
        assertEquals(1, resultAttrs.size());
        // Should keep the first one
        assertEquals(attr1, resultAttrs.get("color"));
        assertNotEquals(attr2, resultAttrs.get("color"));
    }

    /**
     * Test when input response is null
     */
    @Test
    public void testMapResultNullResponse() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when response has null return value
     */
    @Test
    public void testMapResultNullReturnValue() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        when(mockResponse.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when return value has null DealGroupDTO
     */
    @Test
    public void testMapResultNullDealGroupDTO() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when DealGroupDTO has null attrs list
     */
    @Test
    public void testMapResultNullAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when DealGroupDTO has empty attrs list
     */
    @Test
    public void testMapResultEmptyAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(new ArrayList<>());
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }
}
