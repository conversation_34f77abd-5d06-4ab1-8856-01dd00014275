package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.BathCPVConstant.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.FacilityItemComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.TitleComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ServiceFacilityBOStyleTest {

    @InjectMocks
    private ServiceFacilityBOStyle serviceFacilityBOStyle;

    @Mock
    private DealDetailBuildContext context;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ComponentDefinition componentDefinition;

    private MockedStatic<ComponentDefinitionStorage> mockedStorage;

    @BeforeEach
    void setUp() {
        // Mock static ComponentDefinitionStorage with specific method signature
        mockedStorage = mockStatic(ComponentDefinitionStorage.class);
        mockedStorage.when(() -> ComponentDefinitionStorage.getComponentMetadata(any(Class.class))).thenReturn(componentDefinition);
    }

    @AfterEach
    void tearDown() {
        if (mockedStorage != null) {
            mockedStorage.close();
        }
    }

    /**
     * Helper method to verify facility item properties
     */
    private void verifyFacilityItem(ComponentBO component, String expectedName, String expectedDesc) {
        assertTrue(component instanceof FacilityItemComponentBO);
        FacilityItemComponentBO item = (FacilityItemComponentBO) component;
        assertEquals(expectedName, item.getItemName());
        assertEquals(Arrays.asList(expectedDesc), item.getItemDesc());
    }


}
