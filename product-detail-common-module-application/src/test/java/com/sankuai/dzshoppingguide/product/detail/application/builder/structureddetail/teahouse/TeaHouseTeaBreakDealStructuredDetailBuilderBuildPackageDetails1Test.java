package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for TeaHouseTeaBreakDealStructuredDetailBuilder
 */
@ExtendWith(MockitoExtension.class)
public class TeaHouseTeaBreakDealStructuredDetailBuilderBuildPackageDetails1Test {

    @Mock
    private ProductAttr mockProductAttr;

    private TeaHouseTeaBreakDealStructuredDetailBuilder builder;

    @BeforeEach
    public void setUp() {
        builder = new TeaHouseTeaBreakDealStructuredDetailBuilder();
        // Set up default mock responses
        mockCommonCalls();
    }

    /**
     * Test case for null ProductAttr input
     */
    @Test
    public void testBuildPackageDetails_WhenProductAttrIsNull() throws Throwable {
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for space type information
     */
    @Test
    public void testBuildPackageDetails_WithSpaceType() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("rooms")).thenReturn("大包间");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("空间类型", detail.getTitle());
        assertEquals("大包间", detail.getContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for duration and timeout information
     */
    @Test
    public void testBuildPackageDetails_WithDuration() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("max_service_limited_time")).thenReturn("2");
        when(mockProductAttr.getSafeString("DurationUnit", "")).thenReturn("小时");
        when(mockProductAttr.getSkuAttrFirstValue("TimeoutInformation2")).thenReturn("超时加收费用");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("使用时长", detail.getTitle());
        assertEquals("2小时", detail.getContent());
        assertEquals("超时加收费用", detail.getSubContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for fixed number of users
     */
    @Test
    public void testBuildPackageDetails_WithFixedUserCount() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("指定人数");
        when(mockProductAttr.getSkuAttrFirstValue("fixed_people_amount_gate")).thenReturn("4");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("适用人数", detail.getTitle());
        assertEquals("4人", detail.getContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for user range
     */
    @Test
    public void testBuildPackageDetails_WithUserRange() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("人数范围");
        when(mockProductAttr.getSkuAttrFirstValue("minimum_capacity")).thenReturn("2");
        when(mockProductAttr.getSkuAttrFirstValue("maximum_capacity")).thenReturn("6");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("适用人数", detail.getTitle());
        assertEquals("2-6人", detail.getContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for free food information
     */
    @Test
    public void testBuildPackageDetails_WithFreeFood() throws Throwable {
        // arrange
        List<String> freeFood = Lists.newArrayList("茶点", "水果");
        when(mockProductAttr.getSkuAttrValues("freeFood")).thenReturn(freeFood);
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("免费餐食", detail.getTitle());
        assertEquals("茶点、水果", detail.getContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for additional services
     */
    @Test
    public void testBuildPackageDetails_WithAdditionalServices() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("免费WiFi");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealDetailStructuredDetailVO detail = result.get(0);
        assertEquals("附加服务", detail.getTitle());
        assertEquals("免费WiFi", detail.getContent());
        assertEquals(15, detail.getType());
    }

    /**
     * Test case for empty user count type
     */
    @Test
    public void testBuildPackageDetails_WithEmptyUserCountType() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for invalid user range (missing maximum capacity)
     */
    @Test
    public void testBuildPackageDetails_WithInvalidUserRange() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("人数范围");
        when(mockProductAttr.getSkuAttrFirstValue("minimum_capacity")).thenReturn("2");
        when(mockProductAttr.getSkuAttrFirstValue("maximum_capacity")).thenReturn("");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty free food list
     */
    @Test
    public void testBuildPackageDetails_WithEmptyFreeFood() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrValues("freeFood")).thenReturn(Lists.newArrayList());
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with all fields present
     */
    @Test
    public void testBuildPackageDetails_WithAllFields() throws Throwable {
        // arrange
        when(mockProductAttr.getSkuAttrFirstValue("rooms")).thenReturn("大包间");
        when(mockProductAttr.getSkuAttrFirstValue("max_service_limited_time")).thenReturn("2");
        when(mockProductAttr.getSafeString("DurationUnit", "")).thenReturn("小时");
        when(mockProductAttr.getSkuAttrFirstValue("TimeoutInformation2")).thenReturn("超时加收费用");
        when(mockProductAttr.getSkuAttrFirstValue("recommend_person_count_type")).thenReturn("指定人数");
        when(mockProductAttr.getSkuAttrFirstValue("fixed_people_amount_gate")).thenReturn("4");
        when(mockProductAttr.getSkuAttrValues("freeFood")).thenReturn(Lists.newArrayList("茶点", "水果"));
        when(mockProductAttr.getSkuAttrFirstValue("px_additional_service")).thenReturn("免费WiFi");
        // act
        List<DealDetailStructuredDetailVO> result = builder.buildPackageDetails(mockProductAttr);
        // assert
        assertNotNull(result);
        assertEquals(5, result.size());
        // Verify each field
        assertEquals("空间类型", result.get(0).getTitle());
        assertEquals("大包间", result.get(0).getContent());
        assertEquals("使用时长", result.get(1).getTitle());
        assertEquals("2小时", result.get(1).getContent());
        assertEquals("超时加收费用", result.get(1).getSubContent());
        assertEquals("适用人数", result.get(2).getTitle());
        assertEquals("4人", result.get(2).getContent());
        assertEquals("免费餐食", result.get(3).getTitle());
        assertEquals("茶点、水果", result.get(3).getContent());
        assertEquals("附加服务", result.get(4).getTitle());
        assertEquals("免费WiFi", result.get(4).getContent());
    }

    /**
     * Helper method to set up common mock calls
     */
    private void mockCommonCalls() {
        // Using lenient() to avoid strict stubbing issues
        lenient().when(mockProductAttr.getSkuAttrFirstValue(anyString())).thenReturn("");
        lenient().when(mockProductAttr.getSafeString(anyString(), anyString())).thenReturn("");
        lenient().when(mockProductAttr.getSkuAttrValues(anyString())).thenReturn(Lists.newArrayList());
    }
}
