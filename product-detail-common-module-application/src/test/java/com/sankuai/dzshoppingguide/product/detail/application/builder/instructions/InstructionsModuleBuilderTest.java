package com.sankuai.dzshoppingguide.product.detail.application.builder.instructions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealCtx;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayValueDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InstructionsModuleBuilderTest {

    /**
     * Test when input itemValues is empty, should return empty list
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenInputEmpty_ThenReturnEmptyList() throws Throwable {
        // arrange
        List<StandardDisplayValueDTO> emptyList = Collections.emptyList();
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(emptyList, ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when input itemValues is null, should return empty list
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenInputNull_ThenReturnEmptyList() throws Throwable {
        // arrange
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(null, ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when single item with valid values, should return single converted item
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenSingleItem_ThenReturnSingleConvertedItem() throws Throwable {
        // arrange
        StandardDisplayValueDTO item = new StandardDisplayValueDTO();
        item.setSentenceSequence(1);
        item.setWordSequence(1);
        item.setValue("Test Value");
        List<StandardDisplayValueDTO> input = Collections.singletonList(item);
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(input, ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Value", result.get(0).getPnValue());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when multiple items with same sentence sequence, should merge values in word sequence order
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenMultipleItemsSameSequence_ThenMergeInOrder() throws Throwable {
        // arrange
        StandardDisplayValueDTO item1 = new StandardDisplayValueDTO();
        item1.setSentenceSequence(1);
        item1.setWordSequence(2);
        item1.setValue("World");
        StandardDisplayValueDTO item2 = new StandardDisplayValueDTO();
        item2.setSentenceSequence(1);
        item2.setWordSequence(1);
        item2.setValue("Hello");
        List<StandardDisplayValueDTO> input = Arrays.asList(item1, item2);
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(input, ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("HelloWorld", result.get(0).getPnValue());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when items with empty or null values, should filter out invalid values
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenItemsWithEmptyValues_ThenFilterEmptyValues() throws Throwable {
        // arrange
        StandardDisplayValueDTO item1 = new StandardDisplayValueDTO();
        item1.setSentenceSequence(1);
        item1.setWordSequence(1);
        item1.setValue("Valid");
        StandardDisplayValueDTO item2 = new StandardDisplayValueDTO();
        item2.setSentenceSequence(1);
        item2.setWordSequence(2);
        item2.setValue("");
        StandardDisplayValueDTO item3 = new StandardDisplayValueDTO();
        item3.setSentenceSequence(1);
        item3.setWordSequence(3);
        item3.setValue("  ");
        List<StandardDisplayValueDTO> input = Arrays.asList(item1, item2, item3);
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(input, ctx);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Valid", result.get(0).getPnValue());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when multiple items with different sentence sequences, should group by sequence
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenMultipleSequences_ThenGroupBySequence() throws Throwable {
        // arrange
        StandardDisplayValueDTO item1 = new StandardDisplayValueDTO();
        item1.setSentenceSequence(1);
        item1.setWordSequence(1);
        item1.setValue("First");
        StandardDisplayValueDTO item2 = new StandardDisplayValueDTO();
        item2.setSentenceSequence(2);
        item2.setWordSequence(1);
        item2.setValue("Second");
        StandardDisplayValueDTO item3 = new StandardDisplayValueDTO();
        item3.setSentenceSequence(1);
        item3.setWordSequence(2);
        item3.setValue("Group");
        List<StandardDisplayValueDTO> input = Arrays.asList(item1, item2, item3);
        DealCtx ctx = mock(DealCtx.class);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(input, ctx);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("FirstGroup", result.get(0).getPnValue());
        assertEquals("Second", result.get(1).getPnValue());
        verifyNoInteractions(ctx);
    }

    /**
     * Test when DealCtx is null, should still process normally
     */
    @Test
    void testAssemblePNItemValueListByPRE_WhenDealCtxNull_ThenProcessNormally() throws Throwable {
        // arrange
        StandardDisplayValueDTO item = new StandardDisplayValueDTO();
        item.setSentenceSequence(1);
        item.setWordSequence(1);
        item.setValue("Test");
        List<StandardDisplayValueDTO> input = Collections.singletonList(item);
        // act
        List<PnStandardDisplayValueDTO> result = InstructionsModuleBuilder.assemblePNItemValueListByPRE(input, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test", result.get(0).getPnValue());
    }
}
