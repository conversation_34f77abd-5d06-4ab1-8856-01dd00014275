package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DentalServiceProcessStrategyTest {

    private final DentalServiceProcessStrategy strategy = new DentalServiceProcessStrategy();

    /**
     * Test when context has null productCategory
     */
    @Test
    public void testIsHit_NullProductCategory() {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * Test when productCategory has matching second category ID (ORAL_DENTAL)
     */
    @Test
    public void testIsHit_MatchingCategoryId() {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(1, SecondCategoryEnum.ORAL_DENTAL.getSecondCategoryId(), 3);
        // act
        when(context.getProductCategory()).thenReturn(productCategory);
        boolean result = strategy.isHit(context);
        // assert
        assertTrue(result);
    }

    /**
     * Test when productCategory has non-matching second category ID
     */
    @Test
    public void testIsHit_NonMatchingCategoryId() {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(1, SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId(), 3);
        // act
        when(context.getProductCategory()).thenReturn(productCategory);
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * Test when productCategory has different matching second category ID
     * (Edge case - another medical category but not ORAL_DENTAL)
     */
    @Test
    public void testIsHit_DifferentMedicalCategory() {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        ProductCategory productCategory = new ProductCategory(1, SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId(), 3);
        // act
        when(context.getProductCategory()).thenReturn(productCategory);
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }
}
