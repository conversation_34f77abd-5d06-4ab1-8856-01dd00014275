package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.rule;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.SCRUB_1;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.SCRUB_2;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum.IN_STORE_SERVICE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.ServiceProjectItemComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.TitleComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

class BathCommonComponent2BuildScrubTest {

    /**
     * 测试当productThirdCategoryId是IN_STORE_SERVICE时，调用buildServiceProject并传入SCRUB_1
     */
    @Test
    public void testBuildScrub_WhenInStoreService_ThenCallBuildServiceProjectWithScrub1() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        List<ComponentBO> expectedComponents = new ArrayList<>();
        expectedComponents.add(mock(ComponentBO.class));
        try (MockedStatic<BathCommonComponent2> mocked = Mockito.mockStatic(BathCommonComponent2.class)) {
            // 让原始方法可以正常调用
            mocked.when(() -> BathCommonComponent2.buildScrub(any(), any())).thenCallRealMethod();
            // mock buildServiceProject方法
            mocked.when(() -> BathCommonComponent2.buildServiceProject(any(ProductStandardServiceProject.class), any(), eq(SCRUB_1))).thenReturn(Optional.of(expectedComponents));
            // act
            Optional<List<ComponentBO>> result = BathCommonComponent2.buildScrub(productCategory, standardServiceProject);
            // assert
            assertTrue(result.isPresent());
            assertEquals(expectedComponents, result.get());
            mocked.verify(() -> BathCommonComponent2.buildServiceProject(eq(standardServiceProject), any(), eq(SCRUB_1)));
        }
    }

    /**
     * 测试当productThirdCategoryId是BATH_TICKET_AND_IN_STORE_SERVICE时，调用buildServiceProject并传入SCRUB_2
     */
    @Test
    public void testBuildScrub_WhenBathTicketAndInStoreService_ThenCallBuildServiceProjectWithScrub2() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        List<ComponentBO> expectedComponents = new ArrayList<>();
        expectedComponents.add(mock(ComponentBO.class));
        try (MockedStatic<BathCommonComponent2> mocked = Mockito.mockStatic(BathCommonComponent2.class)) {
            // 让原始方法可以正常调用
            mocked.when(() -> BathCommonComponent2.buildScrub(any(), any())).thenCallRealMethod();
            // mock buildServiceProject方法
            mocked.when(() -> BathCommonComponent2.buildServiceProject(any(ProductStandardServiceProject.class), any(), eq(SCRUB_2))).thenReturn(Optional.of(expectedComponents));
            // act
            Optional<List<ComponentBO>> result = BathCommonComponent2.buildScrub(productCategory, standardServiceProject);
            // assert
            assertTrue(result.isPresent());
            assertEquals(expectedComponents, result.get());
            mocked.verify(() -> BathCommonComponent2.buildServiceProject(eq(standardServiceProject), any(), eq(SCRUB_2)));
        }
    }

    /**
     * 测试当productThirdCategoryId是其他值时，返回Optional.empty()
     */
    @Test
    public void testBuildScrub_WhenOtherCategoryId_ThenReturnEmpty() throws Throwable {
        // arrange
        // 其他categoryId
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildScrub(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }
}
