package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.dtos.ServiceProcessDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.ReserveTagQueryResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;

@ExtendWith(MockitoExtension.class)
class MassageReserveStructuredDetailBuilderSortServiceProcessListTest {

    @Mock
    private ProductAttr productAttr;

    private MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();

    /**
     * 测试空列表场景，方法应不做任何操作
     */
    @Test
    void testSortServiceProcessList_EmptyList_NoChange() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        List<ServiceProcessDTO> emptyList = Collections.emptyList();
        // act
        builder.sortServiceProcessList(emptyList);
        // assert
        assertEquals(0, emptyList.size());
    }

    /**
     * 测试null列表场景，方法应不做任何操作
     */
    @Test
    void testSortServiceProcessList_NullList_NoException() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        List<ServiceProcessDTO> originalList = null;
        // 使用spy来验证方法调用情况
        MassageReserveStructuredDetailBuilder spyBuilder = Mockito.spy(builder);
        // act
        spyBuilder.sortServiceProcessList(originalList);
        // assert
        // 验证方法被调用一次
        verify(spyBuilder, times(1)).sortServiceProcessList(originalList);
        // 验证不会抛出异常
        try {
            spyBuilder.sortServiceProcessList(originalList);
            assertTrue(true, "处理null列表时不应抛出异常");
        } catch (Exception e) {
            fail("处理null列表时不应抛出异常");
        }
        // 验证列表仍然为null
        assertNull(originalList);
    }

    /**
     * 测试正常排序场景，列表应按no升序排列
     */
    @Test
    void testSortServiceProcessList_NormalList_SortedByNo() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        ServiceProcessDTO dto1 = mock(ServiceProcessDTO.class);
        when(dto1.getNo()).thenReturn(3);
        ServiceProcessDTO dto2 = mock(ServiceProcessDTO.class);
        when(dto2.getNo()).thenReturn(1);
        ServiceProcessDTO dto3 = mock(ServiceProcessDTO.class);
        when(dto3.getNo()).thenReturn(2);
        List<ServiceProcessDTO> list = Arrays.asList(dto1, dto2, dto3);
        // act
        builder.sortServiceProcessList(list);
        // assert
        assertEquals(1, list.get(0).getNo());
        assertEquals(2, list.get(1).getNo());
        assertEquals(3, list.get(2).getNo());
    }

    /**
     * 测试相同no值的场景，应保持原有相对顺序
     */
    @Test
    void testSortServiceProcessList_SameNoValues_KeepOriginalOrder() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        ServiceProcessDTO dto1 = mock(ServiceProcessDTO.class);
        when(dto1.getNo()).thenReturn(1);
        when(dto1.getBodyName()).thenReturn("A");
        ServiceProcessDTO dto2 = mock(ServiceProcessDTO.class);
        when(dto2.getNo()).thenReturn(1);
        when(dto2.getBodyName()).thenReturn("B");
        ServiceProcessDTO dto3 = mock(ServiceProcessDTO.class);
        when(dto3.getNo()).thenReturn(2);
        List<ServiceProcessDTO> list = Arrays.asList(dto1, dto2, dto3);
        // act
        builder.sortServiceProcessList(list);
        // assert
        assertEquals(1, list.get(0).getNo());
        assertEquals("A", list.get(0).getBodyName());
        assertEquals(1, list.get(1).getNo());
        assertEquals("B", list.get(1).getBodyName());
        assertEquals(2, list.get(2).getNo());
    }

    /**
     * 测试列表包含null元素场景，应抛出NullPointerException
     */
    @Test
    void testSortServiceProcessList_ContainsNull_ThrowsNPE() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        ServiceProcessDTO dto1 = mock(ServiceProcessDTO.class);
        List<ServiceProcessDTO> list = Arrays.asList(dto1, null);
        // act & assert
        assertThrows(NullPointerException.class, () -> builder.sortServiceProcessList(list));
    }

    /**
     * 测试元素包含null no值场景，应抛出NullPointerException
     */
    @Test
    void testSortServiceProcessList_ContainsNullNo_ThrowsNPE() throws Throwable {
        // arrange
        MassageReserveStructuredDetailBuilder builder = new MassageReserveStructuredDetailBuilder();
        ServiceProcessDTO dto1 = mock(ServiceProcessDTO.class);
        ServiceProcessDTO dto2 = mock(ServiceProcessDTO.class);
        when(dto2.getNo()).thenReturn(null);
        List<ServiceProcessDTO> list = Arrays.asList(dto1, dto2);
        // act & assert
        assertThrows(NullPointerException.class, () -> builder.sortServiceProcessList(list));
    }

    @Test
    public void testBuildServiceTitle_NormalCase() throws Throwable {
        // arrange
        String serviceType = "足疗按摩";
        String duration = "60";
        String expectedContent = "60分钟";
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue(serviceType);
        ReserveTagQueryResult reserveTagQueryResult = new ReserveTagQueryResult(metaTagDTO);
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(duration);
        List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
        // act
        builder.buildServiceTitle(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(1, dealDetails.size());
        DealDetailStructuredDetailVO result = dealDetails.get(0);
        assertEquals(1, result.getType());
        assertEquals(serviceType, result.getTitle());
        assertEquals(expectedContent, result.getContent());
    }

    @Test
    public void testBuildServiceTitle_NullReserveTagResult() throws Throwable {
        // arrange
        String duration = "60";
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(duration);
        List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
        // act
        builder.buildServiceTitle(productAttr, null, dealDetails);
        // assert
        assertEquals(1, dealDetails.size());
        DealDetailStructuredDetailVO result = dealDetails.get(0);
        assertEquals(1, result.getType());
        assertEquals("", result.getTitle());
        assertEquals("60分钟", result.getContent());
    }

    @Test
    public void testBuildServiceTitle_NullMetaTagDTO() throws Throwable {
        // arrange
        String duration = "60";
        ReserveTagQueryResult reserveTagQueryResult = new ReserveTagQueryResult(null);
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(duration);
        List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
        // act
        builder.buildServiceTitle(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(1, dealDetails.size());
        DealDetailStructuredDetailVO result = dealDetails.get(0);
        assertEquals(1, result.getType());
        assertEquals("", result.getTitle());
        assertEquals("60分钟", result.getContent());
    }

    @Test
    public void testBuildServiceTitle_NullDuration() throws Throwable {
        // arrange
        String serviceType = "足疗按摩";
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue(serviceType);
        ReserveTagQueryResult reserveTagQueryResult = new ReserveTagQueryResult(metaTagDTO);
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn(null);
        List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
        // act
        builder.buildServiceTitle(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(1, dealDetails.size());
        DealDetailStructuredDetailVO result = dealDetails.get(0);
        assertEquals(1, result.getType());
        assertEquals(serviceType, result.getTitle());
        assertNull(result.getContent());
    }

    @Test
    public void testBuildServiceTitle_EmptyDuration() throws Throwable {
        // arrange
        String serviceType = "足疗按摩";
        MetaTagDTO metaTagDTO = new MetaTagDTO();
        metaTagDTO.setValue(serviceType);
        ReserveTagQueryResult reserveTagQueryResult = new ReserveTagQueryResult(metaTagDTO);
        when(productAttr.getSkuAttrFirstValue("duration")).thenReturn("");
        List<DealDetailStructuredDetailVO> dealDetails = new ArrayList<>();
        // act
        builder.buildServiceTitle(productAttr, reserveTagQueryResult, dealDetails);
        // assert
        assertEquals(1, dealDetails.size());
        DealDetailStructuredDetailVO result = dealDetails.get(0);
        assertEquals(1, result.getType());
        assertEquals(serviceType, result.getTitle());
        assertNull(result.getContent());
    }
}
