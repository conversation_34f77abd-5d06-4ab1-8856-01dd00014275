package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.MustServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class KTVAvailableTimeStrategyTest {

    @InjectMocks
    private KTVAvailableTimeStrategy strategy;

    /**
     * Test case for null fetcherResults
     */
    @Test
    public void testGetAvailableTime_NullFetcherResults() {
        // arrange
        FetcherResultDTO fetcherResults = null;
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for null productServiceProject
     */
    @Test
    public void testGetAvailableTime_NullProductServiceProject() {
        // arrange
        FetcherResultDTO fetcherResults = FetcherResultDTO.builder().build();
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for null serviceProject
     */
    @Test
    public void testGetAvailableTime_NullServiceProject() {
        // arrange
        FetcherResultDTO fetcherResults = FetcherResultDTO.builder().productServiceProject(new ProductServiceProject(null)).build();
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for empty mustGroups
     */
    @Test
    public void testGetAvailableTime_EmptyMustGroups() {
        // arrange
        DealGroupServiceProjectDTO serviceProject = new DealGroupServiceProjectDTO();
        serviceProject.setMustGroups(Collections.emptyList());
        FetcherResultDTO fetcherResults = FetcherResultDTO.builder().productServiceProject(new ProductServiceProject(serviceProject)).build();
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for missing start_time attribute
     */
    @Test
    public void testGetAvailableTime_MissingStartTime() {
        // arrange
        ServiceProjectAttrDTO endTimeAttr = new ServiceProjectAttrDTO();
        endTimeAttr.setAttrName("end_time");
        endTimeAttr.setAttrValue("23:59");
        FetcherResultDTO fetcherResults = createFetcherResultWithAttrs(Collections.singletonList(endTimeAttr));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for missing end_time attribute
     */
    @Test
    public void testGetAvailableTime_MissingEndTime() {
        // arrange
        ServiceProjectAttrDTO startTimeAttr = new ServiceProjectAttrDTO();
        startTimeAttr.setAttrName("start_time");
        startTimeAttr.setAttrValue("00:00");
        FetcherResultDTO fetcherResults = createFetcherResultWithAttrs(Collections.singletonList(startTimeAttr));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for all day scenario (00:00-23:59)
     */
    @Test
    public void testGetAvailableTime_AllDay_Case1() {
        // arrange
        ServiceProjectAttrDTO startTimeAttr = new ServiceProjectAttrDTO();
        startTimeAttr.setAttrName("start_time");
        startTimeAttr.setAttrValue("00:00");
        ServiceProjectAttrDTO endTimeAttr = new ServiceProjectAttrDTO();
        endTimeAttr.setAttrName("end_time");
        endTimeAttr.setAttrValue("23:59");
        FetcherResultDTO fetcherResults = createFetcherResultWithAttrs(Arrays.asList(startTimeAttr, endTimeAttr));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result);
    }

    /**
     * Test case for all day scenario (00:00-24:00)
     */
    @Test
    public void testGetAvailableTime_AllDay_Case2() {
        // arrange
        ServiceProjectAttrDTO startTimeAttr = new ServiceProjectAttrDTO();
        startTimeAttr.setAttrName("start_time");
        startTimeAttr.setAttrValue("00:00");
        ServiceProjectAttrDTO endTimeAttr = new ServiceProjectAttrDTO();
        endTimeAttr.setAttrName("end_time");
        endTimeAttr.setAttrValue("24:00");
        FetcherResultDTO fetcherResults = createFetcherResultWithAttrs(Arrays.asList(startTimeAttr, endTimeAttr));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result);
    }

    /**
     * Test case for partial time scenario
     */
    @Test
    public void testGetAvailableTime_PartialTime() {
        // arrange
        ServiceProjectAttrDTO startTimeAttr = new ServiceProjectAttrDTO();
        startTimeAttr.setAttrName("start_time");
        startTimeAttr.setAttrValue("10:00");
        ServiceProjectAttrDTO endTimeAttr = new ServiceProjectAttrDTO();
        endTimeAttr.setAttrName("end_time");
        endTimeAttr.setAttrValue("22:00");
        FetcherResultDTO fetcherResults = createFetcherResultWithAttrs(Arrays.asList(startTimeAttr, endTimeAttr));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.PARTIAL_TIME, result);
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testGetAvailableTime_ExceptionCase() {
        // arrange
        FetcherResultDTO fetcherResults = mock(FetcherResultDTO.class);
        when(fetcherResults.getProductServiceProject()).thenThrow(new RuntimeException("Test exception"));
        // act
        String result = strategy.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    private FetcherResultDTO createFetcherResultWithAttrs(List<ServiceProjectAttrDTO> attrs) {
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setAttrs(attrs);
        MustServiceProjectGroupDTO mustGroup = new MustServiceProjectGroupDTO();
        mustGroup.setGroups(Collections.singletonList(serviceProjectDTO));
        DealGroupServiceProjectDTO dealGroupServiceProject = new DealGroupServiceProjectDTO();
        dealGroupServiceProject.setMustGroups(Collections.singletonList(mustGroup));
        return FetcherResultDTO.builder().productServiceProject(new ProductServiceProject(dealGroupServiceProject)).build();
    }
}
