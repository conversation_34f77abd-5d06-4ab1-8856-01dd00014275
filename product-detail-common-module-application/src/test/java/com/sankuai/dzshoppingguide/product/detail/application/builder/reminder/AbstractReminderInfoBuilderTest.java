package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for AbstractReminderInfoBuilder.doBuild() method
 */
@ExtendWith(MockitoExtension.class)
public class AbstractReminderInfoBuilderTest {

    @Spy
    private AbstractReminderInfoBuilder reminderInfoBuilder = new AbstractReminderInfoBuilder() {

        @Override
        public ProductDetailReminderVO preBuild() {
            return null;
        }

        @Override
        public ProductDetailReminderVO getBaseReminderInfo() {
            return null;
        }
    };

    /**
     * Test doBuild() when preBuild() returns null and afterBuild() returns null
     */
    @Test
    public void testDoBuild_WhenPreBuildAndAfterBuildReturnNull() {
        // arrange
        doReturn(null).when(reminderInfoBuilder).preBuild();
        doReturn(null).when(reminderInfoBuilder).afterBuild(null);
        // act
        ProductDetailReminderVO result = reminderInfoBuilder.doBuild();
        // assert
        assertNull(result);
        verify(reminderInfoBuilder).preBuild();
        verify(reminderInfoBuilder).afterBuild(null);
    }

    /**
     * Test doBuild() when preBuild() returns value but afterBuild() returns null
     */
    @Test
    public void testDoBuild_WhenPreBuildReturnsValueButAfterBuildReturnsNull() {
        // arrange
        ProductDetailReminderVO preBuildResult = new ProductDetailReminderVO();
        doReturn(preBuildResult).when(reminderInfoBuilder).preBuild();
        doReturn(null).when(reminderInfoBuilder).afterBuild(preBuildResult);
        // act
        ProductDetailReminderVO result = reminderInfoBuilder.doBuild();
        // assert
        assertNull(result);
        verify(reminderInfoBuilder).preBuild();
        verify(reminderInfoBuilder).afterBuild(preBuildResult);
    }

    /**
     * Test doBuild() when both preBuild() and afterBuild() return values
     */
    @Test
    public void testDoBuild_WhenBothMethodsReturnValues() {
        // arrange
        ProductDetailReminderVO preBuildResult = new ProductDetailReminderVO();
        ProductDetailReminderVO afterBuildResult = new ProductDetailReminderVO();
        doReturn(preBuildResult).when(reminderInfoBuilder).preBuild();
        doReturn(afterBuildResult).when(reminderInfoBuilder).afterBuild(preBuildResult);
        // act
        ProductDetailReminderVO result = reminderInfoBuilder.doBuild();
        // assert
        assertNotNull(result);
        assertSame(afterBuildResult, result);
        verify(reminderInfoBuilder).preBuild();
        verify(reminderInfoBuilder).afterBuild(preBuildResult);
    }

    /**
     * Test doBuild() when preBuild() throws exception
     */
    @Test
    public void testDoBuild_WhenPreBuildThrowsException() {
        // arrange
        doThrow(new RuntimeException("Test exception")).when(reminderInfoBuilder).preBuild();
        // act & assert
        assertThrows(RuntimeException.class, () -> reminderInfoBuilder.doBuild());
        verify(reminderInfoBuilder).preBuild();
        verify(reminderInfoBuilder, never()).afterBuild(any());
    }

    /**
     * Test doBuild() when afterBuild() throws exception
     */
    @Test
    public void testDoBuild_WhenAfterBuildThrowsException() {
        // arrange
        ProductDetailReminderVO preBuildResult = new ProductDetailReminderVO();
        doReturn(preBuildResult).when(reminderInfoBuilder).preBuild();
        doThrow(new RuntimeException("Test exception")).when(reminderInfoBuilder).afterBuild(any());
        // act & assert
        assertThrows(RuntimeException.class, () -> reminderInfoBuilder.doBuild());
        verify(reminderInfoBuilder).preBuild();
        verify(reminderInfoBuilder).afterBuild(preBuildResult);
    }
}
