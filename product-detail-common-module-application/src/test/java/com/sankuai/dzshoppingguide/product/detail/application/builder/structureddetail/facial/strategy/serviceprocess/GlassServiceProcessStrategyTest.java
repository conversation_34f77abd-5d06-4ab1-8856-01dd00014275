package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class GlassServiceProcessStrategyTest {

    @InjectMocks
    private GlassServiceProcessStrategy strategy;

    private DealDetailBuildContext context;

    @Mock
    private ProductAttr productAttr;

    @BeforeEach
    void setUp() {
        context = mock(DealDetailBuildContext.class);
    }

    /**
     * 测试当context为null时应该抛出NullPointerException
     */
    @Test
    public void testIsHitWhenContextIsNullShouldThrowException() throws Throwable {
        // arrange
        context = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.isHit(context));
    }

    /**
     * 测试当ProductCategory为null时返回false
     */
    @Test
    public void testIsHitWhenProductCategoryIsNull() throws Throwable {
        // arrange
        when(context.getProductCategory()).thenReturn(null);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当secondCategoryId匹配EYE_GLASSES时返回true
     */
    @Test
    public void testIsHitWhenSecondCategoryIdMatches() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.EYE_GLASSES.getSecondCategoryId(), 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当secondCategoryId不匹配EYE_GLASSES时返回false
     */
    @Test
    public void testIsHitWhenSecondCategoryIdNotMatches() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, SecondCategoryEnum.OPHTHALMOLOGY.getSecondCategoryId(), 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当secondCategoryId为0时返回false
     */
    @Test
    public void testIsHitWhenSecondCategoryIdIsZero() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当secondCategoryId为负数时返回false
     */
    @Test
    public void testIsHitWhenSecondCategoryIdIsNegative() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, -1, 0);
        when(context.getProductCategory()).thenReturn(productCategory);
        // act
        boolean result = strategy.isHit(context);
        // assert
        assertFalse(result);
    }

    @Test
    public void testDoBuild_WhenProductAttrIsNull() throws Throwable {
        // arrange
        DealDetailBuildContext context = DealDetailBuildContext.builder().build();
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testDoBuild_WhenSkuAttrListIsEmpty() throws Throwable {
        // arrange
        DealDetailBuildContext context = DealDetailBuildContext.builder().productAttr(productAttr).build();
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        List<DealDetailStructuredDetailVO> result = strategy.doBuild(context);
        // assert
        assertTrue(result.isEmpty());
    }


}
