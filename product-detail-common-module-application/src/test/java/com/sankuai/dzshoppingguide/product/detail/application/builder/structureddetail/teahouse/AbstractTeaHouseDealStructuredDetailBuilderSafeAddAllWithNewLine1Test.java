package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;

@ExtendWith(MockitoExtension.class)
class AbstractTeaHouseDealStructuredDetailBuilderSafeAddAllWithNewLine1Test {

    @Spy
    private TestTeaHouseBuilder builder;

    @Mock
    private DealDetailStructuredDetailVO mockDetailVO;

    private static class TestTeaHouseBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {

        @Override
        public DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
            return null;
        }

        @Override
        public List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
            return null;
        }

        @Override
        public DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type) {
            return super.buildStructuredDetailVO(title, content, subcontent, type);
        }
    }

    /**
     * Test when both result and addition lists are valid, with newLine=true
     * Verifies:
     * 1. Addition list items are added to result
     * 2. New line is added at the end
     * 3. Final list contains correct number of items
     */
    @Test
    public void testSafeAddAllWithNewLine_ValidListsWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        DealDetailStructuredDetailVO item = DealDetailStructuredDetailVO.builder().content("test content").type(1).build();
        addition.add(item);
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertEquals(2, result.size());
        assertEquals(item, result.get(0));
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType(), result.get(1).getType());
    }

    /**
     * Test when addition list is empty but newLine is true
     * Verifies:
     * 1. No items are added to result list
     * 2. No new line is added when result is empty
     */
    @Test
    public void testSafeAddAllWithNewLine_EmptyAdditionWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when result already contains items and newLine is true
     * Verifies:
     * 1. Existing items in result remain unchanged
     * 2. New line is added at the end
     */
    @Test
    public void testSafeAddAllWithNewLine_ExistingResultWithNewLine() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredDetailVO existingItem = DealDetailStructuredDetailVO.builder().content("existing").type(1).build();
        result.add(existingItem);
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        DealDetailStructuredDetailVO newItem = DealDetailStructuredDetailVO.builder().content("new").type(1).build();
        addition.add(newItem);
        // act
        builder.safeAddAllWithNewLine(result, addition, true);
        // assert
        assertEquals(3, result.size());
        assertEquals(existingItem, result.get(0));
        assertEquals(newItem, result.get(1));
        assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_NEW_LINE.getType(), result.get(2).getType());
    }

    /**
     * Test when result is null
     * Verifies:
     * 1. Method handles null result gracefully
     * 2. No exceptions are thrown
     */
    @Test
    public void testSafeAddAllWithNewLine_NullResult() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> addition = new ArrayList<>();
        addition.add(DealDetailStructuredDetailVO.builder().content("test").build());
        // act & assert
        assertDoesNotThrow(() -> builder.safeAddAllWithNewLine(null, addition, true));
    }

    /**
     * Test when addition is null
     * Verifies:
     * 1. Method handles null addition gracefully
     * 2. Result list remains unchanged
     * 3. No new line is added when addition is null
     */
    @Test
    public void testSafeAddAllWithNewLine_NullAddition() throws Throwable {
        // arrange
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        DealDetailStructuredDetailVO existingItem = DealDetailStructuredDetailVO.builder().content("existing").type(1).build();
        result.add(existingItem);
        // act
        // Changed newLine to false
        builder.safeAddAllWithNewLine(result, null, false);
        // assert
        assertEquals(1, result.size());
        assertEquals(existingItem, result.get(0));
    }

    @Test
    public void testDoBuild_WhenBothDependenciesNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        builder.setDependencies(null, null, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenServiceProjectNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        builder.setDependencies(null, mockProductAttr, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenProductAttrNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductServiceProject mockServiceProject = mock(ProductServiceProject.class);
        builder.setDependencies(mockServiceProject, null, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenAllDependenciesExist() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductServiceProject mockServiceProject = mock(ProductServiceProject.class);
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        AdditionInfoResult mockAdditionInfo = mock(AdditionInfoResult.class);
        builder.setDependencies(mockServiceProject, mockProductAttr, mockAdditionInfo);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNotNull(result);
        assertNotNull(result.getDealDetails());
    }

    static class TestTeaHouseDealStructuredDetailBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {

        private ProductServiceProject serviceProject;

        private ProductAttr productAttr;

        private AdditionInfoResult additionInfo;

        public void setDependencies(ProductServiceProject serviceProject, ProductAttr productAttr, AdditionInfoResult additionInfo) {
            this.serviceProject = serviceProject;
            this.productAttr = productAttr;
            this.additionInfo = additionInfo;
        }

        @Override
        protected <T extends FetcherReturnValueDTO> FetcherResponse<T> getDependencyResponse(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            if (dependencyFetcherClass == ProductServiceProjectFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(serviceProject);
            } else if (dependencyFetcherClass == ProductAttrFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(productAttr);
            } else if (dependencyFetcherClass == AdditionInfoFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(additionInfo);
            }
            return null;
        }

        @Override
        protected DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
            return DealDetailStructuredDetailVO.builder().build();
        }

        @Override
        protected List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
            List<DealDetailStructuredDetailVO> details = new ArrayList<>();
            details.add(DealDetailStructuredDetailVO.builder().build());
            return details;
        }

        @Override
        protected DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type) {
            return DealDetailStructuredDetailVO.builder().title(title).content(content).subContent(subcontent).type(type).build();
        }
    }
}
