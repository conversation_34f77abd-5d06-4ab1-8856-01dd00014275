package com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.rule;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.mockito.Mockito.mock;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.function.Executable;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum;
import java.util.Arrays;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.BATH_TICKET_1;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.BATH_TICKET_2;

@ExtendWith(MockitoExtension.class)
class BathCommonComponent2Test {

    @Mock
    private ProductStandardServiceProject standardServiceProject;

    /**
     * 测试当产品分类为店内服务时，应返回SPA_1类型的服务项目
     */
    @Test
    public void testBuildSPAWhenCategoryIsInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildSPA(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        // 这里假设buildServiceProject方法会正确处理SPA_1类型
    }

    /**
     * 测试当产品分类为浴资票和店内服务时，应返回SPA_2类型的服务项目
     */
    @Test
    public void testBuildSPAWhenCategoryIsBathTicketAndInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildSPA(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        // 这里假设buildServiceProject方法会正确处理SPA_2类型
    }

    /**
     * 测试当产品分类为其他类型时，应返回空Optional
     */
    @Test
    public void testBuildSPAWhenCategoryIsOther() throws Throwable {
        // arrange
        // 不存在的分类ID
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildSPA(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试当产品分类为null时，应返回空Optional
     */
    @Test
    public void testBuildSPAWhenCategoryIsNull() throws Throwable {
        // arrange
        ProductCategory productCategory = null;
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildSPA(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试当产品分类为浴资票时，应返回空Optional
     */
    @Test
    public void testBuildSPAWhenCategoryIsBathTicket() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildSPA(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildAccommodationWhenProductCategoryIsNull() {
        // arrange
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildAccommodation(null, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildAccommodationWhenThirdCategoryIsInStoreService() {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildAccommodation(productCategory, standardServiceProject);
        // assert
        // 由于buildServiceProject是静态方法且逻辑复杂，这里只验证了返回结果不为空
        // 实际项目中可能需要进一步验证调用了正确的参数
        assertTrue(result != null);
    }

    @Test
    public void testBuildAccommodationWhenThirdCategoryIsBathTicketAndInStoreService() {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildAccommodation(productCategory, standardServiceProject);
        // assert
        // 由于buildServiceProject是静态方法且逻辑复杂，这里只验证了返回结果不为空
        // 实际项目中可能需要进一步验证调用了正确的参数
        assertTrue(result != null);
    }

    @Test
    public void testBuildAccommodationWhenThirdCategoryIsUnknown() {
        // arrange
        // 未知的categoryId
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildAccommodation(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildMassageWhenCategoryIsInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        when(standardServiceProject.getSameCPVObjectId(anyLong(), any())).thenReturn(Lists.newArrayList());
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildMassage(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildMassageWhenCategoryIsBathTicketAndInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        when(standardServiceProject.getSameCPVObjectId(anyLong(), any())).thenReturn(Lists.newArrayList());
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildMassage(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildMassageWhenCategoryIsOther() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildMassage(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildMassageWhenStandardServiceProjectIsNull() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        // act & assert
        assertThrows(NullPointerException.class, new Executable() {

            @Override
            public void execute() throws Throwable {
                BathCommonComponent2.buildMassage(productCategory, null);
            }
        });
    }

    @Test
    public void testBuildMassageWithValidParametersForInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        when(standardServiceProject.getSameCPVObjectId(anyLong(), any())).thenReturn(Lists.newArrayList());
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildMassage(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildMassageWithValidParametersForBathTicketAndInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        when(standardServiceProject.getSameCPVObjectId(anyLong(), any())).thenReturn(Lists.newArrayList());
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildMassage(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFoodInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
    }

    @Test
    public void testBuildFoodBathTicketAndInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
    }

    @Test
    public void testBuildFoodBathTicket() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET.getCategoryId());
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
    }

    @Test
    public void testBuildFoodNullProductCategory() throws Throwable {
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(null, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildFoodNullStandardServiceProject() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        // act & assert
        assertThrows(NullPointerException.class, () -> BathCommonComponent2.buildFood(productCategory, null));
    }

    @Test
    public void testBuildFoodUnknownCategoryId() throws Throwable {
        // arrange
        // unknown category ID
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildFoodServiceProjectReturnsEmpty() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId());
        StandardServiceProjectDTO standardServiceProjectDTO = mock(StandardServiceProjectDTO.class);
        when(standardServiceProject.getStandardServiceProject()).thenReturn(standardServiceProjectDTO);
        when(standardServiceProject.getSameCPVObjectId(anyLong(), any())).thenReturn(null);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildFood(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildBathTicketWhenProductCategoryIsNull() throws Throwable {
        // arrange
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildBathTicket(null, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildBathTicketWhenCategoryIsBathTicket() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildBathTicket(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        // 这里无法直接验证内部调用了buildServiceProject方法，但可以通过返回结果验证
    }

    @Test
    public void testBuildBathTicketWhenCategoryIsBathTicketAndInStoreService() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 0, BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId());
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildBathTicket(productCategory, standardServiceProject);
        // assert
        assertTrue(result.isPresent());
        // 这里无法直接验证内部调用了buildServiceProject方法，但可以通过返回结果验证
    }

    @Test
    public void testBuildBathTicketWhenCategoryIsUnknown() throws Throwable {
        // arrange
        // 未知分类ID
        ProductCategory productCategory = new ProductCategory(0, 0, 999);
        ProductStandardServiceProject standardServiceProject = mock(ProductStandardServiceProject.class);
        // act
        Optional<List<ComponentBO>> result = BathCommonComponent2.buildBathTicket(productCategory, standardServiceProject);
        // assert
        assertFalse(result.isPresent());
    }
}
