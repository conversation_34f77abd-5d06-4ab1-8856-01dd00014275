package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.serviceprocess;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.InspectionInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EyesServiceProcessStrategyBuildTest {

    @Mock
    private InspectionInfoBuilder inspectionInfoBuilder;

    @Spy
    @InjectMocks
    private EyesServiceProcessStrategy strategy;

    /**
     * Test when doBuild returns empty list, should return empty list
     */
    @Test
    public void testBuildWhenDoBuildReturnsEmpty() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        doReturn(Collections.emptyList()).when(strategy).doBuild(context);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.build(context);
        // assert
        assertTrue(result.isEmpty());
        verify(strategy).doBuild(context);
    }

    /**
     * Test when doBuild returns non-empty list, should add separator and return
     */
    @Test
    public void testBuildWhenDoBuildReturnsNonEmpty() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        DealDetailStructuredDetailVO item = DealDetailStructuredDetailVO.builder().build();
        List<DealDetailStructuredDetailVO> mockResult = new ArrayList<>();
        mockResult.add(item);
        doReturn(mockResult).when(strategy).doBuild(context);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.build(context);
        // assert
        assertEquals(2, result.size());
        assertSame(item, result.get(0));
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.get(1).getType());
        verify(strategy).doBuild(context);
    }

    /**
     * Test when doBuild returns single item, should add separator and return two items
     */
    @Test
    public void testBuildWhenDoBuildReturnsSingleItem() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        DealDetailStructuredDetailVO item = DealDetailStructuredDetailVO.builder().build();
        List<DealDetailStructuredDetailVO> mockResult = new ArrayList<>();
        mockResult.add(item);
        doReturn(mockResult).when(strategy).doBuild(context);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.build(context);
        // assert
        assertEquals(2, result.size());
        assertSame(item, result.get(0));
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.get(1).getType());
    }

    /**
     * Test when doBuild returns multiple items, should add separator and return all items
     */
    @Test
    public void testBuildWhenDoBuildReturnsMultipleItems() throws Throwable {
        // arrange
        DealDetailBuildContext context = mock(DealDetailBuildContext.class);
        DealDetailStructuredDetailVO item1 = DealDetailStructuredDetailVO.builder().build();
        DealDetailStructuredDetailVO item2 = DealDetailStructuredDetailVO.builder().build();
        List<DealDetailStructuredDetailVO> mockResult = new ArrayList<>();
        mockResult.add(item1);
        mockResult.add(item2);
        doReturn(mockResult).when(strategy).doBuild(context);
        // act
        List<DealDetailStructuredDetailVO> result = strategy.build(context);
        // assert
        assertEquals(3, result.size());
        assertSame(item1, result.get(0));
        assertSame(item2, result.get(1));
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.get(2).getType());
    }
}
