package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.teahouse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo.ModuleDetailStructuredDetailVO;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProjectFetcher;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Unit tests for AbstractTeaHouseDealStructuredDetailBuilder.buildStructuredDetailVO method
 */
@ExtendWith(MockitoExtension.class)
class AbstractTeaHouseDealStructuredDetailBuilderTest {

    private AbstractTeaHouseDealStructuredDetailBuilder builder;

    @BeforeEach
    void setUp() {
        builder = new AbstractTeaHouseDealStructuredDetailBuilder() {

            @Override
            public DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
                return null;
            }

            @Override
            public List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
                return null;
            }

            @Override
            public ModuleDetailStructuredDetailVO doBuild() {
                return null;
            }
        };
    }

    /**
     * Test scenario: All parameters are provided with valid values
     * Expected: All fields should be set correctly in the result object
     */
    @Test
    void testBuildStructuredDetailVO_AllParametersProvided() {
        // arrange
        String title = "Test Title";
        String content = "Test Content";
        String subcontent = "Test Subcontent";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertEquals(content, result.getContent(), "Content should match input");
        assertEquals(subcontent, result.getSubContent(), "Subcontent should match input");
    }

    /**
     * Test scenario: Only required type parameter is provided
     * Expected: Optional fields should be null, type should be set
     */
    @Test
    void testBuildStructuredDetailVO_OnlyTypeProvided() {
        // arrange
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(null, null, null, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertNull(result.getTitle(), "Title should be null");
        assertNull(result.getContent(), "Content should be null");
        assertNull(result.getSubContent(), "Subcontent should be null");
    }

    /**
     * Test scenario: Title and type provided, other fields null
     * Expected: Only title and type should be set
     */
    @Test
    void testBuildStructuredDetailVO_TitleAndTypeProvided() {
        // arrange
        String title = "Test Title";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(title, null, null, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertNull(result.getContent(), "Content should be null");
        assertNull(result.getSubContent(), "Subcontent should be null");
    }

    /**
     * Test scenario: Content and type provided, other fields null
     * Expected: Only content and type should be set
     */
    @Test
    void testBuildStructuredDetailVO_ContentAndTypeProvided() {
        // arrange
        String content = "Test Content";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(null, content, null, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertNull(result.getTitle(), "Title should be null");
        assertEquals(content, result.getContent(), "Content should match input");
        assertNull(result.getSubContent(), "Subcontent should be null");
    }

    /**
     * Test scenario: Subcontent and type provided, other fields null
     * Expected: Only subcontent and type should be set
     */
    @Test
    void testBuildStructuredDetailVO_SubcontentAndTypeProvided() {
        // arrange
        String subcontent = "Test Subcontent";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(null, null, subcontent, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertNull(result.getTitle(), "Title should be null");
        assertNull(result.getContent(), "Content should be null");
        assertEquals(subcontent, result.getSubContent(), "Subcontent should match input");
    }

    /**
     * Test scenario: Empty strings provided for all optional parameters
     * Expected: Empty strings should be set in result object
     */
    @Test
    void testBuildStructuredDetailVO_EmptyStrings() {
        // arrange
        String title = "";
        String content = "";
        String subcontent = "";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(type, result.getType(), "Type should match input");
        assertEquals("", result.getTitle(), "Title should be empty string");
        assertEquals("", result.getContent(), "Content should be empty string");
        assertEquals("", result.getSubContent(), "Subcontent should be empty string");
    }

    /**
     * Test scenario: Test with maximum integer value for type
     * Expected: Type should be set to maximum value
     */
    @Test
    void testBuildStructuredDetailVO_MaxTypeValue() {
        // arrange
        int type = Integer.MAX_VALUE;
        // act
        DealDetailStructuredDetailVO result = builder.buildStructuredDetailVO(null, null, null, type);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(Integer.MAX_VALUE, result.getType(), "Type should be set to max value");
    }

    @Test
    public void testDoBuild_WhenBothDependenciesNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        builder.setDependencies(null, null, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenServiceProjectNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        builder.setDependencies(null, mockProductAttr, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenProductAttrNull() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductServiceProject mockServiceProject = mock(ProductServiceProject.class);
        builder.setDependencies(mockServiceProject, null, null);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    @Test
    public void testDoBuild_WhenAllDependenciesExist() throws Throwable {
        // arrange
        TestTeaHouseDealStructuredDetailBuilder builder = new TestTeaHouseDealStructuredDetailBuilder();
        ProductServiceProject mockServiceProject = mock(ProductServiceProject.class);
        ProductAttr mockProductAttr = mock(ProductAttr.class);
        AdditionInfoResult mockAdditionInfo = mock(AdditionInfoResult.class);
        builder.setDependencies(mockServiceProject, mockProductAttr, mockAdditionInfo);
        // act
        ModuleDetailStructuredDetailVO result = builder.doBuild();
        // assert
        assertNotNull(result);
        assertNotNull(result.getDealDetails());
    }

    static class TestTeaHouseDealStructuredDetailBuilder extends AbstractTeaHouseDealStructuredDetailBuilder {

        private ProductServiceProject serviceProject;

        private ProductAttr productAttr;

        private AdditionInfoResult additionInfo;

        public void setDependencies(ProductServiceProject serviceProject, ProductAttr productAttr, AdditionInfoResult additionInfo) {
            this.serviceProject = serviceProject;
            this.productAttr = productAttr;
            this.additionInfo = additionInfo;
        }

        @Override
        protected <T extends FetcherReturnValueDTO> FetcherResponse<T> getDependencyResponse(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            if (dependencyFetcherClass == ProductServiceProjectFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(serviceProject);
            } else if (dependencyFetcherClass == ProductAttrFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(productAttr);
            } else if (dependencyFetcherClass == AdditionInfoFetcher.class) {
                return (FetcherResponse<T>) FetcherResponse.succeed(additionInfo);
            }
            return null;
        }

        @Override
        protected DealDetailStructuredDetailVO buildPackageDetailsTitle(ProductAttr productAttr) {
            return DealDetailStructuredDetailVO.builder().build();
        }

        @Override
        protected List<DealDetailStructuredDetailVO> buildPackageDetails(ProductAttr productAttr) {
            List<DealDetailStructuredDetailVO> details = new ArrayList<>();
            details.add(DealDetailStructuredDetailVO.builder().build());
            return details;
        }

        @Override
        protected DealDetailStructuredDetailVO buildStructuredDetailVO(String title, String content, String subcontent, int type) {
            return DealDetailStructuredDetailVO.builder().title(title).content(content).subContent(subcontent).type(type).build();
        }
    }
}
