package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link FreeVaccineReminderInfoBuilder} using JUnit5 and Mockito
 */
@ExtendWith(MockitoExtension.class)
class FreeVaccineReminderInfoBuilderTest {

    @InjectMocks
    private FreeVaccineReminderInfoBuilder builder;

    /**
     * Test normal case where preBuild() returns a properly configured ProductDetailReminderVO
     */
    @Test
    void testPreBuildNormalCase() {
        // arrange
        try (MockedStatic<ReminderInfoUtils> mockedUtils = mockStatic(ReminderInfoUtils.class)) {
            GuaranteeInstructionsContentVO expectedContent = new GuaranteeInstructionsContentVO();
            expectedContent.setText("线上预约无需支付费用");
            expectedContent.setFontSize("12");
            expectedContent.setFontColor("#555555");
            mockedUtils.when(() -> ReminderInfoUtils.buildReminderInfo(anyString())).thenReturn(Optional.of(expectedContent));
            // act
            ProductDetailReminderVO result = builder.preBuild();
            // assert
            assertNotNull(result);
            assertNotNull(result.getLayer());
            assertEquals(2, result.getLayer().getType());
            assertEquals(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS, result.getLayer().getModulekey());
            assertNotNull(result.getContents());
            assertEquals(1, result.getContents().size());
            assertEquals("线上预约无需支付费用", result.getContents().get(0).getText());
            assertEquals("12", result.getContents().get(0).getFontSize());
            assertEquals("#555555", result.getContents().get(0).getFontColor());
            mockedUtils.verify(() -> ReminderInfoUtils.buildReminderInfo("线上预约无需支付费用"));
        }
    }

    /**
     * Test when ReminderInfoUtils returns empty Optional
     */
    @Test
    void testPreBuildWhenUtilsReturnsEmpty() {
        // arrange
        try (MockedStatic<ReminderInfoUtils> mockedUtils = mockStatic(ReminderInfoUtils.class)) {
            mockedUtils.when(() -> ReminderInfoUtils.buildReminderInfo(anyString())).thenReturn(Optional.empty());
            // act
            ProductDetailReminderVO result = builder.preBuild();
            // assert
            assertNotNull(result);
            assertNotNull(result.getLayer());
            assertEquals(2, result.getLayer().getType());
            assertEquals(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS, result.getLayer().getModulekey());
            assertNotNull(result.getContents());
            assertTrue(result.getContents().isEmpty());
            mockedUtils.verify(() -> ReminderInfoUtils.buildReminderInfo("线上预约无需支付费用"));
        }
    }

    /**
     * Test that the method always returns a new instance each time it's called
     */
    @Test
    void testPreBuildReturnsNewInstanceEachTime() {
        // arrange
        try (MockedStatic<ReminderInfoUtils> mockedUtils = mockStatic(ReminderInfoUtils.class)) {
            GuaranteeInstructionsContentVO expectedContent = new GuaranteeInstructionsContentVO();
            expectedContent.setText("线上预约无需支付费用");
            mockedUtils.when(() -> ReminderInfoUtils.buildReminderInfo(anyString())).thenReturn(Optional.of(expectedContent));
            // act
            ProductDetailReminderVO result1 = builder.preBuild();
            ProductDetailReminderVO result2 = builder.preBuild();
            // assert
            assertNotSame(result1, result2);
            assertEquals(result1.getLayer().getType(), result2.getLayer().getType());
            assertEquals(result1.getLayer().getModulekey(), result2.getLayer().getModulekey());
            assertEquals(result1.getContents().size(), result2.getContents().size());
            mockedUtils.verify(() -> ReminderInfoUtils.buildReminderInfo("线上预约无需支付费用"), times(2));
        }
    }

    /**
     * Test that the layer configuration is correct
     */
    @Test
    void testPreBuildLayerConfiguration() {
        // arrange
        try (MockedStatic<ReminderInfoUtils> mockedUtils = mockStatic(ReminderInfoUtils.class)) {
            mockedUtils.when(() -> ReminderInfoUtils.buildReminderInfo(anyString())).thenReturn(Optional.of(new GuaranteeInstructionsContentVO()));
            // act
            ProductDetailReminderVO result = builder.preBuild();
            GuaranteeInstructionsBarLayerVO layer = result.getLayer();
            // assert
            assertNotNull(layer);
            assertEquals(2, layer.getType());
            assertEquals(ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS, layer.getModulekey());
            assertNull(layer.getTitle());
            assertNull(layer.getIcon());
            assertNull(layer.getJumpUrl());
            mockedUtils.verify(() -> ReminderInfoUtils.buildReminderInfo("线上预约无需支付费用"));
        }
    }
}
