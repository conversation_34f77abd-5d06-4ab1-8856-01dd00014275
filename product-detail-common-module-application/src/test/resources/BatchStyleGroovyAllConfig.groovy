import com.google.common.collect.Lists
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.base.BOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.ServiceProjectItemComponentBO
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.TitleComponentBO
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style.AdditionalInfoBOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style.ServiceFacilityBOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.*
import static com.sankuai.dzshoppingguide.product.detail.application.constants.BathCPVConstant.*

List<BOStyle<ComponentBO>> styles = Lists.newArrayList(
        new BathGroovyBOStyle2(), new AdditionalInfoBOStyle(), new ServiceFacilityBOStyle()
);
List<ComponentBO> details = mergeStyleResult(styles, context);
return details;


static List<ComponentBO> mergeStyleResult(List<BOStyle<ComponentBO>> styles,
                                                DealDetailBuildContext context) {
    List<List<ComponentBO>> list = Lists.newArrayList();
    for (BOStyle<ComponentBO> style : styles) {
        List<ComponentBO> ComponentBOS = style.build(context);
        if (CollectionUtils.isNotEmpty(ComponentBOS)) {
            list.add(ComponentBOS);
        }
    }
    List<ComponentBO> details = Lists.newArrayList();
    for (int i = 0; i < list.size(); i++) {
        if (CollectionUtils.isNotEmpty(list.get(i))) {
            details.addAll(list.get(i));
            if (i < list.size() - 1) {
//                details.add(LayoutNewLineComponent.builder().build());
            }
        }
    }
    return details;

}

class BathGroovyBOStyle2 implements BOStyle<ComponentBO> {

    @Override
    List<ComponentBO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        ProductCategory productCategory = context.getProductCategory();
        ProductStandardServiceProject productStandardServiceProject = context.getProductStandardServiceProject();
        if (Objects.isNull(productStandardServiceProject)) {
            return Collections.emptyList();
        }
        List<Optional<List<ComponentBO>>> buildResults = Lists.newArrayList(
                // 浴资票(m选n)
                BathCommonComponentRule.buildBathTicket(productCategory, productStandardServiceProject),
                // 按摩足疗(m选n)
                BathCommonComponentRule.buildMassage(productCategory, productStandardServiceProject),
                // 搓澡(m选n)
                BathCommonComponentRule.buildScrub(productCategory, productStandardServiceProject),
                // 餐饮(m选n) || 自助餐(m选n)
                BathCommonComponentRule.buildFood(productCategory, productStandardServiceProject),
                // 住宿休憩(m选n)
                BathCommonComponentRule.buildAccommodation(productCategory, productStandardServiceProject),
                // 玩乐(m选n)
                BathCommonComponentRule.buildPlay(productCategory, productStandardServiceProject),
                // 美容spa(m选n)
                BathCommonComponentRule.buildSPA(productCategory, productStandardServiceProject),
                // 附加服务(m选n)
                BathCommonComponentRule.buildAdditionalService(productStandardServiceProject),
                // 以下M选N
                BathCommonComponentRule.buildOptionalService(productStandardServiceProject),
                // 过夜(只展示免费过夜, 负责过夜在trade服务中)
                BathCommonComponentRule.buildFreeOvernight(productAttr)
        );
        return buildWithSeparator(buildResults);
    }

    private static List<ComponentBO> buildWithSeparator(List<Optional<List<ComponentBO>>> items) {
        List<ComponentBO> result = Lists.newArrayList();
        boolean hasContent = false;
        for (Optional<List<ComponentBO>> item : items) {
            if (item.isPresent() && !item.get().isEmpty()) {
                if (hasContent) {
//                    result.add(new LayoutDividerStripComponent());
                }
                result.addAll(item.get());
                hasContent = true;
            }
        }
        return result;
    }
}

/**
 * 洗浴组件
 */
class BathCommonComponentRule {

    static Optional<List<ComponentBO>> buildServiceProject(
            ProductStandardServiceProject standardServiceProject,
            ServiceProjectBuilder itemBuilder,
            BathServiceProjectEnum projectType) {

        List<ComponentBO> result = org.apache.commons.compress.utils.Lists.newArrayList()

        List<StandardServiceProjectGroupDTO> sameCPVObjectGroups = standardServiceProject.getSameCPVObjectId(projectType.getCpvObjectId(), standardServiceProject.getStandardServiceProject())
        if (CollectionUtils.isEmpty(sameCPVObjectGroups)) {
            return Optional.of(result)
        }
        // 处理全部可享与M选N之间的分割线
        boolean isFirst = true
        for (StandardServiceProjectGroupDTO group : sameCPVObjectGroups) {
            // Add separator before each group except the first one
            if (!isFirst) {
//                result.add(LayoutDividerStripComponent.builder().build())
            }
            isFirst = false

            Integer optionalCount = group.getOptionalCount()
            int total = group.getServiceProjectItems().size()
            // 构建标题（全部可享或M选N）
            def titleOpt = buildTitle(
                    projectType.getServiceProjectName(),
                    optionalCount > 0 ? total : null,
                    optionalCount > 0 ? optionalCount : null
            )
            if (titleOpt.isPresent()) {
                result.add(titleOpt.get())
            }

            // 处理服务项目
            for (StandardServiceProjectItemDTO item : group.getServiceProjectItems()) {
                def componentOpt = itemBuilder.build(item, projectType)
                if (componentOpt.isPresent()) {
                    result.add(componentOpt.get())
                }
            }
        }
        return Optional.of(result)
    }

    static Optional<List<ComponentBO>> buildBathTicket(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    BATH_TICKET_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    BATH_TICKET_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildMassage(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    MASSAGE_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    MASSAGE_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildScrub(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    SCRUB_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    SCRUB_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildFood(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    FOOD_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    FOOD_2
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    BUFFET
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildAccommodation(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    ACCOMMODATION_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    ACCOMMODATION_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildPlay(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    PLAY_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    PLAY_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildSPA(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {
        int thirdCategoryId = 0
        if (productCategory != null) {
            thirdCategoryId = productCategory.getProductThirdCategoryId()
        }

        if (thirdCategoryId == BathThirdCategoryEnum.IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    SPA_1
            )
        } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {
            return buildServiceProject(
                    standardServiceProject,
                    new ServiceProjectBuilder() {
                        @Override
                        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                            return buildServiceProjectItem(serviceProjectItem, projectType)
                        }
                    },
                    SPA_2
            )
        } else {
            return Optional.empty()
        }
    }

    static Optional<List<ComponentBO>> buildAdditionalService(ProductStandardServiceProject standardServiceProject) {
        return buildServiceProject(
                standardServiceProject,
                new ServiceProjectBuilder() {
                    @Override
                    Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
                        return buildServiceProjectItem(serviceProjectItem, projectType)
                    }
                },
                ADDITIONAL_SERVICE
        )
    }

    static Optional<List<ComponentBO>> buildOptionalService(ProductStandardServiceProject standardServiceProject) {
        List<ComponentBO> result = org.apache.commons.compress.utils.Lists.newArrayList()
        List<StandardServiceProjectGroupDTO> differentCPVObjectGroups = standardServiceProject.getDifferentCPVObjectId(standardServiceProject.getStandardServiceProject())
        if (CollectionUtils.isNotEmpty(differentCPVObjectGroups)) {
            for (StandardServiceProjectGroupDTO differentCPVObjectGroup : differentCPVObjectGroups) {
                Integer optionalCount = differentCPVObjectGroup.getOptionalCount()
                int total = differentCPVObjectGroup.getServiceProjectItems().size()
                def titleOpt = buildTitle("以下", total, optionalCount)
                if (titleOpt.isPresent()) {
                    result.add(titleOpt.get())
                }
                // differentCPVObjectGroup.getServiceProjectItems()一定不为空
                for (StandardServiceProjectItemDTO serviceProjectItem : differentCPVObjectGroup.getServiceProjectItems()) {
                    Long cpvObjectId = serviceProjectItem.getStandardAttribute().getCpvObjectId()
                    def componentOpt = buildServiceProjectItem(serviceProjectItem, BathServiceProjectEnum.getEnumByServiceProjectId(cpvObjectId))
                    if (componentOpt.isPresent()) {
                        result.add(componentOpt.get())
                    }
                }
            }
        }
        return Optional.of(result)
    }

    static Optional<List<ComponentBO>> buildFreeOvernight(ProductAttr productAttr) {
        List<ComponentBO> result = org.apache.commons.compress.utils.Lists.newArrayList()
        String overnightService = productAttr.getSkuAttrFirstValue(OVERNIGHT_SERVICE)
        // 付费过夜由trade服务展示
        if (StringUtils.equals(overnightService, "可付费过夜")) {
            return Optional.of(org.apache.commons.compress.utils.Lists.newArrayList())
        } else if (StringUtils.equals(overnightService, "不可过夜")) {
            return Optional.of(org.apache.commons.compress.utils.Lists.newArrayList())
        } else if (StringUtils.equals(overnightService, "可免费过夜")) {
            def titleOpt = buildTitle("免费过夜", null, null)
            if (titleOpt.isPresent()) {
                result.add(titleOpt.get())
            }
            def itemOpt = buildFreeOvernightItem(productAttr)
            if (itemOpt.isPresent()) {
                result.add(itemOpt.get())
            }
        }
        return Optional.of(result)
    }

    private static Optional<ComponentBO> buildTitle(String title, Integer total, Integer optionalCount) {
        StringBuilder sb = new StringBuilder(title)
        if (total != null && optionalCount != null && !total.equals(optionalCount)) {
            sb.append(total).append("选").append(optionalCount)
        }
        return Optional.of(TitleComponentBO.builder().title(sb.toString()).build())
    }

    private static Optional<ComponentBO> buildFreeOvernightItem(ProductAttr productAttr) {
        String overnightStartTime = productAttr.getSkuAttrFirstValue(OVERNIGHT_START_TIME)
        String overnightEndTime = productAttr.getSkuAttrFirstValue(OVERNIGHT_END_TIME)
        String freeBreakfast = productAttr.getSkuAttrFirstValue(FREE_BREAKFAST)
        String stayOverDesc = productAttr.getSkuAttrFirstValue(STAY_OVER_DESC)
        StringBuilder content = new StringBuilder()
        if (StringUtils.isNotBlank(overnightStartTime) && StringUtils.isNotBlank(overnightEndTime)) {
            content.append("起止时间：").append(overnightStartTime).append("-").append(overnightEndTime)
        }
        if (StringUtils.isNotBlank(freeBreakfast)) {
            content.append(freeBreakfast)
        }
        StringBuilder subContent = new StringBuilder()
        if (StringUtils.isNotBlank(stayOverDesc)) {
            subContent.append(stayOverDesc)
        }
        ServiceProjectItemComponentBO component = ServiceProjectItemComponentBO.builder().build()
        component.setItemName(content.toString())
        component.setItemDesc(subContent.toString())
        component.setShow(true)
        return Optional.of(component)
    }

    private static Optional<ComponentBO> buildServiceProjectItem(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
        if (projectType == BATH_TICKET_1 || projectType == BATH_TICKET_2) {
            ServiceProjectItemComponentBO component = ServiceProjectItemComponentBO.builder().build()
            component.setItemName(buildBathTicketItemContent(serviceProjectItem))
            component.setItemExtra(buildUnit(serviceProjectItem))
            component.setItemDesc(buildBathTicketItemSubContent(serviceProjectItem))
            component.setShow(true)
            return Optional.of(component)
        }
        ServiceProjectItemComponentBO component = ServiceProjectItemComponentBO.builder().build()
        component.setItemName(buildServiceProjectItemContent(serviceProjectItem))
        component.setItemExtra(buildUnit(serviceProjectItem))
        component.setItemDesc(buildServiceProjectItemSubContent(serviceProjectItem, projectType))
        component.setShow(true)
        return Optional.of(component)
    }

    private static String buildServiceProjectItemSubContent(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {
        switch (projectType) {
            case MASSAGE_1:
            case MASSAGE_2:
                return buildMassageItemSubContent(serviceProjectItem)
            case SCRUB_1:
            case SCRUB_2:
                return buildScrubItemSubContent(serviceProjectItem)
            case FOOD_1:
            case FOOD_2:
                return buildFoodItemSubContent(serviceProjectItem)
            case BUFFET:
                return buildBuffetItemSubContent(serviceProjectItem)
            case ACCOMMODATION_1:
            case ACCOMMODATION_2:
                return buildAccommodationItemSubContent(serviceProjectItem)
            case PLAY_1:
            case PLAY_2:
                return buildPlayItemSubContent(serviceProjectItem)
            case SPA_1:
            case SPA_2:
                return buildSPAItemSubContent(serviceProjectItem)
            case ADDITIONAL_SERVICE:
                return buildAdditionalServiceItemSubContent(serviceProjectItem)
            default:
                return StringUtils.EMPTY
        }
    }

    private static String buildAdditionalServiceItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        if (StringUtils.isNotBlank(content)) {
            sb.append(content)
        }
        return sb.toString()
    }

    private static String buildSPAItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String serviceType = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_TYPE, "、")
        appendIfNotBlank(sb, serviceType)
        String serviceEffect = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_EFFECT, "、")
        appendIfNotBlank(sb, "|", serviceEffect)
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "\n", content)
        String timeRange = ProductStandardServiceProject.getTimeRange(serviceProjectItem.getStandardAttribute(), START_TIME, END_TIME)
        appendIfNotBlank(sb, "\n", timeRange, "可用")
        return sb.toString()
    }

    private static String buildPlayItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String playType = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), PLAY_TYPE)
        appendIfNotBlank(sb, playType)
        String serviceDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_DURATION)
        String durationUnit = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), DURATION_UNIT)
        appendIfNotBlank(sb, "|", serviceDuration, durationUnit)
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "|", content)
        String timeRange = ProductStandardServiceProject.getTimeRange(serviceProjectItem.getStandardAttribute(), START_TIME, END_TIME)
        appendIfNotBlank(sb, "\n", timeRange, "可用")
        return sb.toString()
    }

    private static String buildAccommodationItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String roomType = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), ROOM_TYPE)
        appendIfNotBlank(sb, roomType)
        String serviceDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_DURATION)
        appendIfNotBlank(sb, "|", serviceDuration, "小时")
        String isProvideMeal = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), IS_PROVIDE_MEAL)
        appendIfNotBlank(sb, "|", isProvideMeal)
        String breakfastType = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BREAKFAST_TYPE, "、")
        appendIfNotBlank(sb, "（", breakfastType, "）")
        List<String> line = org.apache.commons.compress.utils.Lists.newArrayList()
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        addToListIfNotBlank(line, content)
        String checkOutTime = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CHECK_OUT_TIME)
        addToListIfNotBlank(line, checkOutTime + "（前退房）")
        if (CollectionUtils.isNotEmpty(line)) {
            sb.append("\n").append(String.join("|", line))
        }
        return sb.toString()
    }

    private static String buildFoodItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String cuisineTypes = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CUISINE_TYPES)
        if (StringUtils.equals(cuisineTypes, "自助餐")) {
            String buffetContent = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BUFFET_CONTENT, "、")
            appendIfNotBlank(sb, "自助餐：", buffetContent)
        } else if (StringUtils.equals(cuisineTypes, "单点")) {
            String menuFoodContent = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), MENU_FOOD_CONTENT, "、")
            appendIfNotBlank(sb, menuFoodContent)
        }
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "\n", content)
        List<StandardAttributeDTO> foodServiceTime = ProductStandardServiceProject.getAllComplexValues(serviceProjectItem.getStandardAttribute(), FOOD_SERVICE_TIME)
        List<String> line = org.apache.commons.compress.utils.Lists.newArrayList()
        if (CollectionUtils.isNotEmpty(foodServiceTime)) {
            List<String> timePeriods = org.apache.commons.compress.utils.Lists.newArrayList()
            for (StandardAttributeDTO standardAttributeDTO : foodServiceTime) {
                String mealStartTime = ProductStandardServiceProject.getValue(standardAttributeDTO, MEAL_START_TIME)
                String mealEndTime = ProductStandardServiceProject.getValue(standardAttributeDTO, MEAL_END_TIME)
                timePeriods.add(mealStartTime + "-" + mealEndTime)
            }
            line.add(ProductStandardServiceProject.joinListByDelimiter(timePeriods, "、"))
        }
        String buffetDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BUFFET_DURATION)
        if (StringUtils.isNotBlank(buffetDuration)) {
            if (StringUtils.equals(buffetDuration, "不限时")) {
                line.add("用餐时长不限时")
            } else {
                line.add("用餐时长" + buffetDuration)
            }
        }
        if (CollectionUtils.isNotEmpty(line)) {
            sb.append("\n").append(String.join("，", line))
        }
        return sb.toString()
    }

    private static String buildBuffetItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String buffetContent = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BUFFET_CONTENT, "、")
        appendIfNotBlank(sb, buffetContent)
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "\n", content)
        List<StandardAttributeDTO> buffetServiceTime = ProductStandardServiceProject.getAllComplexValues(serviceProjectItem.getStandardAttribute(), BUFFET_SERVICE_TIME)
        List<String> line = org.apache.commons.compress.utils.Lists.newArrayList()
        if (CollectionUtils.isNotEmpty(buffetServiceTime)) {
            List<String> timePeriods = org.apache.commons.compress.utils.Lists.newArrayList()
            for (StandardAttributeDTO standardAttributeDTO : buffetServiceTime) {
                String buffetStartTime = ProductStandardServiceProject.getValue(standardAttributeDTO, BUFFET_START_TIME)
                String buffetEndTime = ProductStandardServiceProject.getValue(standardAttributeDTO, BUFFET_END_TIME)
                timePeriods.add(buffetStartTime + "-" + buffetEndTime)
            }
            line.add(ProductStandardServiceProject.joinListByDelimiter(timePeriods, "、"))
        }
        String buffetDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BUFFET_DURATION)
        if (StringUtils.isNotBlank(buffetDuration)) {
            if (StringUtils.equals(buffetDuration, "不限时")) {
                line.add("用餐时长不限时")
            } else {
                line.add("用餐时长" + buffetDuration)
            }
        }
        if (CollectionUtils.isNotEmpty(line)) {
            sb.append("\n").append(String.join("，", line))
        }
        return sb.toString()
    }

    private static String buildScrubItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String bathMaterials = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BATH_MATERIALS, "、")
        appendIfNotBlank(sb, bathMaterials)
        String serviceDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_DURATION)
        appendIfNotBlank(sb, "|", serviceDuration, "分钟")
        String extraService = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), EXTRA_SERVICE, "、")
        appendIfNotBlank(sb, "|", extraService)
        String suitCrowds = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SUIT_CROWDS)
        appendIfNotBlank(sb, "|", suitCrowds)
        List<String> line = org.apache.commons.compress.utils.Lists.newArrayList()
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        addToListIfNotBlank(line, content)
        String startTime = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), START_TIME)
        String endTime = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), END_TIME)
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            line.add(startTime + "-" + endTime + "可用")
        }
        if (CollectionUtils.isNotEmpty(line)) {
            String lineStr = ProductStandardServiceProject.joinListByDelimiter(line, "|")
            sb.append("\n").append(lineStr)
        }
        return sb.toString()
    }

    private static String buildServiceProjectItemContent(StandardServiceProjectItemDTO serviceProjectItem) {
        String projectName = serviceProjectItem.getServiceProjectName()
        if (StringUtils.isNotBlank(projectName)) {
            return projectName
        } else {
            return StringUtils.EMPTY
        }
    }

    private static String buildMassageItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String spuCategory = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SPU_CATEGORY)
        appendIfNotBlank(sb, spuCategory)
        String bodyRegion = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), BODY_REGION)
        String servicePosition = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_POSITION, "、")
        if (StringUtils.equals(bodyRegion, "全身")) {
            sb.append("|").append("全身")
        } else if (StringUtils.equals(bodyRegion, "局部部位")) {
            appendIfNotBlank(sb, "|", servicePosition)
        }
        String serviceDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), SERVICE_DURATION)
        appendIfNotBlank(sb, "|", serviceDuration, "分钟")
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "\n", content)
        String timeRange = ProductStandardServiceProject.getTimeRange(serviceProjectItem.getStandardAttribute(), START_TIME, END_TIME)
        appendIfNotBlank(sb, "\n", timeRange, "可用")
        return sb.toString()
    }

    private static String buildBathTicketItemContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String availableTimePeriod = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), AVAILABLE_TIME_PERIOD)
        if (!StringUtils.equals(availableTimePeriod, "营业时间内全部可用")) {
            String availableTimePeriodName = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), AVAILABLE_TIME_PERIOD_NAME)
            appendIfNotBlank(sb, availableTimePeriodName)
        }
        String applicableDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), APPLICABLE_DURATION)
        appendIfNotBlank(sb, applicableDuration, "小时")
        sb.append("浴资票").append(buildApplicablePeople(serviceProjectItem))
        return sb.toString()
    }

    private static String buildApplicablePeople(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String adultPopulation = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), ADULT_POPULATION)
        String childrenPopulation = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CHILDREN_POPULATION)
        if (StringUtils.isBlank(adultPopulation) && StringUtils.isBlank(childrenPopulation)) {
            return sb.toString()
        }
        if (!StringUtils.equals(adultPopulation, "0") && !StringUtils.equals(childrenPopulation, "0")) {
            sb.append(adultPopulation).append("成人").append(childrenPopulation).append("儿童")
        } else if (!StringUtils.equals(adultPopulation, "0") && StringUtils.equals(childrenPopulation, "0")) {
            sb.append(adultPopulation).append("人")
        } else if (!StringUtils.equals(childrenPopulation, "0") && StringUtils.equals(adultPopulation, "0")) {
            sb.append(childrenPopulation).append("儿童")
        }
        return sb.toString()
    }

    private static String buildBathTicketItemSubContent(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String applicablePeople = buildApplicablePeople(serviceProjectItem)
        appendIfNotBlank(sb, "适用", applicablePeople)
        String childInstructions = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CHILD_INSTRUCTIONS)
        if (StringUtils.isNotBlank(childInstructions)) {
            sb.append("（").append(childInstructions).append("）").append("，")
        } else {
            sb.append("，")
        }
        String admissionTime = buildAdmissionTime(serviceProjectItem)
        appendIfNotBlank(sb, admissionTime, "，")
        String applicableDuration = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), APPLICABLE_DURATION)
        appendIfNotBlank(sb, "可用", applicableDuration, "小时。")
        String content = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), CONTENT)
        appendIfNotBlank(sb, "\n", content, "。")
        return sb.toString()
    }

    private static String buildAdmissionTime(StandardServiceProjectItemDTO serviceProjectItem) {
        StringBuilder sb = new StringBuilder()
        String availableTimePeriod = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), AVAILABLE_TIME_PERIOD)
        if (StringUtils.equals(availableTimePeriod, "营业时间内全部可用")) {
            return sb.append("营业时间内可入场").toString()
        }
        String dateRange = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), TIME_RANGE, "、")
        // 自定义日期按"、"分隔
        String formattedTimeRange = formatDateRange(dateRange)
        appendIfNotBlank(sb, formattedTimeRange, "可入场")
        String timeRange = ProductStandardServiceProject.getTimeRange(serviceProjectItem.getStandardAttribute(), START_TIME_POINT, END_TIME_POINT)
        appendIfNotBlank(sb, timeRange)
        return sb.toString()
    }

    private static String formatDateRange(String dateRange) {
        if (StringUtils.isBlank(dateRange)) {
            return StringUtils.EMPTY
        }
        // 分割并排序
        String[] days = dateRange.split("、")
        Arrays.sort(days)

        // 将连续的数字转换成区间表示
        StringBuilder result = new StringBuilder()
        int start = Integer.parseInt(days[0])
        int prev = start

        for (int i = 1; i <= days.length; i++) {
            int curr = (i == days.length) ? -1 : Integer.parseInt(days[i])
            if (i == days.length || curr != prev + 1) {
                // 处理区间
                if (prev == start) {
                    result.append("周").append(numberToChinese(start)).append("、")
                } else {
                    result.append("周").append(numberToChinese(start))
                            .append("至周").append(numberToChinese(prev)).append("、")
                }
                start = curr
            }
            prev = curr
        }

        // 删除最后一个顿号
        if (result.length() > 0) {
            result.setLength(result.length() - 1)
        }
        return result.toString()
    }

    private static String buildUnit(StandardServiceProjectItemDTO serviceProjectItem) {
        String amount = ProductStandardServiceProject.getValue(serviceProjectItem.getStandardAttribute(), AMOUNT)
        // 【浴资票】服务项目会出现取不到数据的情况, 则填写默认值1
        return StringUtils.isBlank(amount) ? "（1份）" : "（" + amount + "份）"
    }

    // 数字转中文的辅助方法
    private static String numberToChinese(int num) {
        switch (num) {
            case 1:
                return "一"
            case 2:
                return "二"
            case 3:
                return "三"
            case 4:
                return "四"
            case 5:
                return "五"
            case 6:
                return "六"
            case 7:
                return "日"
            default:
                return ""
        }
    }

    // 辅助方法 - 如果值不为空则添加到StringBuilder
    private static void appendIfNotBlank(StringBuilder sb, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(value)
        }
    }

    // 辅助方法 - 如果值不为空则添加前缀和值到StringBuilder
    private static void appendIfNotBlank(StringBuilder sb, String prefix, String value) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(prefix).append(value)
        }
    }

    // 辅助方法 - 如果值不为空则添加前缀、值和后缀到StringBuilder
    private static void appendIfNotBlank(StringBuilder sb, String prefix, String value, String suffix) {
        if (StringUtils.isNotBlank(value)) {
            sb.append(prefix).append(value).append(suffix)
        }
    }

    // 辅助方法 - 如果值不为空则添加到List
    private static void addToListIfNotBlank(List<String> list, String value) {
        if (StringUtils.isNotBlank(value)) {
            list.add(value)
        }
    }

    interface ServiceProjectBuilder {
        Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType)
    }
}
