import com.google.common.collect.Lists
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.base.BOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style.AdditionalInfoBOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.style.ServiceFacilityBOStyle
import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.rule.BathCommonComponent2
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject
import org.apache.commons.collections4.CollectionUtils

List<BOStyle<ComponentBO>> styles = Lists.newArrayList(
        new BathGroovyBOStyle(), new AdditionalInfoBOStyle(), new ServiceFacilityBOStyle()
);
List<ComponentBO> details = mergeStyleResult(styles, context);
return details;


static List<ComponentBO> mergeStyleResult(List<BOStyle<ComponentBO>> styles,
                                                DealDetailBuildContext context) {
    List<List<ComponentBO>> list = Lists.newArrayList();
    for (BOStyle<ComponentBO> style : styles) {
        List<ComponentBO> ComponentBOS = style.build(context);
        if (CollectionUtils.isNotEmpty(ComponentBOS)) {
            list.add(ComponentBOS);
        }
    }
    List<ComponentBO> details = Lists.newArrayList();
    for (int i = 0; i < list.size(); i++) {
        if (CollectionUtils.isNotEmpty(list.get(i))) {
            details.addAll(list.get(i));
            if (i < list.size() - 1) {
//                details.add(LayoutNewLineComponent.builder().build());
            }
        }
    }
    return details;

}

class BathGroovyBOStyle implements BOStyle<ComponentBO> {

    @Override
    List<ComponentBO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        ProductCategory productCategory = context.getProductCategory();
        ProductStandardServiceProject productStandardServiceProject = context.getProductStandardServiceProject();
        if (Objects.isNull(productStandardServiceProject)) {
            return Collections.emptyList();
        }
        List<Optional<List<ComponentBO>>> buildResults = Lists.newArrayList(
                // 浴资票(m选n)
                BathCommonComponent2.buildBathTicket(productCategory, productStandardServiceProject),
                // 按摩足疗(m选n)
                BathCommonComponent2.buildMassage(productCategory, productStandardServiceProject),
                // 搓澡(m选n)
                BathCommonComponent2.buildScrub(productCategory, productStandardServiceProject),
                // 餐饮(m选n) || 自助餐(m选n)
                BathCommonComponent2.buildFood(productCategory, productStandardServiceProject),
                // 住宿休憩(m选n)
                BathCommonComponent2.buildAccommodation(productCategory, productStandardServiceProject),
                // 玩乐(m选n)
                BathCommonComponent2.buildPlay(productCategory, productStandardServiceProject),
                // 美容spa(m选n)
                BathCommonComponent2.buildSPA(productCategory, productStandardServiceProject),
                // 附加服务(m选n)
                BathCommonComponent2.buildAdditionalService(productStandardServiceProject),
                // 以下M选N
                BathCommonComponent2.buildOptionalService(productStandardServiceProject),
                // 过夜(只展示免费过夜, 负责过夜在trade服务中)
                BathCommonComponent2.buildFreeOvernight(productAttr)
        );
        return buildWithSeparator(buildResults);
    }

    private static List<ComponentBO> buildWithSeparator(List<Optional<List<ComponentBO>>> items) {
        List<ComponentBO> result = Lists.newArrayList();
        boolean hasContent = false;
        for (Optional<List<ComponentBO>> item : items) {
            if (item.isPresent() && !item.get().isEmpty()) {
                if (hasContent) {
//                    result.add(new LayoutDividerStripComponent());
                }
                result.addAll(item.get());
                hasContent = true;
            }
        }
        return result;
    }
}
