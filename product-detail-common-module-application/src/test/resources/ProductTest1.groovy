/**
 * 与ProductAviatorExample3#test7方法功能相同的Groovy脚本
 * 用于处理商品属性并格式化输出
 * 脚本执行时直接返回处理结果
 */

// 定义获取商品属性JSON值的函数
def getProductAttrJSONValue = { productAttributeModel, attrName ->
    if (productAttributeModel != null && productAttributeModel.attrs != null) {
        for (attribute in productAttributeModel.attrs) {
            if (attrName.equals(attribute.attrName) && attribute.attrValues != null && !attribute.attrValues.isEmpty()) {
                def standardAttributeValueDTO = attribute.attrValues[0]
                if (standardAttributeValueDTO != null && standardAttributeValueDTO.simpleValues != null && !standardAttributeValueDTO.simpleValues.isEmpty()) {
                    return new groovy.json.JsonSlurper().parseText(standardAttributeValueDTO.simpleValues[0])
                }
            }
        }
    }
    return []
}

// 定义获取商品属性值的函数
def getProductAttribute = { productAttributeModel, attrName ->
    if (productAttributeModel != null && productAttributeModel.attrs != null) {
        for (attribute in productAttributeModel.attrs) {
            if (attrName.equals(attribute.attrName) && attribute.attrValues != null && !attribute.attrValues.isEmpty()) {
                def standardAttributeValueDTO = attribute.attrValues[0]
                if (standardAttributeValueDTO != null && standardAttributeValueDTO.simpleValues != null && !standardAttributeValueDTO.simpleValues.isEmpty()) {
                    return standardAttributeValueDTO.simpleValues[0]
                }
            }
        }
    }
    return ""
}

// 主处理逻辑 - 直接执行并返回结果
def listValue = getProductAttrJSONValue(product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'serviceBodyRange')
def bodyRegion = getProductAttribute(product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'bodyRegion')
def result = ''
def size = listValue.size()

listValue.eachWithIndex { v, i ->
    result = result + v
    if (i < size - 1) {
        result = result + '、'
    }
}

if (bodyRegion == '全身') {
    return bodyRegion + '（' + result + '）'
} else {
    return result
}
