<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzshoppingguide.detail</groupId>
        <artifactId>product-detail-common-module</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-common-module-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>product-detail-common-module-application</name>

    <dependencies>
        <!-- 模块化编排框架 -->
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-module-arrange-framework</artifactId>
        </dependency>
        <!-- 模块化编排框架 -->
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-page-url-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-common-module-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>org.dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-common-module-domain</artifactId>
        </dependency>
        <!-- Project module -->
        <!-- 查询中心 -->
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.48</version>
        </dependency>
        <!-- 查询中心 -->
        <!-- POI查询 -->
        <dependency>
            <groupId>com.meituan.service.mobile.poi</groupId>
            <artifactId>sinai.client</artifactId>
            <version>3.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- POI查询 -->
        <!-- 头图相关 -->
        <dependency>
            <groupId>com.dianping.piccentercloud</groupId>
            <artifactId>piccenter-display-api</artifactId>
            <version>0.2.31</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>lion-facade</artifactId>
            <version>2.2.5</version>
        </dependency>
        <!-- 头图相关 -->
        <!--折扣卡-->
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-member-card-api</artifactId>
            <version>0.2.34</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dztheme-massagebook-api</artifactId>
            <version>0.0.19</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.158</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
            <version>0.5.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>avatar-dao</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>org.dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-booking-shop-api</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-cache</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-navigation-api</artifactId>
            <version>0.0.32</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping.tpfun</groupId>
                    <artifactId>sku-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
            <version>1.19.15</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai</groupId>
                    <artifactId>beautycontent.store.api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--折扣卡-->
        <!--用户收藏-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-remote</artifactId>
            <version>2.3.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.user.collection</groupId>
            <artifactId>coll-client</artifactId>
            <version>1.5.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.collection</groupId>
            <artifactId>thrift-api</artifactId>
            <version>1.2.2</version>
        </dependency>
        <!--用户收藏-->
        <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-xml</artifactId>
        </dependency>
        <!--图文详情-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-detail-api</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mapi-usercenter-api</artifactId>
            <version>0.0.30</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>2.0.1.5</version>
        </dependency>
        <!--图文详情-->
        <!--idmapper-->

        <!--cityId转换服务,新增加的服务不支持接入了,移步openplatform-dependency-->
        <!-- https://km.sankuai.com/page/435322840 -->
        <!--<dependency>-->
        <!--    <groupId>com.dianping.poi</groupId>-->
        <!--    <artifactId>poi-gis-api</artifactId>-->
        <!--    <version>0.5.67</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.2.50</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-proxy-api</artifactId>
            <version>1.7.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.mobile</groupId>
                    <artifactId>mapi-shell</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>vip-remote</artifactId>
            <version>0.3.35</version>
            <exclusions>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-act-report-read-api</artifactId>
            <version>0.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-base-remote</artifactId>
            <version>2.0.6</version>
        </dependency>
        <!--idmapper-->
        <!--bestShop-->
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.87</version>
        </dependency>
        <!--替换下面的deal-shop-api接口-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.15</version>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.plugin</groupId>
            <artifactId>pp-misc-common</artifactId>
            <version>1.1.4</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
            <version>0.0.161</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>common-base</artifactId>
            <version>2.13.70</version>
        </dependency>
        <!--标签查询接口-->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-query-api</artifactId>
            <version>1.0.25</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-common</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-pic-api</artifactId>
            <version>1.0.38</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dztg-bjwrapper-api</artifactId>
            <version>1.1.10</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-meta-tag-manage-api</artifactId>
            <version>1.0.25</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jaxb-api</artifactId>
                    <groupId>javax.xml.bind</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.activation-api</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--标签查询接口-->
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-launch-api</artifactId>
            <version>0.1.53</version>
            <exclusions>
                <exclusion>
                    <artifactId>mafka-client_2.9</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--企业微信-->
        <!--<dependency>-->
        <!--    <groupId>com.sankuai.scrm</groupId>-->
        <!--    <artifactId>scrm-core-api</artifactId>-->
        <!--    <version>2.0.41</version>-->
        <!--</dependency>-->
        <!--企业微信-->
        <!--榜单标签-->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dzrank-scenes-api</artifactId>
            <version>0.0.47</version>
        </dependency>
        <!--榜单标签-->
        <!--评价标签查询美团-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
            <version>3.4.31</version>
        </dependency>
        <!--评价标签查询美团-->
        <!--评价标签查询点评-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>review-api</artifactId>
            <version>5.10.19</version>
        </dependency>
        <!--评价标签查询点评-->
        <!--团购交易预约-->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>trade-general-reserve-api</artifactId>
            <version>0.0.93</version>
        </dependency>
        <!--团购交易预约-->
        <dependency>
            <groupId>com.meituan.nibscp.common</groupId>
            <artifactId>scp-common-enum</artifactId>
            <version>3.5.35</version>
        </dependency>
        <!--安心学-->
        <dependency>
            <groupId>com.sankuai.nib.price.operation</groupId>
            <artifactId>price-operation-api</artifactId>
            <version>1.0.25</version>
        </dependency>
        <!--安心学-->

        <!--门店标签-->
        <dependency>
            <groupId>com.sankuai.zdc</groupId>
            <artifactId>zdc-tag-apply-api</artifactId>
            <version>1.3.10</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--团购次卡补充说明-->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-attribute-api</artifactId>
            <version>1.1.20</version>
        </dependency>
        <!-- 基础rhino包 -->
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
            <version>1.7.2</version>
        </dependency>
        <!--在线咨询链接服务-->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>cliententry-api</artifactId>
            <version>0.0.31</version>
        </dependency>
        <!-- 智能客服 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-access-facade-api</artifactId>
            <version>0.0.66</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dzim-common</artifactId>
            <version>1.2.33</version>
            <exclusions>
                <exclusion>
                    <artifactId>mtrace-api</artifactId>
                    <groupId>com.meituan.mtrace</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-client</artifactId>
            <version>ES_0.4.22</version>
        </dependency>
        <!-- cpv进场需求 -->

        <dependency>
            <groupId>com.sankuai.meituan.shangou.standard</groupId>
            <artifactId>shangou_product_standardquery_client</artifactId>
            <version>1.4.4</version>
        </dependency>
        <!-- cpv进场需求 -->

        <!--商品结构（CPV）变更自动联动-->
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-ark-common</artifactId>
            <version>1.0.29</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-ark-api</artifactId>
            <version>1.0.29</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-ark-spi</artifactId>
            <version>0.0.22</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp.flow</groupId>
            <artifactId>scp-general-enhance-api</artifactId>
            <version>1.0.28</version>
        </dependency>
        <!--   diffDTO   -->
        <dependency>
            <groupId>com.meituan.nibscp.framework</groupId>
            <artifactId>scp-cpv-api</artifactId>
            <version>1.0.29</version>
            <exclusions>
                <exclusion>
                    <artifactId>scp-common-utils</artifactId>
                    <groupId>com.meituan.nibscp.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>tp-deal-outer-api</artifactId>
            <version>1.1.7</version>
        </dependency>
        <!--商品结构（CPV）变更自动联动-->

        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.9.0</version>
        </dependency>

    </dependencies>


</project>
