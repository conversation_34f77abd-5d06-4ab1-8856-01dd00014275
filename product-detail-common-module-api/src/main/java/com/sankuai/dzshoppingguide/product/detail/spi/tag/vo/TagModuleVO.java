package com.sankuai.dzshoppingguide.product.detail.spi.tag.vo;

import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/5 13:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TagModuleVO extends AbstractModuleVO {

    @Override
    public String getModuleKey() {
        return "module_tags";
    }

}
