package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;

import java.io.Serializable;
import java.util.List;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

@Data
public class UGCUserInfo implements Serializable {
    /**
     * 用户Id
     */
    @MobileField
    private long userId;

    /**
     * 用户标签图
     */
    @MobileField
    private List<ProductUserTag> userTags;

    /**
     * 用户头像
     */
    @MobileField
    private String picUrl;

    /**
     * 用户昵称
     */
    @MobileField
    private String nickName;

    /**
     * 是否匿名
     */
    @MobileField
    private boolean anonymous;

    /**
     * 用户详情跳链
     */
    @MobileField
    private String detailUrl;
}
