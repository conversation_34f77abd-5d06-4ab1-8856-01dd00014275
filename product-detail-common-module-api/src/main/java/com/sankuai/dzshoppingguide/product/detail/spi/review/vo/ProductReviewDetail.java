package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;

import lombok.Data;

import java.io.Serializable;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.util.List;

/**
 * MAPI定义见BookingDetailsUGCDetal
 */
@Data
public class ProductReviewDetail implements Serializable {
    /**
     * 图片视频列表
     */
    @MobileField
    private List<ProductReviewPicture> picList;

    /**
     * 评价标签
     */
    @MobileField
    private List<ProductReviewTag> tagList;

    /**
     * 消费订单价格
     */
    @MobileField
    private String orderPrice;

    /**
     * 点评时间
     */
    @MobileField
    private String reviewTime;

    /**
     * 评价来源：点评-1 美团-2
     */
    @MobileField
    private int reviewSource;

    /**
     * 跳链
     */
    @MobileField
    private String detailUrl;

    /**
     * 评价分数
     */
    @MobileField
    private String score;

    /**
     * 评价内容
     */
    @MobileField
    private String reviewBody;

    /**
     * 评价ID
     */
    @MobileField
    private long reviewId;

    /**
     * UGC用户信息
     */
    @MobileField
    private UGCUserInfo userInfo;
}
