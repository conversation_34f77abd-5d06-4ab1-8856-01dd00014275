package com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component;


import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/15 13:13
 * @Description: 服务设施子项具体内容（带icon）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class FacilityType5ViewComponent extends BaseViewComponent {

    private final String type = ViewComponentTypeEnum.FACILITY_TYPE_5.name();
    private List<Icon> contentsWithIcon;

}
