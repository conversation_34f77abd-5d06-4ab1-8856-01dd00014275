package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc
 */
@TypeDoc(description = "商品详情页须知条")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDetailReminderVO extends CommonGuaranteeInstructionsBar {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.REMINDER_INFO;
    }

    @FieldDoc(description = "须知条文案")
    @MobileDo.MobileField(key = 0xd34e)
    private GuaranteeInstructionsBarLayerVO layer;
}
