package com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0xefc4)
public class InventoryDetail implements Serializable {
    /**
    * 副标题
    */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
    * 主标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}