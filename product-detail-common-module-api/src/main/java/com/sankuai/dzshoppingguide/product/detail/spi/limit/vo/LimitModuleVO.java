package com.sankuai.dzshoppingguide.product.detail.spi.limit.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "限制条模型")
@MobileDo(id = 0x2961 )
public class LimitModuleVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.LIMIT_INFO;
    }

    @FieldDoc(description = "限制条内容")
    @MobileDo.MobileField(key = 0x8535)
    private List<LimitContent> contents;

    @FieldDoc(description = "限制条浮层信息")
    @MobileDo.MobileField(key = 0x479f)
    private List<CommonFloatLayer> layerConfigs;
}
