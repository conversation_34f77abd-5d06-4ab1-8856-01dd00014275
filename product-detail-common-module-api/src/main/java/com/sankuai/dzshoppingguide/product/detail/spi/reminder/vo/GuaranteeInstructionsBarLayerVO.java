package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @see <a href="https://mobile.sankuai.com/studio/model/info/41241">保障须知条浮层</a>
 */
@TypeDoc(description = "保障须知条浮层")
@Data
@MobileDo(id = 0xc32f)
public class GuaranteeInstructionsBarLayerVO implements Serializable {

    @FieldDoc(description = "浮层icon")
    @MobileDo.MobileField(key = 0x3c48)
    private Icon icon;

    @FieldDoc(description = "浮层标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "类型区分（1跳链 2浮层key）")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "对应前端浮层key")
    @MobileDo.MobileField(key = 0xf30e)
    private String modulekey;
}