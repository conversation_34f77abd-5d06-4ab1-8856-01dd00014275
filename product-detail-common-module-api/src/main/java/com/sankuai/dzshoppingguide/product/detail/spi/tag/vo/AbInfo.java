package com.sankuai.dzshoppingguide.product.detail.spi.tag.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/6 15:16
 */
@Data
@TypeDoc(description = "榜单标签ab实验模型")
@MobileDo(id = 0x7210)
public class AbInfo implements Serializable {
    @FieldDoc(description = "实验号_策略分组,例如: exp003395_b")
    @MobileDo.MobileField(key = 0x9ea5)
    private String abCode;

    @FieldDoc(description = "查询id")
    @MobileDo.MobileField(key = 0x370f)
    private String queryId;
}
