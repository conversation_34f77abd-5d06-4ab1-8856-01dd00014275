package com.sankuai.dzshoppingguide.product.detail.spi.title.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:43
 * 移动之家: https://mobile.sankuai.com/studio/model/info/41248
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "预订详情页标题模型")
@MobileDo(id = 0xd84c)
public class TitleModuleVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.TITLE;
    }

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}
