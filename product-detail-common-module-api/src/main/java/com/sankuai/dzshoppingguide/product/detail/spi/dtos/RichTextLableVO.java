package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@MobileDo(id = 0xaad8)
public class RichTextLableVO implements Serializable {
    /**
    * 字体格式权重
    */
    @MobileDo.MobileField(key = 0x579e)
    private String fontWeight;

    /**
    * 展示内容
    */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    /**
    * 文本样式
    */
    @MobileDo.MobileField(key = 0xddfb)
    private String textStyle;

    /**
    * 跳链
    */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
    * 文字颜色
    */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor;

    /**
    * 图片
    */
    @MobileDo.MobileField(key = 0xb548)
    private String preIcon;

    /**
    * 字体大小
    */
    @MobileDo.MobileField(key = 0xfee3)
    private int textSize;

    /**
    * 背景颜色
    */
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

}