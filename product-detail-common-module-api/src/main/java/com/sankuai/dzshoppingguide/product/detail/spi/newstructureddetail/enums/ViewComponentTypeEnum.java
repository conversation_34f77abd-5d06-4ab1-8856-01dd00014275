package com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025/2/8 11:41
 * @see <a href="https://km.sankuai.com/collabpage/2704693336">商品服务详情样式对照表</a>
 */
@Getter
@AllArgsConstructor
public enum ViewComponentTypeEnum {
    // 足疗需求
    DETAIL_TYPE_1(1, "服务详情", "服务项目标题"),
    DETAIL_TYPE_2(2, "服务详情", "服务部位"),
    DETAIL_TYPE_3(3, "服务详情","服务流程"),
    DETAIL_TYPE_4(4, "服务详情","自助餐&小吃&简餐标题"),
    DETAIL_TYPE_5(5, "服务详情","自助餐&小吃&简餐内容"),

    DETAIL_TYPE_11(11, "服务详情","茶馆茶水选择标题，标题前带圆点"),
    DETAIL_TYPE_12(12, "服务详情","茶馆服务项目标题"),
    DETAIL_TYPE_13(13, "服务详情","茶馆使用规则细项"),
    DETAIL_TYPE_14(14, "服务详情","茶馆包房信息标题"),
    DETAIL_TYPE_15(15, "服务详情","茶馆包房信息Item"),

    DETAIL_TYPE_NEW_LINE(2000, "服务详情","换行符"),
    DETAIL_TYPE_DIVIDER_STRIP(3000, "服务详情", "分割符"),




    FACILITY_TYPE_1(1, "服务设施","服务设施标题"),
    FACILITY_TYPE_2(2, "服务设施","服务设施子项（不带icon）"),
    FACILITY_TYPE_3(3, "服务设施","服务设施子项具体内容（不带icon）"),
    FACILITY_TYPE_4(4, "服务设施","服务设施子项（带icon）"),
    FACILITY_TYPE_5(5, "服务设施","服务设施子项具体内容（带icon）"),
    FACILITY_TYPE_6(6, "服务设施","服务设施供餐时段弹窗"),
    FACILITY_TYPE_16(16, "服务设施","服务设施一级标题"),
    FACILITY_TYPE_17(17, "服务设施","服务设施二级标题"),
    FACILITY_TYPE_18(18, "服务设施","服务设施内容"),

    // 双眼 + 口腔需求
    TITLE(6, "重点展示信息","标题"),
    NORMAL_TEXT(7, "文本信息","文本信息"),
    ANNOTATION(8, "注释信息","注释信息"),
    SUB_TEXT(9, "二级文案","二级文案"),
    SERVICE_PROCESS(10, "服务流程详情","服务流程详情"),

    PREFIX_DOT(1, "前缀点", "前缀点"),
    POPUP_DATA(2, "眼镜、口腔科普浮层", "眼镜、口腔科普浮层"),
    GUARANTEE_TITLE(1, "口腔保障浮层标题", "口腔保障浮层标题"),


    KEY_INFO(1001, "重点展示信息","重点展示信息"),
    EXCEL(1002, "表格信息","表格信息"),
    DELIMITER(2000, "模块间分隔符","模块间分隔符"),
    CRAFTSMAN(3001, "手艺人模块","手艺人模块"),

    // 弹窗
    POPUP_TAB(2, "tab弹窗", "tab弹窗"),
    POPUP_STRUCTURED(1, "结构化弹窗", "结构化弹窗"),
    IMAGE_POPUP(3, "图片弹窗", "图片弹窗"),
    BUBBLE_POPUP(4, "气泡弹窗", "气泡弹窗"),
    ;

    final int type;
    final String moduleName;
    final String desc;
}
