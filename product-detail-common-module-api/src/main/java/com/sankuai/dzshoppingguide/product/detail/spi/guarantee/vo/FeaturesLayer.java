package com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 保障浮层模型
 * @see <a href="https://mobile.sankuai.com/studio/model/info/32880">保障浮层模型</>
 */
@Data
@MobileDo(id = 0x4721)
public class FeaturesLayer implements Serializable {

    /**
    * 浮层标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    * 价保项
    */
    @MobileDo.MobileField(key = 0x479f)
    private List<LayerConfig> layerConfigs;

    /**
    * 价保标签
    */
    @MobileDo.MobileField(key = 0xcf30)
    private String priceProtectionTag;
}