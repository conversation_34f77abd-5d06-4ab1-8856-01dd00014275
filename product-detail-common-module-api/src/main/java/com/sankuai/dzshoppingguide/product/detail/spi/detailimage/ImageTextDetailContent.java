package com.sankuai.dzshoppingguide.product.detail.spi.detailimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ContentType;
import lombok.Data;

import java.io.Serializable;


@Data
@TypeDoc(description = "新版预订详情页（图文详情模块-内容详情）")
@MobileDo(id = 0x446c)
public class ImageTextDetailContent implements Serializable {

    @FieldDoc(description = "类型 0文本 1图片 2视频 3标题 4VR")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "文字内容")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "图片地址")
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    @FieldDoc(description = "图片比例")
    @MobileDo.MobileField(key = 0xfc9)
    private String scale;

    @FieldDoc(description = "图片宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private String width;

    @FieldDoc(description = "图片高度")
    @MobileDo.MobileField(key = 0x261f)
    private String height;

    public ImageTextDetailContent(int type, String content) {
        this.type = type;
        if (type == ContentType.PIC.getType()) {
            this.picUrl = content;
        } else {
            this.content = content;
        }
    }
}