package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;

import java.io.Serializable;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * mapi定义搜BookingDetailSevaluateScoreTag
 */
@Data
public class ProductReviewTagFilter implements Serializable {
    /**
     * 评价数
     */
    @MobileField
    private int reviewCount;

    /**
     * 1为高亮展示 2为普通灰色
     */
    @MobileField
    private int type;

    /**
     * 跳转链接
     */
    @MobileField
    private String url;

    /**
     * 标题
     */
    @MobileField
    private String title;

    /**
     * 情感：喜欢：1，不喜欢：-1，中立：0
     */
    private int affection;
}
