package com.sankuai.dzshoppingguide.product.detail.spi.addition.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 15:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "团购次卡补充说明")
@MobileDo(id = 0xe6e0)
public class AdditionalInfoVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.ADDITIONAL_INFO;
    }

    @FieldDoc(description = "名称")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "补充说明")
    @MobileDo.MobileField(key = 0x2847)
    private String descModel;
}
