package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:05
 */
public interface ModuleKeyConstants {

    /**
     * 通用数据模块for打点
     */
    String COMMON_DATA = "module_common_data";

    /**
     * 导航栏模块
     */
    String NAVIGATION_BAR = "module_detail_navigation_bar_normal";

    /**
     * 头图模块
     */
    String HEAD_PIC = "module_detail_head_pic_banner";

    /**
     * 预订折扣卡模块
     */
    String DISCOUNT_CARD = "module_detail_discountcard";

    /**
     * 标题模块
     */
    String TITLE = "module_detail_title";

    /**
     * 亮点模块
     */
    String HIGHLIGHTS = "module_detail_highlight";

    /**
     * 团购详情模块
     */
    String STRUCTURED_DEAL_DETAILS = "module_detail_structured_detail";

    /**
     * 服务设施
     */
    String DEAL_DETAIL_FACILITIES = "module_detail_service_facilities";


    /**
     * 图文详情模块
     */
    String DETAIL_IMAGE = "module_detail_image_text";

    /**
     * 须知条
     */
    String REMINDER_INFO = "module_detail_reminder_info_tag";

    /**
     * 须知条浮层-展开详情（与预定须知为同一module）
     */
    String REMINDER_INFO_INSTRUCTIONS = "module_detail_booking_instructions";

    /**
     * 保障条和保障浮层
     */
    String GUARANTEE_INFO = "module_detail_guarantee_info_tag";

    /**
     * 美团预订评价
     * 前端采用NPM包形式接入，后端开发SPI中转接口
     */
    String MT_RESERVE_REVIEW_MODULE_V1 = "module_detail_mt_review_v1";

    /**
     * 点评预订店评价
     */
    String DP_RESERVE_SHOP_REVIEW_MODULE= "module_detail_dp_reserve_shop_review";

    /**
     * 门店标签模块
     */
    String SHOP_TAG= "module_detail_deal_shop_tag";

    /**
     * 适用门店模块
     */
    String AVAILABLE_SHOP= "module_detail_deal_available_shop";

    /**
     * 团购评价
     */
    String DEAL_REVIEW = "module_detail_deal_review";

    /**
     * 标签模块
     */
    String MODULE_TAGS = "module_detail_deal_tags";

    /**
     * 团购次卡补充说明
     */
    String ADDITIONAL_INFO = "module_detail_deal_timescard_additional";

    /**
     * 补充说明模块
     */
    String ADDITIONAL_INFO_MODULE = "module_detail_deal_additional";

    /**
     * 企业微信模块
     */
    String WX_CORP_BANNER = "module_detail_wx_corp_banner";


    String MODULE_DETAIL_TAB = "module_detail_tab";

    String MODULE_DETAIL_RECOMMEND = "module_detail_recommend";

    /**
     * 视频浮层
     */
    String FLOAT_VIDEO_LAYER = "module_detail_deal_float_video_layer";

    String LIMIT_INFO = "module_detail_limit_module";

    String INVENTORY_INFO = "module_detail_inventory_module";
}
