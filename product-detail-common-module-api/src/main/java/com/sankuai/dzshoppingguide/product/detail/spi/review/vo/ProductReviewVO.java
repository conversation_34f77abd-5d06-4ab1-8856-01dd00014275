package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ProductReviewVO extends AbstractModuleVO implements Serializable {

    /**
     * 标签筛选器
     */
    @MobileDo.MobileField
    private List<ProductReviewTagFilter> tagList;

    /**
     * 评价列表
     */
    @MobileField
    private List<ProductReviewDetail> reviewList;

    /**
     * 更多跳转链接
     */
    @MobileField
    private String moreUrl;

    /**
     * 模块名
     */
    @MobileField
    private String title;

    /**
     * 评价总数
     */
    @MobileField
    private int totalCount;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.DEAL_REVIEW;
    }
}
