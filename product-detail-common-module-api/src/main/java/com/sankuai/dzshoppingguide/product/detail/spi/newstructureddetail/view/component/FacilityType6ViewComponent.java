package com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component;


import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/15 13:13
 * @Description: 服务设施供餐时段弹窗
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class FacilityType6ViewComponent extends BaseViewComponent {

    private final String type = ViewComponentTypeEnum.FACILITY_TYPE_6.name();
    private String title;
    private String content;

}
