package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/5 16:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "推荐模块")
@MobileDo(id = 0x14c4)
public class ModuleDetailRecommendVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MODULE_DETAIL_RECOMMEND;
    }
}
