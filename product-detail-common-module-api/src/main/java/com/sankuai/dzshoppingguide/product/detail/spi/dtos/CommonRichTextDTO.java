package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/28 15:17
 */
@Data
@TypeDoc(description = "通用富文本模型")
@MobileDo(id = 0xef2a)
public class CommonRichTextDTO implements Serializable {

    @FieldDoc(description = "字体颜色")
    @MobileDo.MobileField(key = 0x2efe)
    private String fontColor;

    @FieldDoc(description = "字体粗细")
    @MobileDo.MobileField(key = 0x579e)
    private String fontWeight;

    @FieldDoc(description = "字体大小")
    @MobileDo.MobileField(key = 0xb53a)
    private String fontSize;

    @FieldDoc(description = "内容")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "dot标识符")
    @MobileDo.MobileField(key = 0x4188)
    private boolean dotFlag;

    @FieldDoc(description = "字体类型")
    @MobileDo.MobileField(key = 0x45e2)
    private String fontType;
}
