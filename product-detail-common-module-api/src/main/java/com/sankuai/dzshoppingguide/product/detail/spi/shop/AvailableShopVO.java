package com.sankuai.dzshoppingguide.product.detail.spi.shop;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@MobileDo(id = 0xe861)
public class AvailableShopVO extends AbstractModuleVO implements Serializable {
    /**
     * 门店地图跳链
     */
    @MobileField(key = 0x201e)
    private String shopMapUrl;

    /**
     * 门店列表链接
     */
    @MobileField(key = 0x46c8)
    private String shopListUrl;

    /**
     * 门店跳链
     */
    @MobileField(key = 0x7dac)
    private String shopUrl;

    /**
     * IM跳链
     */
    @MobileField(key = 0x7851)
    private String imUrl;

    /**
     * 商户电话数组
     */
    @MobileField(key = 0x6536)
    private List<String> phoneNos;

    /**
     * 门店名称
     */
    @MobileField(key = 0xb5c9)
    private String shopName;

    /**
     * 营业时间
     */
    @MobileField(key = 0x9e25)
    private String businessHour;

    /**
     * 均价文案
     */
    @MobileField(key = 0x4b46)
    private String avgPrice;

    /**
     * 门店星级，数值在[0,50], 如果传入-1代表无星级信息
     */
    @MobileField(key = 0xa669)
    private int shopPower;

    /**
     * 距离文案
     */
    @MobileField(key = 0x9ac4)
    private String distance;

    /**
     * 门店头图
     */
    @MobileField(key = 0x8980)
    private String shopPic;

    /**
     * 门店总数
     */
    @MobileField(key = 0x244c)
    private long totalCount;

    /**
     * 门店ID
     */
    @MobileField(key = 0x349b)
    private long shopId;

    /**
     * 模块名
     */
    @MobileField(key = 0xb308)
    private String moduleName;


    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.AVAILABLE_SHOP;
    }
}
