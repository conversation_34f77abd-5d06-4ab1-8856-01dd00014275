package com.sankuai.dzshoppingguide.product.detail.spi.tag.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 11:40
 */
@Data
@TypeDoc(description = "评价标签模块")
@MobileDo(id = 0x258)
public class ReviewTagVO extends BaseTagDTO {

    @FieldDoc(description = "好评度")
    @MobileDo.MobileField(key = 0x48c9)
    private String goodReviewRatio;

    @FieldDoc(description = "评价分数")
    @MobileDo.MobileField(key = 0x7e44)
    private String reviewScore;


}
