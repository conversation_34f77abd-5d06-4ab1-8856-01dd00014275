package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/05/22
 * 商品结构（CPV）变更自动联动 影响分析 配置
 */
@Data
public class CpvImpactAnalyseConfig {

    /**
     * 新增类型 影响提示文案
     */
    private String impactTipAdd;

    /**
     * 删除类型 影响提示文案
     */
    private String impactTipDelete;

    /**
     * 更新类型 影响提示文案
     */
    private String impactTipUpdate;

    /**
     * 创建人mis号, 为空时会使用creatorId
     */
    private String creatorMis;

    /**
     * 修改人mis号, 为空时会使用modifierId
     */
    private String modifierMis;

    /**
     * creatorId（IAM账号id/empId）
     */
    private Long creatorId;

    /**
     * modifierId（IAM账号id/empId）
     */
    private Long modifierId;

    /**
     * 阻断变更提示文案
     */
    private String abortTip;

    /**
     * 不阻断变更提示文案
     */
    private String notAbortTip;

    /**
     * 联动变更提示文案
     */
    private String cascadeExecuteTip;

    /**
     * 是否有影响提示文案
     */
    private String hasImpactTip;

}
