package com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/15 11:24
 * @Description: TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class FacilityType1ViewComponent extends BaseViewComponent {

    public final String type = ViewComponentTypeEnum.FACILITY_TYPE_1.name();
    private String title;
}
