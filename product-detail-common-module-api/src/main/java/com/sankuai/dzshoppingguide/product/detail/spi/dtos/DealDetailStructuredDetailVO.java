package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @create 2025/2/6 19:12
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41235
 */
@Data
@Builder
@TypeDoc(description = "新版预订详情页（结构化团详模块）")
@MobileDo(id = 0xcaae)
public class DealDetailStructuredDetailVO implements Serializable {

    @FieldDoc(description = "行数据标识")
    @MobileDo.MobileField(key = 0xb231)
    private String itemId;

    @FieldDoc(description = "content颜色")
    @MobileDo.MobileField(key = 0xde6d)
    private String contentColor;

    @FieldDoc(description = "信息前缀 https://mobile.sankuai.com/studio/model/edit/41235")
    @MobileDo.MobileField(key = 0x7706)
    private Integer prefix;

    @FieldDoc(description = "跳转链接")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "弹窗浮层数据")
    @MobileDo.MobileField(key = 0x4548)
    private String popupData;

    @FieldDoc(description = "渐变背景色-终色")
    @MobileDo.MobileField(key = 0x9ca4)
    private String endBackgroundColor;

    @FieldDoc(description = "序号")
    @MobileDo.MobileField(key = 0x811f)
    private Integer order;

    @FieldDoc(description = "副内容")
    @MobileDo.MobileField(key = 0x667e)
    private String subContent;

    @FieldDoc(description = "图标地址")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "背景颜色")
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    @FieldDoc(description = "详情")
    @MobileDo.MobileField(key = 0xa83b)
    private String detail;

    @FieldDoc(description = "内容")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "标题字体加粗")
    @MobileDo.MobileField(key = 0xc2e5)
    private String titleFontWeight;

    @FieldDoc(description = "对应前端展示类型，目前1-5")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 单位文本，份数等，例如：1份
     */
    @MobileDo.MobileField(key = 0xd9b2)
    private String unit;

    /**
     * 子标题
     */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 弹窗图标
     */
    @MobileDo.MobileField(key = 0x3005)
    private String popupIcon;
}
