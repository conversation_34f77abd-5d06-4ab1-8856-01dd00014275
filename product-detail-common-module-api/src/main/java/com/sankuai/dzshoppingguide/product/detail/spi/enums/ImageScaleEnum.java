package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2023/8/20
 */
@Getter
@AllArgsConstructor
public enum ImageScaleEnum {
    THREE_TO_FOUR(1, "3:4"),
    SIXTEEN_TO_NINE(2, "16:9"),
    ONE_TO_ONE(3, "1:1"),
    ;

    private int code;

    private String scale;

    public ImageScaleEnum getByCode(int code) {
        for (ImageScaleEnum imageScaleEnum : ImageScaleEnum.values()) {
            if (imageScaleEnum.code == code) {
                return imageScaleEnum;
            }
        }
        return null;
    }

    public ImageScaleEnum getByScale(String scale) {
        for (ImageScaleEnum imageScaleEnum : ImageScaleEnum.values()) {
            if (imageScaleEnum.scale.equals(scale)) {
                return imageScaleEnum;
            }
        }

        return null;
    }
}