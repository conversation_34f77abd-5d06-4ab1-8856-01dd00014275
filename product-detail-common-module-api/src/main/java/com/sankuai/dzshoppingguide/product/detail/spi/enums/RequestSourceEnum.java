package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
public enum RequestSourceEnum {
    /**
     * 美团猜喜页进入团详页路径标识，历史原因有2个标识
     */
    CAI_XI("caixi", "CAIXI"),
    HOME_PAGE("homepage", "CAIXI"),

    /**
     * 分销场景进入团详页路径标识
     */
    ODP("odp", "O2O_DISTRIBUTE_PLATFORM"),

    /**
     * 特价团购
     */
    COST_EFFECTIVE("cost_effective", "COST_EFFECTIVE"),

    /**
     *  直播场景进入团详页路径标识
     */
    LIVE_STREAM("mlive", "LIVE_STREAM"),
    /**
     * 从小视界进入团详页路径标识
     */
    MINI_VISION("mini_vision", "MINI_VISION"),
    /**
     * 上单预览渠道，开店宝、Apollo访问
     */
    CREATE_ORDER_PREVIEW("create_order_preview", "CREATE_ORDER_PREVIEW"),
    /**
     * 从商家详情页进入团详页路径标识
     */
    POI_PAGE("poi_page", "POI_PAGE"),

    M_VIDEO("mvideo", "M_VIDEO"),

    /**
     * 营销入口美甲款式路径标识
     */
    CUBE_EXHIBIT_NAIL("cube_exhibit_nail", "CUBE_EXHIBIT_NAIL"),
    /**
     * 从款式组件进入团详
     */
    STYLE("style", "STYLE"),
    /**
     * 新客活动
     */
    NEW_CUSTOMER_ACTIVITY("new_customer_activity", "NEW_CUSTOMER_ACTIVITY"),
    /**
     * 丽人常买清单
     */
    BEAUTY_BUYING_LIST("beauty_buyinglist", "BEAUTY_BUYING_LIST"),
    /**
     * 预订团单渠道标识
     */
    PRE_ORDER_DEAL("pre_order_deal", "PRE_ORDER_DEAL"),

    /**
     * 开店宝Web端预览
     */
    MERCHANT_PREVIEW("merchant_preview", "MERCHANT_PREVIEW"),

    /**
     * 交易快照
     */
    TRADE_SNAPSHOT("trade_snapshot", "TRADE_SNAPSHOT"),
    /**
     * 保洁自营自建货架
     */
    SELF_OPERATED_CLEANING_POI_PAGE("self_operated_cleaning_poi_page", "SELF_OPERATED_CLEANING_POI_PAGE"),
    /**
     * 保洁自营全切落地页
     */
    SELF_OPERATED_CLEANING_PAGE("self_operated_cleaning_page", "SELF_OPERATED_CLEANING_PAGE")
    ;

    private String source;

    private String scene;

    RequestSourceEnum(String source) {
        this.source = source;
    }

    RequestSourceEnum(String source, String scene) {
        this.source = source;
        this.scene = scene;
    }

    /**
     * 是否来源直播场景
     * @param pageSource 页面来源
     * @return 是否来源直播场景
     */
    public static boolean fromLive(String pageSource) {
        return LIVE_STREAM.getSource().equals(pageSource);
    }

    /**
     * 是否来源小视界场景
     * @param pageSource 页面来源
     * @return 是否来源小视界
     */
    public static boolean fromMiniVision(String pageSource) {
        return MINI_VISION.getSource().equals(pageSource);
    }

    /**
     * 是否来源Deal页浮层视频场景
     * @param pageSource
     * @return
     */
    public static boolean fromMVideo(String pageSource) {
        return M_VIDEO.getSource().equals(pageSource);
    }

    /**
     * 是否来源丽人常买清单
     * @param pageSource 页面来源
     * @return 是否来源丽人常买清单
     */
    public static boolean fromBeautyBuyingList(String pageSource) {
        return BEAUTY_BUYING_LIST.getSource().equals(pageSource);
    }


    public static boolean needDyeAndReport(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }

        return ODP.source.equals(source) || COST_EFFECTIVE.source.equals(source);
    }

    public static RequestSourceEnum fromDyeTraceScene(String dyeTraceScene) {
        for (RequestSourceEnum requestSourceEnum : values()) {
            if (Objects.equals(requestSourceEnum.getScene(), dyeTraceScene)) {
                return requestSourceEnum;
            }
        }
        return null;
    }

    public static boolean fromCaiXi(String pageSource) {
        return CAI_XI.getSource().equals(pageSource) || HOME_PAGE.getSource().equals(pageSource);
    }

    public static boolean fromMerchantPreview(String pageSource) {
        return MERCHANT_PREVIEW.getSource().equals(pageSource);
    }

    public static boolean fromTradeSnapshot(String pageSource) {
        return TRADE_SNAPSHOT.getSource().equals(pageSource);
    }

    /**
     * 保洁自营全切落地页
     * @param pageSource 页面来源
     * @return 是否来源保洁自营全切落地页
     */
    public static boolean fromSelfOperatedCleaningPage(String pageSource) {
        return SELF_OPERATED_CLEANING_PAGE.getSource().equals(pageSource);
    }

    /**
     * 保洁自营自建货架
     * @param pageSource 页面来源
     * @return 是否来源保洁自营自建货架
     */
    public static boolean fromSelfOperatedCleaningPoiPage(String pageSource) {
        return SELF_OPERATED_CLEANING_POI_PAGE.getSource().equals(pageSource);
    }

    /**
     * 判断是否来自直播
     * @param source 请求来源
     * @return true表示来自直播，false表示不是来自直播
     */
    public static boolean fromMLive(String source) {
        return Objects.equals(source, LIVE_STREAM.getSource());
    }
}
