package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:38
 */
@Data
@TypeDoc(description = "新版预订详情页（折扣卡模块-详情文本）")
@MobileDo(id = 0x2aa1)
public class DiscountCardContents implements Serializable {

    @FieldDoc(description = "内容文案")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    @FieldDoc(description = "字体颜色")
    @MobileDo.MobileField(key = 0x2efe)
    private String fontColor;

    @FieldDoc(description = "字体大小")
    @MobileDo.MobileField(key = 0xb53a)
    private int fontSize;
}
