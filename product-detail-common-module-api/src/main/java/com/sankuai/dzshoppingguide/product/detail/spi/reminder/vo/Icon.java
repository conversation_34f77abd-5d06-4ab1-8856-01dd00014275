package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0xb0bb)
public class Icon implements Serializable {
    /**
    * 图标高度
    */
    @MobileDo.MobileField(key = 0x75c3)
    private int iconHeight;

    /**
    * 图标宽度
    */
    @MobileDo.MobileField(key = 0x4864)
    private int iconWidth;

    /**
    * icon链接
    */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    public int getIconHeight() {
        return iconHeight;
    }

    public void setIconHeight(int iconHeight) {
        this.iconHeight = iconHeight;
    }

    public int getIconWidth() {
        return iconWidth;
    }

    public void setIconWidth(int iconWidth) {
        this.iconWidth = iconWidth;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}