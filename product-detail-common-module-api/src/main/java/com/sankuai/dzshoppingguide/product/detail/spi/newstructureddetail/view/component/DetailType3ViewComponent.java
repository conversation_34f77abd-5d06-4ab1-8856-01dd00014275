package com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.view.component;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: caisiyuan03
 * @Date: 2025/5/15 10:51
 * @Description: TODO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class DetailType3ViewComponent extends BaseViewComponent {

    public final String type = ViewComponentTypeEnum.DETAIL_TYPE_3.name();
    private String title;
    private int order;
    private String content;
    private String subContent;
    private String detail;
}
