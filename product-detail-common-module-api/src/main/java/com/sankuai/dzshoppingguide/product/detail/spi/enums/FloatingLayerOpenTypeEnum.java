package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-03-20
 * @desc 浮层打开类型
 */
@Getter
public enum FloatingLayerOpenTypeEnum {
    OPEN_LAYER(1, "打开浮层"),
    JUMP_TO_DETAIL(2, "跳转到详情页");

    private final int code;
    private final String desc;

    FloatingLayerOpenTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
