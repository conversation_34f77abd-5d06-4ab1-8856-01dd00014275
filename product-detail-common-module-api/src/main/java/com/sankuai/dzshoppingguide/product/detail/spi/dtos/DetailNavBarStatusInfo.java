package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhen<PERSON><PERSON><EMAIL>
 * @Date: 2025/2/11
 */
@Data
@TypeDoc(description = "详情页收藏模块状态信息")
@MobileDo(id = 0xa6f4)
public class DetailNavBarStatusInfo implements Serializable {
    @FieldDoc(description = "收藏状态，0-为收藏，1-已收藏")
    @MobileDo.MobileField(key = 0xb409)
    private int favorStatus = 0;
}
