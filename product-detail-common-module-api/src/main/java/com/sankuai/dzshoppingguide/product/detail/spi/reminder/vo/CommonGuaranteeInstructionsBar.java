package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @see <a href="https://mobile.sankuai.com/studio/model/info/41243">保障须知条通用模型</a>
 */
@TypeDoc(description = "保障须知条通用模型")
@EqualsAndHashCode(callSuper = true)
@MobileDo(id = 0xf1b4)
@Data
public class CommonGuaranteeInstructionsBar extends AbstractModuleVO {

    @Override
    public String getModuleKey() {
        return "";
    }

    @FieldDoc(description = "须知条文案详情-特殊样式")
    @MobileDo.MobileField(key = 0x6474)
    private List<GuaranteeInstructionsContentVO> specialContents;

    @FieldDoc(description = "须知条文案详情-普通样式")
    @MobileDo.MobileField(key = 0x8535)
    private List<GuaranteeInstructionsContentVO> contents;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}