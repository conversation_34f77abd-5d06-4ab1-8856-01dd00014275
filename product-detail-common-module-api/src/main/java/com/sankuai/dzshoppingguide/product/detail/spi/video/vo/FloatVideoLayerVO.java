package com.sankuai.dzshoppingguide.product.detail.spi.video.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "视频浮层")
@MobileDo(id = 0xba26)
public class FloatVideoLayerVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.FLOAT_VIDEO_LAYER;
    }
}
