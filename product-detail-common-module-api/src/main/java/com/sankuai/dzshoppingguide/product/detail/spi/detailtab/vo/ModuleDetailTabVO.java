package com.sankuai.dzshoppingguide.product.detail.spi.detailtab.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/5 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "tab模块")
@MobileDo(id = 0xf74a)
public class ModuleDetailTabVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MODULE_DETAIL_TAB;
    }
}
