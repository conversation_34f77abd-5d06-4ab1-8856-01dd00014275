package com.sankuai.dzshoppingguide.product.detail.spi.tag.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 11:39
 */
@Data
@TypeDoc(description = "榜单标签模型")
@MobileDo(id = 0xee1f)
public class RankTagVO extends BaseTagDTO {

    /**
     * 榜单类型，1-业务自建，2-点评平台榜单
     */
    @MobileDo.MobileField(key = 0x56c5)
    private int rankType;

    /**
     * 排名
     */
    @MobileDo.MobileField(key = 0xf67a)
    private int ranking;


    /**
     * 榜单场景
     */
    @MobileDo.MobileField(key = 0x4ecf)
    private String rankScene;


    /**
     * 分流实验结果
     */
    @MobileDo.MobileField(key = 0x4762)
    private List<AbInfo> abInfo;
}
