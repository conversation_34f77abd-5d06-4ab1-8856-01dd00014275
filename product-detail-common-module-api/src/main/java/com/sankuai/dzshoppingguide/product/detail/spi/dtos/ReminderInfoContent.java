package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2025/1/26 16:20
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41242
 */
@Data
@TypeDoc(description = "须知条详情")
@MobileDo(id = 0x6dba)
public class ReminderInfoContent implements Serializable {
    @FieldDoc(description = "字体颜色")
    @MobileDo.MobileField(key = 0x2efe)
    private String fontColor;

    @FieldDoc(description = "字体大小")
    @MobileDo.MobileField(key = 0xb53a)
    private String fontSize;

    @FieldDoc(description = "文本内容")
    @MobileDo.MobileField(key = 0x451b)
    private int text;

    @FieldDoc(description = "后缀Icon")
    @MobileDo.MobileField(key = 0x26c8)
    private Icon suffixIcon;

    @FieldDoc(description = "前缀Icon")
    @MobileDo.MobileField(key = 0x7a3c)
    private Icon prefixIcon;
}
