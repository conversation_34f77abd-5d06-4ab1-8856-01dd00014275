package com.sankuai.dzshoppingguide.product.detail.spi.detailimage;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@TypeDoc(description = "新版预订详情页（图文详情模块）")
@MobileDo(id = 0xb758)
@EqualsAndHashCode(callSuper = true)
public class ImageTextDetailVO extends AbstractModuleVO {

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.DETAIL_IMAGE;
    }

    @FieldDoc(description = "图文详情列表")
    @MobileDo.MobileField(key = 0x8535)
    private List<ImageTextDetailContent> contents;

    @FieldDoc(description = "折叠大小")
    @MobileDo.MobileField(key = 0x5fd0)
    private int foldThreshold;

    @FieldDoc(description = "是否折叠")
    @MobileDo.MobileField(key = 0xc31)
    private boolean fold;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;


}