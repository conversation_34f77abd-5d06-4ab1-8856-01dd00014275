package com.sankuai.dzshoppingguide.product.detail.spi.corpwxflowbanner.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 16:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "团详企业微信模块")
@MobileDo(id = 0x7098)
public class CorpWxFlowBannerVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.WX_CORP_BANNER;
    }

    /**
     * 副标题文案
     */
    @MobileDo.MobileField(key = 0xa4d0)
    private String subTitleText;

    /**
     * 展示文案
     */
    @MobileDo.MobileField(key = 0x2607)
    private String showText;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 按钮文案
     */
    @MobileDo.MobileField(key = 0xe221)
    private String buttonText;

    /**
     * 图片链接
     */
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    /**
     * 是否展示资源信息
     */
    @MobileDo.MobileField(key = 0x23f7)
    private boolean isShowResource;
}
