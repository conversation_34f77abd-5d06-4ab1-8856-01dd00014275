package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhen<PERSON><PERSON><EMAIL>
 * @Date: 2025/2/8
 */
@Data
@TypeDoc(description = "商品详情页顶部导航栏项配置")
@MobileDo(id = 0xc167)
public class NavBarItemConf implements Serializable {
    @FieldDoc(description = "导航栏项类型，如1-选中，2-未选中")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "导航项icon")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "导航图标名称")
    @MobileDo.MobileField(key = 0x451b)
    private String text;
}
