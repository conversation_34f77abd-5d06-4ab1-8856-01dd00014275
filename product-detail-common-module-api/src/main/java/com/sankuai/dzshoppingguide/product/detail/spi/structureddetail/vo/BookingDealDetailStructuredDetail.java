package com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.RichTextLableVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 新版预订详情页（结构化团详模块）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "结构化团详模块")
@MobileDo(id = 0xcaae)
public class BookingDealDetailStructuredDetail extends AbstractModuleVO implements Serializable {
    /**
    * 标题字体加粗
    */
    @MobileField(key = 0xc2e5)
    private String titleFontWeight;

    /**
    * 弹窗图标
    */
    @MobileField(key = 0x3005)
    private String popupIcon;

    /**
    * 子标题
    */
    @MobileField(key = 0xd894)
    private String subTitle;

    /**
    * 内容
    */
    @MobileField(key = 0xcce)
    private String content;

    /**
    * 标题
    */
    @MobileField(key = 0x24cc)
    private String title;

    /**
    * 副内容
    */
    @MobileField(key = 0x667e)
    private String subContent;

    /**
    * 详情
    */
    @MobileField(key = 0xa83b)
    private String detail;

    /**
    * 单位文本，份数等，例如：1份
    */
    @MobileField(key = 0xd9b2)
    private String unit;

    /**
    * 时长文本
    */
    @MobileField(key = 0x9042)
    private RichTextLableVO durationText;

    /**
    * 价格文本
    */
    @MobileField(key = 0x1f5d)
    private RichTextLableVO priceText;

    /**
    * 单位文本，份数等，例如：1份
    */
    @MobileField(key = 0xde5f)
    private RichTextLableVO unitText;

    /**
    * 标题，可定制文本
    */
    @MobileField(key = 0x6f17)
    private RichTextLableVO titleText;

    /**
    * 副内容，可定制化文本
    */
    @MobileField(key = 0x3c5a)
    private RichTextLableVO subContentText;

    /**
    * 详情，可定制文本
    */
    @MobileField(key = 0x8325)
    private RichTextLableVO detailText;

    /**
    * 内容，可定制文本
    */
    @MobileField(key = 0xe188)
    private RichTextLableVO contentText;

    /**
    * 行数据标识
    */
    @MobileField(key = 0xb231)
    private String itemId;

    /**
    * content颜色
    */
    @MobileField(key = 0xde6d)
    private String contentColor;

    /**
    * 信息前缀 https://mobile.sankuai.com/studio/model/edit/41235
    */
    @MobileField(key = 0x7706)
    private int prefix;

    /**
    * 跳转链接
    */
    @MobileField(key = 0x774e)
    private String jumpUrl;

    /**
    * 弹窗浮层数据
    */
    @MobileField(key = 0x4548)
    private String popupData;

    /**
    * 渐变背景色-终色
    */
    @MobileField(key = 0x90aa)
    private String endBackgroundColor;

    /**
    * 序号
    */
    @MobileField(key = 0x811f)
    private int order;

    /**
    * 图标地址
    */
    @MobileField(key = 0x3c48)
    private String icon;

    /**
    * 背景颜色
    */
    @MobileField(key = 0xba62)
    private String backgroundColor;

    /**
    * 对应前端展示类型，目前1-5
    */
    @MobileField(key = 0x8f0c)
    private int type;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.STRUCTURED_DEAL_DETAILS;
    }

}