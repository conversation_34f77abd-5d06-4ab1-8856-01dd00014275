package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2025/1/26 16:55
 * 移动之家： https://mobile.sankuai.com/studio/model/info/41228
 */
@Data
@TypeDoc(description = "Icon通用模型")
@MobileDo(id = 0xb0bb)
@NoArgsConstructor
public class Icon implements Serializable {

    @FieldDoc(description = "icon链接")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "图标宽度")
    @MobileDo.MobileField(key = 0x4864)
    private int iconWidth;

    @FieldDoc(description = "图标高度")
    @MobileDo.MobileField(key = 0x75c3)
    private int iconHeight;

    @FieldDoc(description = "文本内容")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    public Icon(String icon, String text) {
        this.icon = icon;
        this.text = text;
    }
}
