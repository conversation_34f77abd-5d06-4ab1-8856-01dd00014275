package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:13
 */
@Data
@TypeDoc(description = "图片尺寸")
@MobileDo(id = 0x9764)
@Builder
public class ImagesSize implements Serializable {
    /**
     * 图片宽度
     */
    @FieldDoc(description = "图片宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private int width;

    /**
     *
     */
    @FieldDoc(description = "图片高度")
    @MobileDo.MobileField(key = 0x261f)
    private int height;
}
