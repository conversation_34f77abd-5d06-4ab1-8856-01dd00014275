package com.sankuai.dzshoppingguide.product.detail.spi.limit.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "限制条模型")
@MobileDo(id = 0xe787)
@Data
@Builder
public class LimitContent implements Serializable {
    /**
     * 样式
     */
    @MobileDo.MobileField(key = 0x1b3a)
    private String style;

    /**
     * 类型
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * icon高度
     */
    @MobileDo.MobileField(key = 0x75c3)
    private int iconHeight;

    /**
     * icon宽度
     */
    @MobileDo.MobileField(key = 0x4864)
    private int iconWidth;

    /**
     * icon链接
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 限制描述
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;
}