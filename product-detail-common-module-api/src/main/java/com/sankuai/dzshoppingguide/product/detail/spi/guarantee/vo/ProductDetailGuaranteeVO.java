package com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FloatingLayerOpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.CommonGuaranteeInstructionsBar;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-11
 * @desc 商品详情页保障模块
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDetailGuaranteeVO extends CommonGuaranteeInstructionsBar {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.GUARANTEE_INFO;
    }

    public ProductDetailGuaranteeVO() {

    }

    public ProductDetailGuaranteeVO(String title, List<GuaranteeInstructionsContentVO> contents, FeaturesLayer floatingLayer, FloatingLayerOpenTypeEnum openTypeEnum) {
        this.setTitle(title);
        this.setContents(contents);
        this.setFloatingLayer(floatingLayer);
        this.setFloatingLayerOpenType(openTypeEnum.getCode());
    }

    @FieldDoc(description = "浮层打开类型，1-打开浮层，2-跳转到详情页")
    @MobileDo.MobileField(key = 0x7498)
    private int floatingLayerOpenType;

    @FieldDoc(description = "浮层信息")
    @MobileDo.MobileField(key = 0x6f65)
    private FeaturesLayer floatingLayer;
}
