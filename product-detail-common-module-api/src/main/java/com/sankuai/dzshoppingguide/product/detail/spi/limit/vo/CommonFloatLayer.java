package com.sankuai.dzshoppingguide.product.detail.spi.limit.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0xbd35)
@Data
public class CommonFloatLayer implements Serializable {
    /**
    * 小程序跳链
    */
    @MobileDo.MobileField(key = 0xf6a0)
    private String miniJumpUrl;

    /**
    * 不同样式对应的值，比如高亮下发对应颜色值
    */
    @MobileDo.MobileField(key = 0xddfb)
    private String textStyle;

    /**
    * 信息展示类型，0-纯文本，1-高亮
    */
    @MobileDo.MobileField(key = 0x2e2a)
    private int textType;

    /**
    * 价保类型 1-退改协议，2-价保
    */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
    * 价保描述
    */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
    * 价保跳链
    */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
    * 价保标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    * 价保标签
    */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;
}