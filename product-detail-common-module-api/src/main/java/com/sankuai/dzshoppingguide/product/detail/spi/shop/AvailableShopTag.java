package com.sankuai.dzshoppingguide.product.detail.spi.shop;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0xe316)
public class AvailableShopTag extends AbstractModuleVO implements Serializable {
    /**
     * 文字颜色
     */
    @MobileField(key = 0xb53a)
    private String fontSize;

    /**
     * 文字颜色
     */
    @MobileField(key = 0x2efe)
    private String fontColor;

    /**
     * 右侧图标
     */
    @MobileField(key = 0x3c48)
    private Icon icon;

    /**
     * 跳转链接
     */
    @MobileField(key = 0xe998)
    private String linkUrl;

    /**
     * 标签头
     */
    @MobileField(key = 0xa648)
    private String tagHeader;

    /**
     * 标签文案
     */
    @MobileField(key = 0xb84b)
    private String tagText;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.SHOP_TAG;
    }
}
