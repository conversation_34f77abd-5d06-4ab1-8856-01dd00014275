package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @see <a href="https://mobile.sankuai.com/studio/model/info/41242">商品保障须知条内容</a>
 */
@TypeDoc(description = "商品保障须知条内容")
@Data
@MobileDo(id = 0x6dba)
@NoArgsConstructor
public class GuaranteeInstructionsContentVO implements Serializable {

    public GuaranteeInstructionsContentVO(String text) {
        this.text = text;
    }

    public GuaranteeInstructionsContentVO(String text, String fontSize, String type) {
        this.text = text;
        this.fontSize = fontSize;
        this.type = type;
    }

    @FieldDoc(description = "前缀Icon")
    @MobileDo.MobileField(key = 0x7a3c)
    private Icon prefixIcon;

    @FieldDoc(description = "后缀Icon")
    @MobileDo.MobileField(key = 0x26c8)
    private Icon suffixIcon;

    @FieldDoc(description = "字体颜色")
    @MobileDo.MobileField(key = 0x2efe)
    private String fontColor;

    @FieldDoc(description = "字体大小")
    @MobileDo.MobileField(key = 0xb53a)
    private String fontSize;

    @FieldDoc(description = "文本内容")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "0: 普通样式 1: 特殊样式(不可用日期)")
    @MobileDo.MobileField(key = 0x1b3a)
    private int style;

    @FieldDoc(description = "类型")
    @MobileDo.MobileField(key = 0x8f0c)
    private String type;
}