package com.sankuai.dzshoppingguide.product.detail.spi.discountcard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DiscountCardContents;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:31
 * 移动之家 https://mobile.sankuai.com/studio/model/info/41251
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "新版预订详情页(折扣卡模块)")
@MobileDo(id = 0x3b9b)
public abstract class BasicCardModuleVO extends AbstractModuleVO {

    /**
     * 会员卡类型
     * @see com.sankuai.dzshoppingguide.product.detail.application.enums.CardTypeEnum
     */
    @FieldDoc(description = "会员卡类型")
    @MobileDo.MobileField(key = 0x89e8)
    private int cardType;

    @FieldDoc(description = "文本内容列表")
    @MobileDo.MobileField(key = 0x8535)
    private List<DiscountCardContents> contents;

    @FieldDoc(description = "跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "背景颜色")
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    @FieldDoc(description = "后缀Icon")
    @MobileDo.MobileField(key = 0x26c8)
    private String suffixIcon;

    @FieldDoc(description = "前缀Icon")
    @MobileDo.MobileField(key = 0x7a3c)
    private String prefixIcon;

    @FieldDoc(description = "是否展示开卡条")
    @MobileDo.MobileField(key = 0xeef)
    private boolean isShow;
}
