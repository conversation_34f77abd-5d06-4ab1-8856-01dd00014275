<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.2</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.dzshoppingguide.detail</groupId>
    <artifactId>product-detail-common-module</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>product-detail-common-module</name>

    <modules>
        <module>product-detail-common-module-api</module>
        <module>product-detail-common-module-starter</module>
        <module>product-detail-common-module-application</module>
        <module>product-detail-common-module-domain</module>
        <module>product-detail-common-module-infrastructure</module>
        <module>product-detail-page-url-api</module>
        <module>product-detail-page-url-sdk</module>
    </modules>

    <properties>
        <revision>1.0.0</revision>
        <product-detail-page-url.version>1.0.0</product-detail-page-url.version>
        <product-detail-common-module-api.version>1.0.0</product-detail-common-module-api.version>
        <product-detail-gateway-api.version>1.0.5</product-detail-gateway-api.version>
        <product-detail-low-code-framework.version>1.0.0-SNAPSHOT</product-detail-low-code-framework.version>
        <product-detail-module-arrange-framework.version>1.0.5</product-detail-module-arrange-framework.version>
        <product-detail-common.version>1.0.5</product-detail-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dp.arts</groupId>
                <artifactId>arts-client</artifactId>
                <version>ES_0.4.22</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-page-url-api</artifactId>
                <version>${product-detail-page-url.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-common-module-api</artifactId>
                <version>${product-detail-common-module-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-common</artifactId>
                <version>${product-detail-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-api</artifactId>
                <version>${product-detail-gateway-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-low-code-framework</artifactId>
                <version>${product-detail-low-code-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-module-arrange-framework</artifactId>
                <version>${product-detail-module-arrange-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-common-module-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-common-module-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-common-module-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-common-module-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>4.1.117.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
