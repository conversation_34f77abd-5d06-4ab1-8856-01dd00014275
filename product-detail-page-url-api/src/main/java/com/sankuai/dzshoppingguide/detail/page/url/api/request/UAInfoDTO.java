package com.sankuai.dzshoppingguide.detail.page.url.api.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 14:40
 */
@Data
public class UAInfoDTO implements Serializable {

    @FieldDoc(description = "用户ID", rule = "区分平台",
            requiredness = Requiredness.OPTIONAL)
    private long userId;

    @FieldDoc(description = "对应请求头中的pragma-unionid字段",
            requiredness = Requiredness.OPTIONAL)
    private String unionId;

    @FieldDoc(description = "App 版本号(如果是非标准版本号,则返回空字符串)",
            requiredness = Requiredness.OPTIONAL)
    private String appVersion;

}