package com.sankuai.dzshoppingguide.detail.page.url.api.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 14:34
 */
@TypeDoc(description = "跳链查询请求体")
@Data
public class ProductDetailPageUrlQueryRequest implements Serializable {

    @FieldDoc(description = "ua信息",
            rule = "用户与设备相关信息，尽量传，不然会影响实验结果",
            requiredness = Requiredness.REQUIRED)
    private UAInfoDTO uaInfo;

    @FieldDoc(description = "商品id",
            rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分",
            requiredness = Requiredness.REQUIRED)
    private long productId;

    @FieldDoc(description = "skuId",
            rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分",
            requiredness = Requiredness.OPTIONAL)
    private long skuId;

    @FieldDoc(description = "商品类型，具体看枚举",
            type = ProductTypeEnum.class,
            requiredness = Requiredness.REQUIRED
    )
    protected int productType;

    @FieldDoc(description = "商品分类，如果有可以传，不然内部会调用缓存or远程服务查询，有额外请求耗时",
            rule = "团购传团购二级分类，泛商品传spuType，不确定怎么传就不要传，内部会查",
            requiredness = Requiredness.OPTIONAL)
    private int productSecondCategoryId;

    @FieldDoc(description = "客户端类型，具体看枚举",
            type = ClientTypeEnum.class,
            requiredness = Requiredness.REQUIRED)
    private int clientType;

    @FieldDoc(description = "门店id", rule = "区分平台",
            requiredness = Requiredness.OPTIONAL)
    protected long poiId;

    @FieldDoc(description = "渠道",
            requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2418267775，暂时用文档维护，后续会用系统维护")
    protected String pageSource;

    @FieldDoc(description = "定制参数集合",
            requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2418267775，暂时用文档维护，后续会用系统维护")
    private final Map<String, String> customParams = new HashMap<>();

    public void checkParams() {
        if (productId <= 0) {
            throw new IllegalArgumentException("非法productId:" + productId);
        }
        if (!ProductTypeEnum.containsCode(productType)) {
            throw new IllegalArgumentException("非法productType:" + productType);
        }
        if (ClientTypeEnum.containsCode(clientType)) {
            throw new IllegalArgumentException("非法clientType:" + clientType);
        }
    }

}
